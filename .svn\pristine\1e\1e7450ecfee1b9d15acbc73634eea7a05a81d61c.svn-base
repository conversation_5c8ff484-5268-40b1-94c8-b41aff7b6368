/*
 * File: LaunchCtrl.h
 *
 * Code generated for Simulink model 'LaunchCtrl'.
 *
 * Model version                  : 1.304
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu Jun 12 15:18:56 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Not run
 */

#ifndef RTW_HEADER_LaunchCtrl_h_
#define RTW_HEADER_LaunchCtrl_h_
#include <string.h>
#ifndef LaunchCtrl_COMMON_INCLUDES_
# define LaunchCtrl_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "mathlib.h"
#endif                                 /* LaunchCtrl_COMMON_INCLUDES_ */

#include "LaunchCtrl_types.h"

/* Includes for objects with custom storage classes. */
#include "launchctrl_out.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKLCCMETRG_dim                 6U                        /* Referenced by:
                                                                  * '<S55>/BKLCCMETRG_dim'
                                                                  * '<S56>/BKLCCMETRG_dim'
                                                                  * '<S80>/BKLCCMETRG_dim'
                                                                  * '<S81>/BKLCCMETRG_dim'
                                                                  */

/* dim */
#define BKLCIDXCTF_dim                 3U                        /* Referenced by: '<S12>/BKLCIDXCTF_dim' */

/* dim */
#define BKRPMLCTRGWOT_dim              4U                        /* Referenced by:
                                                                  * '<S13>/BKRPMLCTRGWOT_dim'
                                                                  * '<S17>/BKRPMLCTRGWOT_dim'
                                                                  */

/* dim */
#define ID_LAUNCH_CTRL                 21860712U                 /* Referenced by: '<S2>/ID_LAUNCH_CTRL' */

/* mask */

/* Block signals and states (default storage) for system '<Root>' */
typedef struct {
  int32_T offLcKm;                     /* '<S45>/Enable_Lc_Km' */
  int32_T tmpOdometer;                 /* '<S45>/Enable_Lc_Km' */
  uint8_T flgRetTO;                    /* '<S4>/Chart_LaunchCtrl' */
  uint8_T enLcKm;                      /* '<S45>/Enable_Lc_Km' */
  uint8_T is_active_c3_LaunchCtrl;     /* '<S4>/Chart_LaunchCtrl' */
  uint8_T is_LC_COND_CTRL;             /* '<S4>/Chart_LaunchCtrl' */
  uint8_T is_LC_OVERRIDE_CTRL;         /* '<S4>/Chart_LaunchCtrl' */
  uint8_T cntWAMsg;                    /* '<S4>/Chart_LaunchCtrl' */
  uint8_T cnt;                         /* '<S4>/Chart_LaunchCtrl' */
  uint8_T is_active_c1_LaunchCtrl;     /* '<S45>/Enable_Lc_Km' */
  uint8_T is_c1_LaunchCtrl;            /* '<S45>/Enable_Lc_Km' */
  boolean_T Relay_Mode;                /* '<S14>/Relay' */
} D_Work_LaunchCtrl_T;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState trig_to_fc3_Trig_ZCE;     /* '<S1>/trig_to_fc3' */
  ZCSigState trig_to_fc2_Trig_ZCE;     /* '<S1>/trig_to_fc2' */
  ZCSigState trig_to_fc1_Trig_ZCE;     /* '<S1>/trig_to_fc1' */
} PrevZCSigStates_LaunchCtrl_T;

/* External inputs (root inport signals with default storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_T10ms;                    /* '<Root>/ev_T10ms' */
  uint8_T ev_PreTDC;                   /* '<Root>/ev_PreTDC' */
  int16_T CmiDriverP_p4a;              /* '<Root>/CmiDriverP' */
  int16_T CmiDriverI_lto;              /* '<Root>/CmiDriverI' */
  uint16_T Rpm_m33;                    /* '<Root>/Rpm' */
  uint8_T LcLevelCAN_hc3;              /* '<Root>/LcLevelCAN' */
  uint8_T LcToExitCAN_non;             /* '<Root>/LcToExitCAN' */
  uint8_T AwLevelCAN_knf;              /* '<Root>/AwLevelCAN' */
  int16_T AxCAN_imv;                   /* '<Root>/AxCAN' */
  uint8_T SetTracCtrlCAN_pm0;          /* '<Root>/SetTracCtrlCAN' */
  uint8_T FlgYawRec_avt;               /* '<Root>/FlgYawRec' */
  uint8_T StDiag_hib[80];              /* '<Root>/StDiag' */
  uint8_T GearPos_bze;                 /* '<Root>/GearPos' */
  uint16_T VehSpeedFront_mjd;          /* '<Root>/VehSpeedFront' */
  int32_T VehRbVfAxInt_hyu;            /* '<Root>/VehRbVfAxInt' */
  uint8_T FlgSpringUp_apb;             /* '<Root>/FlgSpringUp' */
  uint16_T GasPosCC_nbq;               /* '<Root>/GasPosCC' */
  uint8_T ClutchSignal_ps5;            /* '<Root>/ClutchSignal' */
  uint8_T EndStartFlg_cdu;             /* '<Root>/EndStartFlg' */
  uint8_T RidingMode_jw4;              /* '<Root>/RidingMode' */
  int16_T CmiTargetP_gqa;              /* '<Root>/CmiTargetP' */
  int16_T CmiTargetI_kfl;              /* '<Root>/CmiTargetI' */
  uint8_T StSatAw_dio;                 /* '<Root>/StSatAw' */
  int16_T TWater_mf3;                  /* '<Root>/TWater' */
  uint32_T Odometer_lae;               /* '<Root>/Odometer' */
  int16_T CmeDriverP_hcq;              /* '<Root>/CmeDriverP' */
  uint8_T TcDiagRec_a0d;               /* '<Root>/TcDiagRec' */
  uint8_T VehSpeedCtrlCAN_fee;         /* '<Root>/VehSpeedCtrlCAN' */
} ExternalInputs_LaunchCtrl_T;

/* Block signals and states (default storage) */
extern D_Work_LaunchCtrl_T LaunchCtrl_DWork;

/* External inputs (root inport signals with default storage) */
extern ExternalInputs_LaunchCtrl_T LaunchCtrl_U;

/* Model entry point functions */
extern void LaunchCtrl_initialize(void);
extern void LaunchCtrl_step(void);

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S16>/Data Type Duplicate' : Unused code path elimination
 * Block '<S15>/Data Type Duplicate' : Unused code path elimination
 * Block '<S23>/Data Type Duplicate' : Unused code path elimination
 * Block '<S24>/Data Type Duplicate' : Unused code path elimination
 * Block '<S25>/Data Type Duplicate' : Unused code path elimination
 * Block '<S21>/Data Type Duplicate' : Unused code path elimination
 * Block '<S61>/Data Type Duplicate' : Unused code path elimination
 * Block '<S62>/Data Type Duplicate' : Unused code path elimination
 * Block '<S63>/Data Type Duplicate' : Unused code path elimination
 * Block '<S64>/Data Type Duplicate' : Unused code path elimination
 * Block '<S60>/Data Type Duplicate' : Unused code path elimination
 * Block '<S66>/Data Type Duplicate' : Unused code path elimination
 * Block '<S71>/Data Type Duplicate' : Unused code path elimination
 * Block '<S69>/Data Type Duplicate' : Unused code path elimination
 * Block '<S72>/Data Type Duplicate' : Unused code path elimination
 * Block '<S70>/Data Type Duplicate' : Unused code path elimination
 * Block '<S77>/Data Type Duplicate' : Unused code path elimination
 * Block '<S75>/Data Type Duplicate' : Unused code path elimination
 * Block '<S78>/Data Type Duplicate' : Unused code path elimination
 * Block '<S76>/Data Type Duplicate' : Unused code path elimination
 * Block '<S85>/Data Type Duplicate' : Unused code path elimination
 * Block '<S86>/Data Type Duplicate' : Unused code path elimination
 * Block '<S87>/Data Type Duplicate' : Unused code path elimination
 * Block '<S89>/Data Type Duplicate' : Unused code path elimination
 * Block '<Root>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<Root>/Data Type Conversion14' : Eliminate redundant data type conversion
 * Block '<Root>/Data Type Conversion20' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S23>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S23>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S23>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion' : Eliminate redundant data type conversion
 * Block '<S57>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S57>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S57>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S61>/Conversion' : Eliminate redundant data type conversion
 * Block '<S58>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S58>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S58>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S62>/Conversion' : Eliminate redundant data type conversion
 * Block '<S59>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S59>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S60>/Conversion' : Eliminate redundant data type conversion
 * Block '<S60>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S60>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S60>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S64>/Conversion' : Eliminate redundant data type conversion
 * Block '<S66>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S66>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S66>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S69>/Conversion' : Eliminate redundant data type conversion
 * Block '<S69>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S69>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S69>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S71>/Conversion' : Eliminate redundant data type conversion
 * Block '<S70>/Conversion' : Eliminate redundant data type conversion
 * Block '<S70>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S70>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S70>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S72>/Conversion' : Eliminate redundant data type conversion
 * Block '<S75>/Conversion' : Eliminate redundant data type conversion
 * Block '<S75>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S75>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S75>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S77>/Conversion' : Eliminate redundant data type conversion
 * Block '<S76>/Conversion' : Eliminate redundant data type conversion
 * Block '<S76>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S76>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S76>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S78>/Conversion' : Eliminate redundant data type conversion
 * Block '<S82>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S82>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S82>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S85>/Conversion' : Eliminate redundant data type conversion
 * Block '<S83>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S83>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S83>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S86>/Conversion' : Eliminate redundant data type conversion
 * Block '<S84>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S84>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S89>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S89>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S89>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S26>/Selector' : Unused code path elimination
 * Block '<S26>/ZERO' : Unused code path elimination
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'LaunchCtrl'
 * '<S1>'   : 'LaunchCtrl/LaunchCtrl'
 * '<S2>'   : 'LaunchCtrl/LaunchCtrl/Init'
 * '<S3>'   : 'LaunchCtrl/LaunchCtrl/PreTDC'
 * '<S4>'   : 'LaunchCtrl/LaunchCtrl/T10ms'
 * '<S5>'   : 'LaunchCtrl/LaunchCtrl/trig_to_fc1'
 * '<S6>'   : 'LaunchCtrl/LaunchCtrl/trig_to_fc2'
 * '<S7>'   : 'LaunchCtrl/LaunchCtrl/trig_to_fc3'
 * '<S8>'   : 'LaunchCtrl/LaunchCtrl/Init/Init_Data'
 * '<S9>'   : 'LaunchCtrl/LaunchCtrl/Init/Init_Scheduler'
 * '<S10>'  : 'LaunchCtrl/LaunchCtrl/PreTDC/Calc_IdxLcCutOff'
 * '<S11>'  : 'LaunchCtrl/LaunchCtrl/PreTDC/Calc_IdxLcCutOff1'
 * '<S12>'  : 'LaunchCtrl/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_IdxLcCutOff'
 * '<S13>'  : 'LaunchCtrl/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_RpmTarget'
 * '<S14>'  : 'LaunchCtrl/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_IdxLcCutOff/Calc_flgHys'
 * '<S15>'  : 'LaunchCtrl/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_IdxLcCutOff/LookUp_S16_S16'
 * '<S16>'  : 'LaunchCtrl/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_IdxLcCutOff/LookUp_S16_S16/Data Type Conversion Inherited3'
 * '<S17>'  : 'LaunchCtrl/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_RpmTarget/Calc_Ratio'
 * '<S18>'  : 'LaunchCtrl/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_RpmTarget/LC_TO_IDLE'
 * '<S19>'  : 'LaunchCtrl/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_RpmTarget/LC_TO_LIM'
 * '<S20>'  : 'LaunchCtrl/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_RpmTarget/LookUp_IR_S16'
 * '<S21>'  : 'LaunchCtrl/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_RpmTarget/RateLimiter_S2'
 * '<S22>'  : 'LaunchCtrl/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_RpmTarget/Calc_Ratio/Filt_Speed'
 * '<S23>'  : 'LaunchCtrl/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_RpmTarget/Calc_Ratio/PreLookUpIdSearch_U16'
 * '<S24>'  : 'LaunchCtrl/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_RpmTarget/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S25>'  : 'LaunchCtrl/LaunchCtrl/PreTDC/Calc_IdxLcCutOff/Calc_RpmTarget/RateLimiter_S2/Data Type Conversion Inherited1'
 * '<S26>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Calc_Ctrl_Level'
 * '<S27>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Calc_Flag_Conditions'
 * '<S28>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Calc_LcToExit'
 * '<S29>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Chart_LaunchCtrl'
 * '<S30>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_CtfLim'
 * '<S31>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_Dis'
 * '<S32>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_Idle'
 * '<S33>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_Reduct'
 * '<S34>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_Ret'
 * '<S35>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_SatLim'
 * '<S36>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_To_Idle'
 * '<S37>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Calc_Ctrl_Level/Compare To Constant'
 * '<S38>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcDis'
 * '<S39>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcEn'
 * '<S40>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcEnd'
 * '<S41>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcLaunch'
 * '<S42>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcLevel'
 * '<S43>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcReady'
 * '<S44>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcRet'
 * '<S45>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_TripKm'
 * '<S46>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcDis/Compare To Zero'
 * '<S47>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcEn/Compare To Zero'
 * '<S48>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcEn/Compare To Zero1'
 * '<S49>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcLevel/Sel_EnLcMaxLevel'
 * '<S50>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcReady/Reset_Idle'
 * '<S51>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcReady/Set_Ready'
 * '<S52>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcReady/Set_Ready/Compare To Zero'
 * '<S53>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_TripKm/Enable_Lc_Km'
 * '<S54>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_CtfLim/Assign_Lc_CtfLim'
 * '<S55>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Lc_CtfLim'
 * '<S56>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Ratio'
 * '<S57>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Lc_CtfLim/LookUp_IR_S1'
 * '<S58>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Lc_CtfLim/LookUp_IR_S16'
 * '<S59>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Lc_CtfLim/LookUp_IR_S2'
 * '<S60>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Lc_CtfLim/RateLimiter_S16'
 * '<S61>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Lc_CtfLim/LookUp_IR_S1/Data Type Conversion Inherited3'
 * '<S62>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Lc_CtfLim/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S63>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Lc_CtfLim/LookUp_IR_S2/Data Type Conversion Inherited3'
 * '<S64>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Lc_CtfLim/RateLimiter_S16/Data Type Conversion Inherited1'
 * '<S65>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Ratio/Filt_Speed'
 * '<S66>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_CtfLim/Calc_Ratio/PreLookUpIdSearch_U16'
 * '<S67>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_Reduct/Assign_Lc_Reduct'
 * '<S68>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_Reduct/Calc_Lc_Reduct'
 * '<S69>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_Reduct/Calc_Lc_Reduct/RateLimiter_S1'
 * '<S70>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_Reduct/Calc_Lc_Reduct/RateLimiter_S16'
 * '<S71>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_Reduct/Calc_Lc_Reduct/RateLimiter_S1/Data Type Conversion Inherited1'
 * '<S72>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_Reduct/Calc_Lc_Reduct/RateLimiter_S16/Data Type Conversion Inherited1'
 * '<S73>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_Ret/Assign_Lc_Ret'
 * '<S74>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_Ret/Calc_Lc_Ret'
 * '<S75>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_Ret/Calc_Lc_Ret/RateLimiter_S1'
 * '<S76>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_Ret/Calc_Lc_Ret/RateLimiter_S16'
 * '<S77>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_Ret/Calc_Lc_Ret/RateLimiter_S1/Data Type Conversion Inherited1'
 * '<S78>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_Ret/Calc_Lc_Ret/RateLimiter_S16/Data Type Conversion Inherited1'
 * '<S79>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_SatLim/Assign_Lc_SatLim'
 * '<S80>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_SatLim/Calc_Lc_SatLim'
 * '<S81>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_SatLim/Calc_Ratio'
 * '<S82>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_SatLim/Calc_Lc_SatLim/LookUp_IR_S1'
 * '<S83>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_SatLim/Calc_Lc_SatLim/LookUp_IR_S16'
 * '<S84>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_SatLim/Calc_Lc_SatLim/LookUp_IR_S2'
 * '<S85>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_SatLim/Calc_Lc_SatLim/LookUp_IR_S1/Data Type Conversion Inherited3'
 * '<S86>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_SatLim/Calc_Lc_SatLim/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S87>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_SatLim/Calc_Lc_SatLim/LookUp_IR_S2/Data Type Conversion Inherited3'
 * '<S88>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_SatLim/Calc_Ratio/Filt_Speed'
 * '<S89>'  : 'LaunchCtrl/LaunchCtrl/T10ms/Lc_SatLim/Calc_Ratio/PreLookUpIdSearch_U16'
 */

/*-
 * Requirements for '<Root>': LaunchCtrl
 */
#endif                                 /* RTW_HEADER_LaunchCtrl_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
