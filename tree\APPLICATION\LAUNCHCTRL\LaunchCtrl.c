/*
 * File: LaunchCtrl.c
 *
 * Code generated for Simulink model 'LaunchCtrl'.
 *
 * Model version                  : 1.304
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu Jun 12 15:18:56 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Not run
 */

#include "LaunchCtrl.h"
#include "LaunchCtrl_private.h"

/* Named constants for Chart: '<S45>/Enable_Lc_Km' */
#define LaunchCtrl_IN_KM               ((uint8_T)1U)
#define LaunchCtrl_IN_ONE              ((uint8_T)2U)
#define LaunchCtrl_IN_THREE            ((uint8_T)3U)
#define LaunchCtrl_IN_TWO              ((uint8_T)4U)

/* Named constants for Chart: '<S4>/Chart_LaunchCtrl' */
#define Launc_IN_LC_OVERRIDE_PRE_LAUNCH ((uint8_T)3U)
#define LaunchCtr_IN_LC_OVERRIDE_LAUNCH ((uint8_T)2U)
#define LaunchCtrl_IN_LC_DISABLE       ((uint8_T)1U)
#define LaunchCtrl_IN_LC_ENABLING      ((uint8_T)2U)
#define LaunchCtrl_IN_LC_LAUNCH        ((uint8_T)3U)
#define LaunchCtrl_IN_LC_NONE          ((uint8_T)1U)
#define LaunchCtrl_IN_LC_READY         ((uint8_T)4U)
#define LaunchCtrl_IN_LC_RET           ((uint8_T)5U)
#define LaunchCtrl_IN_LC_TO_IDLE       ((uint8_T)6U)
#define LaunchCtrl_IN_LC_TO_LIM        ((uint8_T)7U)
#define LaunchCtrl_IN_LC_WAIT_IDLE     ((uint8_T)8U)

/* user code (top of source file) */
/* System '<Root>/LaunchCtrl' */
#ifdef _BUILD_LAUNCHCTRL_

/* Block signals and states (default storage) */
D_Work_LaunchCtrl_T LaunchCtrl_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_LaunchCtrl_T LaunchCtrl_PrevZCSigState;

/* External inputs (root inport signals with default storage) */
ExternalInputs_LaunchCtrl_T LaunchCtrl_U;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
uint8_T AwLevel;

/* selector */
int16_T CmeLcSat;

/* CmiLcP */
int16_T CmeLcSatOffI;

/* CmiLcP */
uint8_T EnAwMaxLevel;

/* selector */
uint8_T EnLcMaxLevel;

/* selector */
uint8_T EnTcMaxLevel[5];

/* Tc max level */
uint8_T FlgCtfLc;

/* flg cutoff */
uint8_T FlgEnLcKm;

/* flg Km */
uint8_T FlgLcCmiLow;

/* flag */
uint8_T FlgLcDiag;

/* flag */
uint8_T FlgLcEn;

/* flag */
uint8_T FlgLcEnd;

/* flag */
uint8_T FlgLcLaunch;

/* flag */
uint8_T FlgLcLevel;

/* flag */
uint8_T FlgLcLim;

/* flg limiter */
uint8_T FlgLcReady;

/* flag */
uint8_T FlgLcRet;

/* flag */
uint8_T FlgLcTrg;

/* flag cutoff */
uint32_T IDLaunchCtrl;

/* ID Version */
uint8_T IdxLcCutOff;

/* idx cutoff */
uint8_T LcActive;

/* LC Active */
uint8_T LcLevel;

/* selector */
int16_T LcRpmErr;

/* Err */
int8_T LcTrip;

/* counter Trip */
uint16_T RpmLcTrgCme;

/* RpmLcTrg */
int16_T RpmLcTrgCtf;

/* RpmLcTrg */
uint8_T SetTracCtrl;

/* selector */
uint8_T StLc;

/* Lc Status */
uint16_T TimLcToExit;

/* timeout */
uint16_T VehLcAxIntSat;

/* VehRbVfAxInt saturation in Spring Up */

/* Forward declaration for local functions */
static void LaunchCtrl_LC_COND_CTRL(const uint8_T *Merge, const boolean_T
  *Switch);

/* Output and update for function-call system: '<S1>/Init' */
void LaunchCtrl_Init(void)
{
  int32_T i;

  {
    /* user code (Output function Header) */

    /* System '<S1>/Init' */
    LaunchCtrl_initialize();

    /* Constant: '<S2>/ID_LAUNCH_CTRL' */
    IDLaunchCtrl = ID_LAUNCH_CTRL;

    /* Chart: '<S2>/Init_Scheduler' incorporates:
     *  SubSystem: '<S2>/Init_Data'
     */
    /* DataStoreWrite: '<S8>/Data Store Write1' incorporates:
     *  Constant: '<S8>/ZERO2'
     */
    /* Gateway: LaunchCtrl/Init/Init_Scheduler */
    /* During: LaunchCtrl/Init/Init_Scheduler */
    /* Entry Internal: LaunchCtrl/Init/Init_Scheduler */
    /* Transition: '<S9>:2' */
    /* Transition: '<S9>:4' */
    /* Event: '<S9>:6' */
    IdxLcCutOff = 0U;

    /* DataStoreWrite: '<S8>/Data Store Write10' incorporates:
     *  Constant: '<S8>/ZERO8'
     */
    CmeLcSatOffI = 0;

    /* DataStoreWrite: '<S8>/Data Store Write11' incorporates:
     *  Constant: '<S8>/ZERO9'
     */
    RpmLcTrgCtf = 0;

    /* DataStoreWrite: '<S8>/Data Store Write12' incorporates:
     *  Constant: '<S8>/ENLCMAXLEVEL'
     */
    EnLcMaxLevel = ENLCMAXLEVEL;

    /* DataStoreWrite: '<S8>/Data Store Write13' incorporates:
     *  Constant: '<S8>/ENAWMAXLEVEL'
     */
    EnAwMaxLevel = ENAWMAXLEVEL;
    for (i = 0; i < 5; i++) {
      /* DataStoreWrite: '<S8>/Data Store Write14' incorporates:
       *  Constant: '<S8>/VTENTCMAXLEVEL'
       */
      EnTcMaxLevel[(i)] = VTENTCMAXLEVEL[i];
    }

    /* DataStoreWrite: '<S8>/Data Store Write15' incorporates:
     *  Constant: '<S8>/ZERO10'
     */
    VehLcAxIntSat = 0U;

    /* DataStoreWrite: '<S8>/Data Store Write16' incorporates:
     *  Constant: '<S8>/ZERO11'
     */
    LcActive = 0U;

    /* DataStoreWrite: '<S8>/Data Store Write17' incorporates:
     *  Constant: '<S8>/LC_DISABLE'
     */
    StLc = ((uint8_T)LC_DISABLE);

    /* DataStoreWrite: '<S8>/Data Store Write2' incorporates:
     *  Constant: '<S8>/ZERO1'
     */
    CmeLcSat = 0;

    /* DataStoreWrite: '<S8>/Data Store Write3' incorporates:
     *  Constant: '<S8>/ZERO'
     */
    FlgLcTrg = 0U;

    /* DataStoreWrite: '<S8>/Data Store Write4' incorporates:
     *  Constant: '<S8>/ZERO3'
     */
    FlgCtfLc = 0U;

    /* DataStoreWrite: '<S8>/Data Store Write5' incorporates:
     *  Constant: '<S8>/ZERO4'
     */
    RpmLcTrgCme = 0U;

    /* DataStoreWrite: '<S8>/Data Store Write6' incorporates:
     *  Constant: '<S8>/ZERO5'
     */
    FlgLcLim = 0U;

    /* DataTypeConversion: '<S8>/Data Type Conversion' incorporates:
     *  Constant: '<S8>/NUMLCTRIP'
     *  DataStoreWrite: '<S8>/Data Store Write7'
     */
    LcTrip = (int8_T)NUMLCTRIP;

    /* DataStoreWrite: '<S8>/Data Store Write8' incorporates:
     *  Constant: '<S8>/ZERO6'
     */
    AwLevel = 0U;

    /* DataStoreWrite: '<S8>/Data Store Write9' incorporates:
     *  Constant: '<S8>/ZERO7'
     */
    SetTracCtrl = 0U;

    /* user code (Output function Trailer) */

    /* System '<S1>/Init' */

    /* PILOTAGGIO USCITE - 10ms */
  }
}

/* Output and update for function-call system: '<S4>/Lc_Dis' */
void LaunchCtrl_Lc_Dis(void)
{
  /* DataStoreWrite: '<S31>/Data Store Write1' incorporates:
   *  Constant: '<S31>/ZERO2'
   */
  CmeLcSatOffI = 0;

  /* DataStoreWrite: '<S31>/Data Store Write2' incorporates:
   *  Constant: '<S31>/ZERO1'
   */
  CmeLcSat = 0;

  /* DataStoreWrite: '<S31>/Data Store Write3' incorporates:
   *  Constant: '<S31>/ZERO'
   */
  FlgLcTrg = 0U;

  /* DataStoreWrite: '<S31>/Data Store Write4' incorporates:
   *  Constant: '<S31>/ZERO3'
   */
  FlgCtfLc = 0U;

  /* DataStoreWrite: '<S31>/Data Store Write5' incorporates:
   *  Constant: '<S31>/ZERO4'
   */
  RpmLcTrgCme = 0U;

  /* DataStoreWrite: '<S31>/Data Store Write6' incorporates:
   *  Constant: '<S31>/ZERO5'
   */
  FlgLcLim = 0U;

  /* DataStoreWrite: '<S31>/Data Store Write7' incorporates:
   *  Constant: '<S31>/ZERO6'
   */
  VehLcAxIntSat = 0U;

  /* DataStoreWrite: '<S31>/Data Store Write8' incorporates:
   *  Constant: '<S31>/ZERO7'
   */
  LcActive = 0U;
}

/* Output and update for function-call system: '<S4>/Lc_Idle' */
void LaunchCtrl_Lc_Idle(void)
{
  /* DataStoreWrite: '<S32>/Data Store Write1' incorporates:
   *  Constant: '<S32>/ZERO1'
   */
  CmeLcSatOffI = 0;

  /* DataStoreWrite: '<S32>/Data Store Write2' incorporates:
   *  Constant: '<S32>/LCCMETRGIDLE'
   */
  CmeLcSat = LCCMETRGIDLE;

  /* DataStoreWrite: '<S32>/Data Store Write3' incorporates:
   *  Constant: '<S32>/ONE'
   */
  FlgLcTrg = 1U;

  /* DataStoreWrite: '<S32>/Data Store Write4' incorporates:
   *  Constant: '<S32>/ZERO2'
   */
  FlgCtfLc = 0U;

  /* DataStoreWrite: '<S32>/Data Store Write5' incorporates:
   *  Constant: '<S32>/ZERO3'
   */
  RpmLcTrgCme = 0U;

  /* DataStoreWrite: '<S32>/Data Store Write6' incorporates:
   *  Constant: '<S32>/ZERO5'
   */
  FlgLcLim = 0U;

  /* DataStoreWrite: '<S32>/Data Store Write7' incorporates:
   *  Constant: '<S32>/ZERO6'
   */
  VehLcAxIntSat = 0U;

  /* DataStoreWrite: '<S32>/Data Store Write8' incorporates:
   *  Constant: '<S32>/ZERO7'
   */
  LcActive = 0U;
}

/* Output and update for function-call system: '<S4>/Lc_CtfLim' */
void LaunchCtrl_Lc_CtfLim(uint16_T rtu_vehspeed_index, int32_T
  rtu_vehspeed_ratio)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_U16_o_ibd;
  uint16_T rtb_PreLookUpIdSearch_U16_o_iab;
  int16_T rtb_LookUp_IR_S16_irz;
  uint8_T rtb_Conversion3_j5l;
  uint16_T rtb_Switch_kpb;
  int16_T rtb_DataStoreRead2;
  int16_T rtb_Conversion1_can[7];
  int32_T i;

  /* DataTypeConversion: '<S65>/Data Type Conversion' */
  i = rtu_vehspeed_ratio;
  if (rtu_vehspeed_ratio < 0) {
    i = 0;
  } else {
    if (rtu_vehspeed_ratio > 65535) {
      i = 65535;
    }
  }

  rtb_Switch_kpb = (uint16_T)i;

  /* End of DataTypeConversion: '<S65>/Data Type Conversion' */

  /* MinMax: '<S65>/MinMax' incorporates:
   *  Constant: '<S65>/LCVEHAXINTSAT'
   */
  if (LCVEHAXINTSAT < rtb_Switch_kpb) {
    rtb_Switch_kpb = LCVEHAXINTSAT;
  }

  /* End of MinMax: '<S65>/MinMax' */

  /* MinMax: '<S65>/MinMax1' incorporates:
   *  DataStoreRead: '<S65>/Data Store Read'
   */
  if (rtu_vehspeed_index > rtb_Switch_kpb) {
    rtb_Switch_kpb = rtu_vehspeed_index;
  }

  if (rtb_Switch_kpb > VehLcAxIntSat) {
  } else {
    rtb_Switch_kpb = VehLcAxIntSat;
  }

  /* End of MinMax: '<S65>/MinMax1' */

  /* DataStoreWrite: '<S65>/Data Store Write1' */
  VehLcAxIntSat = rtb_Switch_kpb;

  /* Switch: '<S65>/Switch' incorporates:
   *  Constant: '<S65>/LCVEHAXINTSAT'
   *  RelationalOperator: '<S65>/Relational Operator'
   */
  if (rtu_vehspeed_index >= LCVEHAXINTSAT) {
    rtb_Switch_kpb = rtu_vehspeed_index;
  }

  /* End of Switch: '<S65>/Switch' */

  /* DataTypeConversion: '<S66>/Data Type Conversion8' incorporates:
   *  Constant: '<S56>/BKLCCMETRG_dim'
   */
  rtb_Conversion3_j5l = (uint8_T)BKLCCMETRG_dim;

  /* S-Function (PreLookUpIdSearch_U16): '<S66>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S56>/BKLCCMETRG'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o_ibd,
                        &rtb_PreLookUpIdSearch_U16_o_iab, (uint16_T)
                        rtb_Switch_kpb, (uint16_T*)&BKLCCMETRG[0], (uint8_T)
                        rtb_Conversion3_j5l);

  /* Outputs for Atomic SubSystem: '<S30>/Calc_Lc_CtfLim' */
  /* DataTypeConversion: '<S57>/Conversion3' incorporates:
   *  Constant: '<S55>/BKLCCMETRG_dim'
   */
  rtb_Conversion3_j5l = (uint8_T)BKLCCMETRG_dim;

  /* S-Function (LookUp_IR_S16): '<S57>/LookUp_IR_S16' incorporates:
   *  Constant: '<S55>/VTLCCMEOFFI'
   */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_irz, (int16_T*)&VTLCCMEOFFI[0], (uint16_T)
                rtb_PreLookUpIdSearch_U16_o_ibd, (uint16_T)
                rtb_PreLookUpIdSearch_U16_o_iab, (uint8_T)rtb_Conversion3_j5l);

  /* DataStoreWrite: '<S55>/Data Store Write1' */
  CmeLcSatOffI = rtb_LookUp_IR_S16_irz;

  /* DataTypeConversion: '<S58>/Conversion3' incorporates:
   *  Constant: '<S55>/BKLCCMETRG_dim'
   */
  rtb_Conversion3_j5l = (uint8_T)BKLCCMETRG_dim;

  /* S-Function (LookUp_IR_S16): '<S58>/LookUp_IR_S16' incorporates:
   *  Constant: '<S55>/VTLCCMETRG'
   */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_irz, (int16_T*)&VTLCCMETRG[0], (uint16_T)
                rtb_PreLookUpIdSearch_U16_o_ibd, (uint16_T)
                rtb_PreLookUpIdSearch_U16_o_iab, (uint8_T)rtb_Conversion3_j5l);

  /* DataStoreRead: '<S55>/Data Store Read2' */
  rtb_DataStoreRead2 = CmeLcSat;

  /* S-Function (RateLimiter_S16): '<S60>/RateLimiter_S16' incorporates:
   *  Constant: '<S55>/LCRTCMEREADY'
   *  Constant: '<S55>/MIN'
   */
  RateLimiter_S16( &rtb_LookUp_IR_S16_irz, (int16_T)rtb_LookUp_IR_S16_irz,
                  (int16_T)rtb_DataStoreRead2, (int16_T)-32736, (int16_T)
                  LCRTCMEREADY);

  /* DataStoreWrite: '<S55>/Data Store Write2' */
  CmeLcSat = rtb_LookUp_IR_S16_irz;

  /* DataTypeConversion: '<S59>/Conversion1' incorporates:
   *  Constant: '<S55>/VTLCRPMTRG'
   */
  for (i = 0; i < 7; i++) {
    rtb_Conversion1_can[i] = (int16_T)VTLCRPMTRG[i];
  }

  /* End of DataTypeConversion: '<S59>/Conversion1' */

  /* DataTypeConversion: '<S59>/Conversion3' incorporates:
   *  Constant: '<S55>/BKLCCMETRG_dim'
   */
  rtb_Conversion3_j5l = (uint8_T)BKLCCMETRG_dim;

  /* S-Function (LookUp_IR_S16): '<S59>/LookUp_IR_S16' */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_irz, (int16_T*)&rtb_Conversion1_can[0],
                (uint16_T)rtb_PreLookUpIdSearch_U16_o_ibd, (uint16_T)
                rtb_PreLookUpIdSearch_U16_o_iab, (uint8_T)rtb_Conversion3_j5l);

  /* DataTypeConversion: '<S63>/Conversion' incorporates:
   *  DataStoreWrite: '<S55>/Data Store Write3'
   */
  RpmLcTrgCme = (uint16_T)rtb_LookUp_IR_S16_irz;

  /* End of Outputs for SubSystem: '<S30>/Calc_Lc_CtfLim' */

  /* Outputs for Atomic SubSystem: '<S30>/Assign_Lc_CtfLim' */
  /* DataStoreWrite: '<S54>/Data Store Write3' incorporates:
   *  Constant: '<S54>/ONE'
   */
  FlgLcTrg = 1U;

  /* DataStoreWrite: '<S54>/Data Store Write4' incorporates:
   *  Constant: '<S54>/ZERO2'
   */
  FlgCtfLc = 1U;

  /* DataStoreWrite: '<S54>/Data Store Write6' incorporates:
   *  Constant: '<S54>/ZERO5'
   */
  FlgLcLim = 0U;

  /* DataStoreWrite: '<S54>/Data Store Write8' incorporates:
   *  Constant: '<S54>/ZERO7'
   */
  LcActive = 1U;

  /* End of Outputs for SubSystem: '<S30>/Assign_Lc_CtfLim' */
}

/* Output and update for function-call system: '<S4>/Lc_SatLim' */
void LaunchCtrl_Lc_SatLim(uint16_T rtu_vehspeed_index, int32_T
  rtu_vehspeed_ratio)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_U16_o_aw0;
  uint16_T rtb_PreLookUpIdSearch_U16_o_noe;
  int16_T rtb_LookUp_IR_S16_dpn;
  uint8_T rtb_Conversion3_fcv;
  uint16_T rtb_Switch_kfu;
  int16_T rtb_Conversion1_kgu[7];
  int32_T i;

  /* DataTypeConversion: '<S88>/Data Type Conversion' */
  i = rtu_vehspeed_ratio;
  if (rtu_vehspeed_ratio < 0) {
    i = 0;
  } else {
    if (rtu_vehspeed_ratio > 65535) {
      i = 65535;
    }
  }

  rtb_Switch_kfu = (uint16_T)i;

  /* End of DataTypeConversion: '<S88>/Data Type Conversion' */

  /* MinMax: '<S88>/MinMax' incorporates:
   *  Constant: '<S88>/LCVEHAXINTSAT'
   */
  if (LCVEHAXINTSAT < rtb_Switch_kfu) {
    rtb_Switch_kfu = LCVEHAXINTSAT;
  }

  /* End of MinMax: '<S88>/MinMax' */

  /* MinMax: '<S88>/MinMax1' incorporates:
   *  DataStoreRead: '<S88>/Data Store Read'
   */
  if (rtu_vehspeed_index > rtb_Switch_kfu) {
    rtb_Switch_kfu = rtu_vehspeed_index;
  }

  if (rtb_Switch_kfu > VehLcAxIntSat) {
  } else {
    rtb_Switch_kfu = VehLcAxIntSat;
  }

  /* End of MinMax: '<S88>/MinMax1' */

  /* DataStoreWrite: '<S88>/Data Store Write1' */
  VehLcAxIntSat = rtb_Switch_kfu;

  /* Switch: '<S88>/Switch' incorporates:
   *  Constant: '<S88>/LCVEHAXINTSAT'
   *  RelationalOperator: '<S88>/Relational Operator'
   */
  if (rtu_vehspeed_index >= LCVEHAXINTSAT) {
    rtb_Switch_kfu = rtu_vehspeed_index;
  }

  /* End of Switch: '<S88>/Switch' */

  /* DataTypeConversion: '<S89>/Data Type Conversion8' incorporates:
   *  Constant: '<S81>/BKLCCMETRG_dim'
   */
  rtb_Conversion3_fcv = (uint8_T)BKLCCMETRG_dim;

  /* S-Function (PreLookUpIdSearch_U16): '<S89>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S81>/BKLCCMETRG'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o_aw0,
                        &rtb_PreLookUpIdSearch_U16_o_noe, (uint16_T)
                        rtb_Switch_kfu, (uint16_T*)&BKLCCMETRG[0], (uint8_T)
                        rtb_Conversion3_fcv);

  /* Outputs for Atomic SubSystem: '<S35>/Calc_Lc_SatLim' */
  /* DataTypeConversion: '<S84>/Conversion1' incorporates:
   *  Constant: '<S80>/VTLCRPMTRG'
   */
  for (i = 0; i < 7; i++) {
    rtb_Conversion1_kgu[i] = (int16_T)VTLCRPMTRG[i];
  }

  /* End of DataTypeConversion: '<S84>/Conversion1' */

  /* DataTypeConversion: '<S84>/Conversion3' incorporates:
   *  Constant: '<S80>/BKLCCMETRG_dim'
   */
  rtb_Conversion3_fcv = (uint8_T)BKLCCMETRG_dim;

  /* S-Function (LookUp_IR_S16): '<S84>/LookUp_IR_S16' */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_dpn, (int16_T*)&rtb_Conversion1_kgu[0],
                (uint16_T)rtb_PreLookUpIdSearch_U16_o_aw0, (uint16_T)
                rtb_PreLookUpIdSearch_U16_o_noe, (uint8_T)rtb_Conversion3_fcv);

  /* DataTypeConversion: '<S87>/Conversion' incorporates:
   *  DataStoreWrite: '<S80>/Data Store Write1'
   */
  RpmLcTrgCme = (uint16_T)rtb_LookUp_IR_S16_dpn;

  /* DataTypeConversion: '<S83>/Conversion3' incorporates:
   *  Constant: '<S80>/BKLCCMETRG_dim'
   */
  rtb_Conversion3_fcv = (uint8_T)BKLCCMETRG_dim;

  /* S-Function (LookUp_IR_S16): '<S83>/LookUp_IR_S16' incorporates:
   *  Constant: '<S80>/VTLCCMETRG'
   */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_dpn, (int16_T*)&VTLCCMETRG[0], (uint16_T)
                rtb_PreLookUpIdSearch_U16_o_aw0, (uint16_T)
                rtb_PreLookUpIdSearch_U16_o_noe, (uint8_T)rtb_Conversion3_fcv);

  /* DataStoreWrite: '<S80>/Data Store Write2' */
  CmeLcSat = rtb_LookUp_IR_S16_dpn;

  /* DataTypeConversion: '<S82>/Conversion3' incorporates:
   *  Constant: '<S80>/BKLCCMETRG_dim'
   */
  rtb_Conversion3_fcv = (uint8_T)BKLCCMETRG_dim;

  /* S-Function (LookUp_IR_S16): '<S82>/LookUp_IR_S16' incorporates:
   *  Constant: '<S80>/VTLCCMEOFFI'
   */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_dpn, (int16_T*)&VTLCCMEOFFI[0], (uint16_T)
                rtb_PreLookUpIdSearch_U16_o_aw0, (uint16_T)
                rtb_PreLookUpIdSearch_U16_o_noe, (uint8_T)rtb_Conversion3_fcv);

  /* DataStoreWrite: '<S80>/Data Store Write3' */
  CmeLcSatOffI = rtb_LookUp_IR_S16_dpn;

  /* End of Outputs for SubSystem: '<S35>/Calc_Lc_SatLim' */

  /* Outputs for Atomic SubSystem: '<S35>/Assign_Lc_SatLim' */
  /* DataStoreWrite: '<S79>/Data Store Write3' incorporates:
   *  Constant: '<S79>/ONE'
   */
  FlgLcTrg = 1U;

  /* DataStoreWrite: '<S79>/Data Store Write4' incorporates:
   *  Constant: '<S79>/ZERO2'
   */
  FlgCtfLc = 0U;

  /* DataStoreWrite: '<S79>/Data Store Write6' incorporates:
   *  Constant: '<S79>/ONE2'
   */
  FlgLcLim = 1U;

  /* DataStoreWrite: '<S79>/Data Store Write8' incorporates:
   *  Constant: '<S79>/ZERO7'
   */
  LcActive = 1U;

  /* End of Outputs for SubSystem: '<S35>/Assign_Lc_SatLim' */
}

/* Output and update for function-call system: '<S4>/Lc_Ret' */
void LaunchCtrl_Lc_Ret(uint8_T rtu_flgRetTO, int16_T rtu_CmeDriverP)
{
  /* local block i/o variables */
  int16_T rtb_RateLimiter_S16_egk;
  int16_T rtb_DataStoreRead1_ojl;

  /* Outputs for Atomic SubSystem: '<S34>/Calc_Lc_Ret' */
  /* DataStoreRead: '<S74>/Data Store Read1' */
  rtb_DataStoreRead1_ojl = CmeLcSatOffI;

  /* S-Function (RateLimiter_S16): '<S75>/RateLimiter_S16' incorporates:
   *  Constant: '<S74>/LCRTCMERET'
   *  Constant: '<S74>/MIN'
   *  Constant: '<S74>/ZERO'
   */
  RateLimiter_S16( &rtb_RateLimiter_S16_egk, (int16_T)0, (int16_T)
                  rtb_DataStoreRead1_ojl, (int16_T)-32736, (int16_T)LCRTCMERET);

  /* DataStoreWrite: '<S74>/Data Store Write1' */
  CmeLcSatOffI = rtb_RateLimiter_S16_egk;

  /* DataStoreRead: '<S74>/Data Store Read2' */
  rtb_DataStoreRead1_ojl = CmeLcSat;

  /* Switch: '<S74>/Switch' incorporates:
   *  Constant: '<S74>/LCRTCMERET'
   *  Constant: '<S74>/LCRTCMERETLOW'
   */
  if (rtu_flgRetTO != 0) {
    rtb_RateLimiter_S16_egk = LCRTCMERETLOW;
  } else {
    rtb_RateLimiter_S16_egk = LCRTCMERET;
  }

  /* End of Switch: '<S74>/Switch' */

  /* S-Function (RateLimiter_S16): '<S76>/RateLimiter_S16' incorporates:
   *  Constant: '<S74>/MIN'
   */
  RateLimiter_S16( &rtb_RateLimiter_S16_egk, (int16_T)rtu_CmeDriverP, (int16_T)
                  rtb_DataStoreRead1_ojl, (int16_T)-32736, (int16_T)
                  rtb_RateLimiter_S16_egk);

  /* DataStoreWrite: '<S74>/Data Store Write2' */
  CmeLcSat = rtb_RateLimiter_S16_egk;

  /* End of Outputs for SubSystem: '<S34>/Calc_Lc_Ret' */

  /* Outputs for Atomic SubSystem: '<S34>/Assign_Lc_Ret' */
  /* DataStoreWrite: '<S73>/Data Store Write3' incorporates:
   *  Constant: '<S73>/ONE'
   */
  FlgLcTrg = 1U;

  /* DataStoreWrite: '<S73>/Data Store Write4' incorporates:
   *  Constant: '<S73>/ZERO2'
   */
  FlgCtfLc = 0U;

  /* DataStoreWrite: '<S73>/Data Store Write6' incorporates:
   *  Constant: '<S73>/ZERO5'
   */
  FlgLcLim = 0U;

  /* DataStoreWrite: '<S73>/Data Store Write8' incorporates:
   *  Constant: '<S73>/ZERO7'
   */
  LcActive = 0U;

  /* End of Outputs for SubSystem: '<S34>/Assign_Lc_Ret' */
}

/* Output and update for function-call system: '<S4>/Lc_Reduct' */
void LaunchCtrl_Lc_Reduct(void)
{
  /* local block i/o variables */
  int16_T rtb_RateLimiter_S16_m5q;
  int16_T rtb_DataStoreRead1_hml;
  int16_T rtb_Gain;

  /* Outputs for Atomic SubSystem: '<S33>/Assign_Lc_Reduct' */
  /* DataStoreWrite: '<S67>/Data Store Write3' incorporates:
   *  Constant: '<S67>/ONE'
   */
  FlgLcTrg = 1U;

  /* DataStoreWrite: '<S67>/Data Store Write4' incorporates:
   *  Constant: '<S67>/ZERO2'
   */
  FlgCtfLc = 1U;

  /* DataStoreWrite: '<S67>/Data Store Write6' incorporates:
   *  Constant: '<S67>/ZERO5'
   */
  FlgLcLim = 0U;

  /* DataStoreWrite: '<S67>/Data Store Write8' incorporates:
   *  Constant: '<S67>/ZERO7'
   */
  LcActive = 0U;

  /* End of Outputs for SubSystem: '<S33>/Assign_Lc_Reduct' */

  /* Outputs for Atomic SubSystem: '<S33>/Calc_Lc_Reduct' */
  /* DataStoreRead: '<S68>/Data Store Read1' */
  rtb_DataStoreRead1_hml = CmeLcSatOffI;

  /* Gain: '<S68>/Gain' incorporates:
   *  Constant: '<S68>/LCRTCMERED'
   */
  rtb_Gain = (int16_T)(-LCRTCMERED);

  /* S-Function (RateLimiter_S16): '<S69>/RateLimiter_S16' incorporates:
   *  Constant: '<S68>/MIN'
   *  Constant: '<S68>/ZERO1'
   */
  RateLimiter_S16( &rtb_RateLimiter_S16_m5q, (int16_T)0, (int16_T)
                  rtb_DataStoreRead1_hml, (int16_T)-32736, (int16_T)rtb_Gain);

  /* DataStoreWrite: '<S68>/Data Store Write1' */
  CmeLcSatOffI = rtb_RateLimiter_S16_m5q;

  /* DataStoreRead: '<S68>/Data Store Read2' */
  rtb_DataStoreRead1_hml = CmeLcSat;

  /* S-Function (RateLimiter_S16): '<S70>/RateLimiter_S16' incorporates:
   *  Constant: '<S68>/LCCMETRGRED'
   *  Constant: '<S68>/LCRTCMERED'
   *  Constant: '<S68>/MAX'
   */
  RateLimiter_S16( &rtb_RateLimiter_S16_m5q, (int16_T)LCCMETRGRED, (int16_T)
                  rtb_DataStoreRead1_hml, (int16_T)LCRTCMERED, (int16_T)32736);

  /* DataStoreWrite: '<S68>/Data Store Write2' */
  CmeLcSat = rtb_RateLimiter_S16_m5q;

  /* End of Outputs for SubSystem: '<S33>/Calc_Lc_Reduct' */
}

/* Output and update for function-call system: '<S4>/Lc_To_Idle' */
void LaunchCtrl_Lc_To_Idle(void)
{
  /* DataStoreWrite: '<S36>/Data Store Write1' incorporates:
   *  Constant: '<S36>/ZERO1'
   */
  CmeLcSatOffI = 0;

  /* DataStoreWrite: '<S36>/Data Store Write2' incorporates:
   *  Constant: '<S36>/LCCMETRGIDLE'
   */
  CmeLcSat = LCCMETRGIDLE;

  /* DataStoreWrite: '<S36>/Data Store Write3' incorporates:
   *  Constant: '<S36>/ONE'
   */
  FlgLcTrg = 1U;

  /* DataStoreWrite: '<S36>/Data Store Write4' incorporates:
   *  Constant: '<S36>/ZERO2'
   */
  FlgCtfLc = 1U;

  /* DataStoreWrite: '<S36>/Data Store Write6' incorporates:
   *  Constant: '<S36>/ZERO5'
   */
  FlgLcLim = 0U;

  /* DataStoreWrite: '<S36>/Data Store Write8' incorporates:
   *  Constant: '<S36>/ZERO7'
   */
  LcActive = 0U;
}

/* Function for Chart: '<S4>/Chart_LaunchCtrl' */
static void LaunchCtrl_LC_COND_CTRL(const uint8_T *Merge, const boolean_T
  *Switch)
{
  int32_T tmp;

  /* During 'LC_COND_CTRL': '<S29>:73' */
  switch (LaunchCtrl_DWork.is_LC_COND_CTRL) {
   case LaunchCtrl_IN_LC_DISABLE:
    /* DataStoreWrite: '<S39>/Data Store Write1' */
    /* During 'LC_DISABLE': '<S29>:17' */
    /* Transition: '<S29>:247' */
    if ((FlgLcEn != 0) && (LaunchCtrl_DWork.cnt > 25)) {
      /* Transition: '<S29>:23' */
      LaunchCtrl_DWork.cnt = 0U;

      /* Outputs for Function Call SubSystem: '<S4>/Lc_Dis' */
      /* Event: '<S29>:128' */
      LaunchCtrl_Lc_Dis();

      /* End of Outputs for SubSystem: '<S4>/Lc_Dis' */
      StLc = ((uint8_T)LC_ENABLING);
      LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_ENABLING;
    } else {
      /* Transition: '<S29>:22' */
      tmp = LaunchCtrl_DWork.cnt + 1;
      if (tmp > 255) {
        tmp = 255;
      }

      LaunchCtrl_DWork.cnt = (uint8_T)tmp;

      /* Outputs for Function Call SubSystem: '<S4>/Lc_Dis' */
      /* Event: '<S29>:128' */
      LaunchCtrl_Lc_Dis();

      /* End of Outputs for SubSystem: '<S4>/Lc_Dis' */
    }
    break;

   case LaunchCtrl_IN_LC_ENABLING:
    /* DataStoreWrite: '<S39>/Data Store Write1' incorporates:
     *  DataStoreWrite: '<S42>/Data Store Write1'
     */
    /* During 'LC_ENABLING': '<S29>:19' */
    /* Transition: '<S29>:26' */
    if (FlgLcEn == 0) {
      /* Transition: '<S29>:37' */
      LaunchCtrl_DWork.cnt = 0U;

      /* Outputs for Function Call SubSystem: '<S4>/Lc_Dis' */
      /* Event: '<S29>:128' */
      LaunchCtrl_Lc_Dis();

      /* End of Outputs for SubSystem: '<S4>/Lc_Dis' */
      StLc = ((uint8_T)LC_DISABLE);
      LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_DISABLE;
    } else {
      /* Transition: '<S29>:36' */
      if (((FlgLcLevel != 0) && (EndStartFlg != 0)) && (LaunchCtrl_DWork.cnt >
           15)) {
        /* Transition: '<S29>:28' */
        LaunchCtrl_DWork.cnt = 0U;

        /* Outputs for Function Call SubSystem: '<S4>/Lc_Idle' */
        /* Event: '<S29>:129' */
        LaunchCtrl_Lc_Idle();

        /* End of Outputs for SubSystem: '<S4>/Lc_Idle' */
        StLc = ((uint8_T)LC_WAIT_IDLE);
        LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_WAIT_IDLE;
      } else {
        /* Transition: '<S29>:27' */
        tmp = LaunchCtrl_DWork.cnt + 1;
        if (tmp > 255) {
          tmp = 255;
        }

        LaunchCtrl_DWork.cnt = (uint8_T)tmp;

        /* Outputs for Function Call SubSystem: '<S4>/Lc_Dis' */
        /* Event: '<S29>:128' */
        LaunchCtrl_Lc_Dis();

        /* End of Outputs for SubSystem: '<S4>/Lc_Dis' */
      }
    }
    break;

   case LaunchCtrl_IN_LC_LAUNCH:
    /* DataStoreWrite: '<S40>/Data Store Write2' */
    /* During 'LC_LAUNCH': '<S29>:49' */
    /* Transition: '<S29>:52' */
    if ((FlgLcEnd != 0) && (LaunchCtrl_DWork.cnt > 15)) {
      /* Transition: '<S29>:54' */
      LaunchCtrl_DWork.cnt = 0U;
      LaunchCtrl_DWork.flgRetTO = 0U;

      /* Outputs for Function Call SubSystem: '<S4>/Lc_Ret' */
      /* Event: '<S29>:132' */
      LaunchCtrl_Lc_Ret(LaunchCtrl_DWork.flgRetTO, CmeDriverP);

      /* End of Outputs for SubSystem: '<S4>/Lc_Ret' */
      StLc = ((uint8_T)LC_RET);
      LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_RET;
    } else {
      /* Transition: '<S29>:55' */
      tmp = LaunchCtrl_DWork.cnt + 1;
      if (tmp > 255) {
        tmp = 255;
      }

      LaunchCtrl_DWork.cnt = (uint8_T)tmp;

      /* Outputs for Function Call SubSystem: '<S4>/Lc_SatLim' */
      /* Event: '<S29>:131' */
      LaunchCtrl_Lc_SatLim(VehSpeedFront, VehRbVfAxInt);

      /* End of Outputs for SubSystem: '<S4>/Lc_SatLim' */
    }

    /* End of DataStoreWrite: '<S40>/Data Store Write2' */
    break;

   case LaunchCtrl_IN_LC_READY:
    /* DataStoreWrite: '<S41>/Data Store Write2' */
    /* During 'LC_READY': '<S29>:38' */
    /* Transition: '<S29>:42' */
    if (FlgLcLaunch != 0) {
      /* Transition: '<S29>:50' */
      LaunchCtrl_DWork.cnt = 0U;

      /* Outputs for Function Call SubSystem: '<S4>/Lc_SatLim' */
      /* Event: '<S29>:131' */
      LaunchCtrl_Lc_SatLim(VehSpeedFront, VehRbVfAxInt);

      /* End of Outputs for SubSystem: '<S4>/Lc_SatLim' */
      tmp = EELcTrip + 1;
      if (tmp > 65535) {
        tmp = 65535;
      }

      EELcTrip = (uint16_T)tmp;
      StLc = ((uint8_T)LC_LAUNCH);
      if (LcTrip != 0) {
        /* Transition: '<S29>:175' */
        tmp = LcTrip - 1;
        if (tmp < -128) {
          tmp = -128;
        }

        LcTrip = (int8_T)tmp;
      } else {
        /* Transition: '<S29>:176' */
      }

      LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_LAUNCH;
    } else {
      /* Transition: '<S29>:67' */
      if (((((*Merge) == 0) || (*Switch)) || (EndStartFlg == 0)) &&
          (LaunchCtrl_DWork.cnt > 15)) {
        /* Transition: '<S29>:65' */
        LaunchCtrl_DWork.cnt = 0U;

        /* Outputs for Function Call SubSystem: '<S4>/Lc_Reduct' */
        /* Event: '<S29>:168' */
        LaunchCtrl_Lc_Reduct();

        /* End of Outputs for SubSystem: '<S4>/Lc_Reduct' */
        StLc = ((uint8_T)LC_TO_LIM);

        /* Transition: '<S29>:135' */
        LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_TO_LIM;
      } else {
        /* Transition: '<S29>:48' */
        tmp = LaunchCtrl_DWork.cnt + 1;
        if (tmp > 255) {
          tmp = 255;
        }

        LaunchCtrl_DWork.cnt = (uint8_T)tmp;

        /* Outputs for Function Call SubSystem: '<S4>/Lc_CtfLim' */
        /* Event: '<S29>:130' */
        LaunchCtrl_Lc_CtfLim(VehSpeedFront, VehRbVfAxInt);

        /* End of Outputs for SubSystem: '<S4>/Lc_CtfLim' */
      }
    }
    break;

   case LaunchCtrl_IN_LC_RET:
    /* DataStoreWrite: '<S44>/Data Store Write2' */
    /* During 'LC_RET': '<S29>:76' */
    /* Transition: '<S29>:78' */
    if ((FlgLcRet != 0) && (LaunchCtrl_DWork.cnt > 15)) {
      /* Transition: '<S29>:79' */
      LaunchCtrl_DWork.cnt = 0U;

      /* Outputs for Function Call SubSystem: '<S4>/Lc_Dis' */
      /* Event: '<S29>:128' */
      LaunchCtrl_Lc_Dis();

      /* End of Outputs for SubSystem: '<S4>/Lc_Dis' */
      StLc = ((uint8_T)LC_DISABLE);

      /* Transition: '<S29>:237' */
      /* Transition: '<S29>:56' */
      LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_DISABLE;
    } else {
      /* Transition: '<S29>:80' */
      tmp = LaunchCtrl_DWork.cnt + 1;
      if (tmp > 255) {
        tmp = 255;
      }

      LaunchCtrl_DWork.cnt = (uint8_T)tmp;

      /* Outputs for Function Call SubSystem: '<S4>/Lc_Ret' */
      /* Event: '<S29>:132' */
      LaunchCtrl_Lc_Ret(LaunchCtrl_DWork.flgRetTO, CmeDriverP);

      /* End of Outputs for SubSystem: '<S4>/Lc_Ret' */
    }

    /* End of DataStoreWrite: '<S44>/Data Store Write2' */
    break;

   case LaunchCtrl_IN_LC_TO_IDLE:
    /* DataStoreWrite: '<S42>/Data Store Write1' */
    /* During 'LC_TO_IDLE': '<S29>:64' */
    /* Transition: '<S29>:221' */
    if (((FlgLcLevel == 0) || (EndStartFlg == 0)) && (LaunchCtrl_DWork.cnt > 15))
    {
      /* Transition: '<S29>:148' */
      LaunchCtrl_DWork.cnt = 0U;

      /* Outputs for Function Call SubSystem: '<S4>/Lc_Dis' */
      /* Event: '<S29>:128' */
      LaunchCtrl_Lc_Dis();

      /* End of Outputs for SubSystem: '<S4>/Lc_Dis' */
      StLc = ((uint8_T)LC_DISABLE);

      /* Transition: '<S29>:244' */
      /* Transition: '<S29>:245' */
      LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_DISABLE;
    } else {
      /* Transition: '<S29>:222' */
      if ((((*Merge) == 0) && (LaunchCtrl_DWork.cntWAMsg >= CNTLCWAMSG)) &&
          (LaunchCtrl_DWork.cnt > 15)) {
        /* Transition: '<S29>:70' */
        LaunchCtrl_DWork.cnt = 0U;

        /* Outputs for Function Call SubSystem: '<S4>/Lc_Idle' */
        /* Event: '<S29>:129' */
        LaunchCtrl_Lc_Idle();

        /* End of Outputs for SubSystem: '<S4>/Lc_Idle' */
        StLc = ((uint8_T)LC_WAIT_IDLE);
        LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_WAIT_IDLE;
      } else {
        /* Transition: '<S29>:220' */
        tmp = LaunchCtrl_DWork.cnt + 1;
        if (tmp > 255) {
          tmp = 255;
        }

        LaunchCtrl_DWork.cnt = (uint8_T)tmp;

        /* Outputs for Function Call SubSystem: '<S4>/Lc_To_Idle' */
        /* Event: '<S29>:248' */
        LaunchCtrl_Lc_To_Idle();

        /* End of Outputs for SubSystem: '<S4>/Lc_To_Idle' */
        tmp = LaunchCtrl_DWork.cntWAMsg + 1;
        if (tmp > 255) {
          tmp = 255;
        }

        LaunchCtrl_DWork.cntWAMsg = (uint8_T)tmp;
      }
    }
    break;

   case LaunchCtrl_IN_LC_TO_LIM:
    /* DataStoreWrite: '<S44>/Data Store Write1' incorporates:
     *  DataStoreWrite: '<S41>/Data Store Write2'
     */
    /* During 'LC_TO_LIM': '<S29>:133' */
    /* Transition: '<S29>:225' */
    if ((FlgLcCmiLow != 0) || ((EndStartFlg == 0) && (LaunchCtrl_DWork.cnt > 15)))
    {
      /* Transition: '<S29>:143' */
      LaunchCtrl_DWork.cnt = 0U;

      /* Outputs for Function Call SubSystem: '<S4>/Lc_To_Idle' */
      /* Event: '<S29>:248' */
      LaunchCtrl_Lc_To_Idle();

      /* End of Outputs for SubSystem: '<S4>/Lc_To_Idle' */
      LaunchCtrl_DWork.cntWAMsg = 0U;
      StLc = ((uint8_T)LC_TO_IDLE);
      LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_TO_IDLE;
    } else {
      /* Transition: '<S29>:224' */
      if (FlgLcLaunch != 0) {
        /* Transition: '<S29>:139' */
        LaunchCtrl_DWork.cnt = 0U;

        /* Outputs for Function Call SubSystem: '<S4>/Lc_SatLim' */
        /* Event: '<S29>:131' */
        LaunchCtrl_Lc_SatLim(VehSpeedFront, VehRbVfAxInt);

        /* End of Outputs for SubSystem: '<S4>/Lc_SatLim' */
        tmp = EELcTrip + 1;
        if (tmp > 65535) {
          tmp = 65535;
        }

        EELcTrip = (uint16_T)tmp;
        StLc = ((uint8_T)LC_LAUNCH);

        /* Transition: '<S29>:235' */
        if (LcTrip != 0) {
          /* Transition: '<S29>:175' */
          tmp = LcTrip - 1;
          if (tmp < -128) {
            tmp = -128;
          }

          LcTrip = (int8_T)tmp;
        } else {
          /* Transition: '<S29>:176' */
        }

        LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_LAUNCH;
      } else {
        /* Transition: '<S29>:144' */
        tmp = LaunchCtrl_DWork.cnt + 1;
        if (tmp > 255) {
          tmp = 255;
        }

        LaunchCtrl_DWork.cnt = (uint8_T)tmp;

        /* Outputs for Function Call SubSystem: '<S4>/Lc_Reduct' */
        /* Event: '<S29>:168' */
        LaunchCtrl_Lc_Reduct();

        /* End of Outputs for SubSystem: '<S4>/Lc_Reduct' */
      }
    }

    /* End of DataStoreWrite: '<S44>/Data Store Write1' */
    break;

   default:
    /* DataStoreWrite: '<S42>/Data Store Write1' */
    /* During 'LC_WAIT_IDLE': '<S29>:24' */
    /* Transition: '<S29>:30' */
    if ((FlgLcLevel == 0) || (EndStartFlg == 0)) {
      /* Transition: '<S29>:31' */
      LaunchCtrl_DWork.cnt = 0U;

      /* Outputs for Function Call SubSystem: '<S4>/Lc_Dis' */
      /* Event: '<S29>:128' */
      LaunchCtrl_Lc_Dis();

      /* End of Outputs for SubSystem: '<S4>/Lc_Dis' */
      StLc = ((uint8_T)LC_DISABLE);

      /* Transition: '<S29>:56' */
      LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_DISABLE;
    } else {
      /* Transition: '<S29>:33' */
      if (((*Merge) != 0) && (LaunchCtrl_DWork.cnt > 15)) {
        /* Transition: '<S29>:39' */
        LaunchCtrl_DWork.cnt = 0U;

        /* Outputs for Function Call SubSystem: '<S4>/Lc_CtfLim' */
        /* Event: '<S29>:130' */
        LaunchCtrl_Lc_CtfLim(VehSpeedFront, VehRbVfAxInt);

        /* End of Outputs for SubSystem: '<S4>/Lc_CtfLim' */
        StLc = ((uint8_T)LC_READY);
        LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_READY;
      } else {
        /* Transition: '<S29>:34' */
        tmp = LaunchCtrl_DWork.cnt + 1;
        if (tmp > 255) {
          tmp = 255;
        }

        LaunchCtrl_DWork.cnt = (uint8_T)tmp;

        /* Outputs for Function Call SubSystem: '<S4>/Lc_Idle' */
        /* Event: '<S29>:129' */
        LaunchCtrl_Lc_Idle();

        /* End of Outputs for SubSystem: '<S4>/Lc_Idle' */
      }
    }
    break;
  }
}

/* System initialize for function-call system: '<S1>/T10ms' */
void LaunchCtrl_T10ms_Init(void)
{
  /* SystemInitialize for Chart: '<S45>/Enable_Lc_Km' */
  LaunchCtrl_DWork.is_active_c1_LaunchCtrl = 0U;
  LaunchCtrl_DWork.is_c1_LaunchCtrl = 0;
  LaunchCtrl_DWork.offLcKm = 0;
  LaunchCtrl_DWork.tmpOdometer = 0;
  LaunchCtrl_DWork.enLcKm = 0U;

  /* SystemInitialize for Chart: '<S4>/Chart_LaunchCtrl' */
  LaunchCtrl_DWork.is_LC_COND_CTRL = 0;
  LaunchCtrl_DWork.is_LC_OVERRIDE_CTRL = 0;
  LaunchCtrl_DWork.is_active_c3_LaunchCtrl = 0U;
  LaunchCtrl_DWork.cntWAMsg = 0U;
  LaunchCtrl_DWork.cnt = 0U;
  LaunchCtrl_DWork.flgRetTO = 0U;
}

/* Output and update for function-call system: '<S1>/T10ms' */
void LaunchCtrl_T10ms(void)
{
  boolean_T rtb_LogicalOperator1;
  int32_T rtb_DataTypeConversion_of2;
  uint8_T AwLevel0;
  uint8_T SetTracCtrl0;
  boolean_T Switch;
  uint8_T rtb_LogicalOperator1_0;
  uint32_T tmp;

  /* Logic: '<S26>/Logical Operator1' incorporates:
   *  Constant: '<S26>/ST_SAT_MAX'
   *  RelationalOperator: '<S26>/Relational Operator1'
   */
  rtb_LogicalOperator1 = ((ST_SAT_MAX == StSatAw) && (FlgYawRec != 0));

  /* Switch: '<S26>/Switch' incorporates:
   *  Constant: '<S26>/ENAWMAXLEVEL'
   *  Constant: '<S26>/REC_AW'
   *  DataStoreWrite: '<S26>/Data Store Write2'
   */
  if (rtb_LogicalOperator1) {
    EnAwMaxLevel = 4U;
  } else {
    EnAwMaxLevel = ENAWMAXLEVEL;
  }

  /* End of Switch: '<S26>/Switch' */

  /* Logic: '<S38>/Logical Operator' incorporates:
   *  Constant: '<S38>/DIAG_CLUTCH'
   *  Constant: '<S38>/DIAG_GEAR_SENSOR'
   *  Constant: '<S38>/DIAG_VEHSPEED'
   *  Constant: '<S38>/DIAG_VEHSPEED_FRONT'
   *  Constant: '<S38>/FAULT'
   *  Constant: '<S38>/FAULT1'
   *  Constant: '<S38>/FAULT2'
   *  Constant: '<S38>/FAULT3'
   *  Constant: '<S46>/Constant'
   *  DataStoreWrite: '<S38>/Data Store Write1'
   *  RelationalOperator: '<S38>/Relational Operator'
   *  RelationalOperator: '<S38>/Relational Operator1'
   *  RelationalOperator: '<S38>/Relational Operator2'
   *  RelationalOperator: '<S38>/Relational Operator3'
   *  RelationalOperator: '<S46>/Compare'
   *  Selector: '<S38>/Selector'
   *  Selector: '<S38>/Selector1'
   *  Selector: '<S38>/Selector2'
   *  Selector: '<S38>/Selector3'
   */
  FlgLcDiag = (uint8_T)(((((StDiag[(DIAG_GEAR_SENSOR)] != FAULT) && (StDiag
    [(DIAG_CLUTCH)] != FAULT)) && (StDiag[(DIAG_VEHSPEED_FRONT)] != FAULT)) &&
    (StDiag[(DIAG_VEHSPEED)] != FAULT)) && (FlgYawRec == 0));

  /* Logic: '<S39>/Logical Operator' incorporates:
   *  Constant: '<S39>/ENLCCTRL'
   *  Constant: '<S48>/Constant'
   *  DataStoreWrite: '<S38>/Data Store Write1'
   *  DataStoreWrite: '<S39>/Data Store Write1'
   *  RelationalOperator: '<S47>/Compare'
   *  RelationalOperator: '<S48>/Compare'
   */
  FlgLcEn = (uint8_T)(((((ENLCCTRL != 0) && (VehSpeedFront == 0)) && (GearPos ==
    0)) && (FlgLcDiag != 0)) && (EndStartFlg != 0));

  /* MinMax: '<S42>/MinMax' incorporates:
   *  Constant: '<S42>/ENLCMAXLEVEL'
   */
  if (ENLCMAXLEVEL < LcLevelCAN) {
    LcLevel = ENLCMAXLEVEL;
  } else {
    LcLevel = LcLevelCAN;
  }

  /* End of MinMax: '<S42>/MinMax' */

  /* DataTypeConversion: '<S45>/Data Type Conversion' */
  rtb_DataTypeConversion_of2 = (int32_T)Odometer;

  /* Chart: '<S45>/Enable_Lc_Km' */
  /* Gateway: LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_TripKm/Enable_Lc_Km */
  /* During: LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_TripKm/Enable_Lc_Km */
  if (LaunchCtrl_DWork.is_active_c1_LaunchCtrl == 0U) {
    /* Entry: LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_TripKm/Enable_Lc_Km */
    LaunchCtrl_DWork.is_active_c1_LaunchCtrl = 1U;

    /* Entry Internal: LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_TripKm/Enable_Lc_Km */
    /* Transition: '<S53>:2' */
    LaunchCtrl_DWork.tmpOdometer = rtb_DataTypeConversion_of2;
    tmp = EELcKm;
    if (EELcKm > 2147483647U) {
      tmp = 2147483647U;
    }

    LaunchCtrl_DWork.offLcKm = (int32_T)tmp;
    LaunchCtrl_DWork.enLcKm = 0U;
    LaunchCtrl_DWork.is_c1_LaunchCtrl = LaunchCtrl_IN_KM;
  } else {
    switch (LaunchCtrl_DWork.is_c1_LaunchCtrl) {
     case LaunchCtrl_IN_KM:
      /* During 'KM': '<S53>:1' */
      /* Transition: '<S53>:5' */
      if ((LaunchCtrl_DWork.tmpOdometer >= 0) && (rtb_DataTypeConversion_of2 <
           (LaunchCtrl_DWork.tmpOdometer - MAX_int32_T))) {
        rtb_DataTypeConversion_of2 = MAX_int32_T;
      } else if ((LaunchCtrl_DWork.tmpOdometer < 0) &&
                 (rtb_DataTypeConversion_of2 > (LaunchCtrl_DWork.tmpOdometer -
                   MIN_int32_T))) {
        rtb_DataTypeConversion_of2 = MIN_int32_T;
      } else {
        rtb_DataTypeConversion_of2 = LaunchCtrl_DWork.tmpOdometer -
          rtb_DataTypeConversion_of2;
      }

      if ((rtb_DataTypeConversion_of2 < 0) && (LaunchCtrl_DWork.offLcKm <
           (MIN_int32_T - rtb_DataTypeConversion_of2))) {
        rtb_DataTypeConversion_of2 = MIN_int32_T;
      } else if ((rtb_DataTypeConversion_of2 > 0) && (LaunchCtrl_DWork.offLcKm >
                  (MAX_int32_T - rtb_DataTypeConversion_of2))) {
        rtb_DataTypeConversion_of2 = MAX_int32_T;
      } else {
        rtb_DataTypeConversion_of2 += LaunchCtrl_DWork.offLcKm;
      }

      if (0 > rtb_DataTypeConversion_of2) {
        rtb_DataTypeConversion_of2 = 0;
      }

      EELcKm = (uint32_T)rtb_DataTypeConversion_of2;
      if ((rtb_DataTypeConversion_of2 <= 0) && (LcTrip != 0)) {
        /* Transition: '<S53>:6' */
        if (LcTrip >= NUMLCTRIP) {
          /* Transition: '<S53>:10' */
          LaunchCtrl_DWork.enLcKm = 1U;
          LaunchCtrl_DWork.is_c1_LaunchCtrl = LaunchCtrl_IN_ONE;
        } else {
          /* Transition: '<S53>:12' */
          if (LcTrip >= (NUMLCTRIP - 1)) {
            /* Transition: '<S53>:13' */
            LaunchCtrl_DWork.enLcKm = 1U;
            LaunchCtrl_DWork.is_c1_LaunchCtrl = LaunchCtrl_IN_TWO;
          } else {
            /* Transition: '<S53>:20' */
            if (LcTrip >= (NUMLCTRIP - 2)) {
              /* Transition: '<S53>:22' */
              LaunchCtrl_DWork.enLcKm = 1U;
              LaunchCtrl_DWork.is_c1_LaunchCtrl = LaunchCtrl_IN_THREE;
            } else {
              /* Transition: '<S53>:23' */
            }
          }
        }
      } else {
        /* Transition: '<S53>:7' */
      }
      break;

     case LaunchCtrl_IN_ONE:
      /* During 'ONE': '<S53>:3' */
      /* Transition: '<S53>:15' */
      if ((TWater < THRTEMPLC) && (LcTrip >= (NUMLCTRIP - 1))) {
        /* Transition: '<S53>:16' */
        LaunchCtrl_DWork.enLcKm = 1U;
        LaunchCtrl_DWork.is_c1_LaunchCtrl = LaunchCtrl_IN_TWO;
      } else {
        if (LcTrip < NUMLCTRIP) {
          /* Transition: '<S53>:17' */
          LaunchCtrl_DWork.tmpOdometer = rtb_DataTypeConversion_of2;
          LaunchCtrl_DWork.offLcKm = NLCKM;
          LaunchCtrl_DWork.enLcKm = 0U;
          LaunchCtrl_DWork.is_c1_LaunchCtrl = LaunchCtrl_IN_KM;
        }
      }
      break;

     case LaunchCtrl_IN_THREE:
      /* During 'THREE': '<S53>:18' */
      /* Transition: '<S53>:29' */
      if (LcTrip < (NUMLCTRIP - 2)) {
        /* Transition: '<S53>:28' */
        LaunchCtrl_DWork.tmpOdometer = rtb_DataTypeConversion_of2;
        LaunchCtrl_DWork.offLcKm = NLCKM;
        LaunchCtrl_DWork.enLcKm = 0U;
        LaunchCtrl_DWork.is_c1_LaunchCtrl = LaunchCtrl_IN_KM;
      } else {
        /* Transition: '<S53>:30' */
      }
      break;

     default:
      /* During 'TWO': '<S53>:8' */
      /* Transition: '<S53>:25' */
      if (LcTrip < (NUMLCTRIP - 1)) {
        /* Transition: '<S53>:26' */
        LaunchCtrl_DWork.tmpOdometer = rtb_DataTypeConversion_of2;
        LaunchCtrl_DWork.offLcKm = NLCKM;
        LaunchCtrl_DWork.enLcKm = 0U;
        LaunchCtrl_DWork.is_c1_LaunchCtrl = LaunchCtrl_IN_KM;
      } else {
        /* Transition: '<S53>:31' */
      }
      break;
    }
  }

  /* End of Chart: '<S45>/Enable_Lc_Km' */

  /* Logic: '<S42>/Logical Operator' incorporates:
   *  Constant: '<S42>/ZERO'
   *  DataStoreWrite: '<S42>/Data Store Write1'
   *  DataStoreWrite: '<S42>/Data Store Write3'
   *  RelationalOperator: '<S42>/Relational Operator'
   */
  FlgLcLevel = (uint8_T)(((LcLevel != 0) && (EndStartFlg != 0)) &&
    (LaunchCtrl_DWork.enLcKm != 0));

  /* If: '<S43>/If' incorporates:
   *  DataStoreRead: '<S43>/Data Store Read'
   */
  if (FlgLcReady != 0) {
    /* Outputs for IfAction SubSystem: '<S43>/Reset_Idle' incorporates:
     *  ActionPort: '<S50>/Action Port'
     */
    /* DataStoreWrite: '<S43>/Data Store Write2' incorporates:
     *  Constant: '<S50>/LCGASIDLE'
     *  Constant: '<S50>/LCGEAREND'
     *  DataStoreWrite: '<S38>/Data Store Write1'
     *  DataStoreWrite: '<S42>/Data Store Write1'
     *  Logic: '<S50>/Logical Operator1'
     *  RelationalOperator: '<S50>/Relational Operator1'
     *  RelationalOperator: '<S50>/Relational Operator2'
     */
    FlgLcReady = (uint8_T)((((FlgLcDiag != 0) && (GearPos < LCGEAREND)) &&
      (GasPosCC >= LCGASIDLE)) && (FlgLcLevel != 0));

    /* End of Outputs for SubSystem: '<S43>/Reset_Idle' */
  } else {
    /* Outputs for IfAction SubSystem: '<S43>/Set_Ready' incorporates:
     *  ActionPort: '<S51>/Action Port'
     */
    /* DataStoreWrite: '<S43>/Data Store Write2' incorporates:
     *  Constant: '<S51>/CLUTCH_DISENGAGED'
     *  Constant: '<S51>/GEAR_1'
     *  Constant: '<S51>/LCGASWOT'
     *  DataStoreWrite: '<S38>/Data Store Write1'
     *  DataStoreWrite: '<S42>/Data Store Write1'
     *  Logic: '<S51>/Logical Operator1'
     *  RelationalOperator: '<S51>/Relational Operator'
     *  RelationalOperator: '<S51>/Relational Operator1'
     *  RelationalOperator: '<S51>/Relational Operator2'
     *  RelationalOperator: '<S52>/Compare'
     */
    FlgLcReady = (uint8_T)((((((FlgLcDiag != 0) && (GasPosCC >= LCGASWOT)) &&
      (FlgLcLevel != 0)) && (GearPos == 1)) && (VehSpeedFront == 0)) &&
      (ClutchSignal == 0));

    /* End of Outputs for SubSystem: '<S43>/Set_Ready' */
  }

  /* End of If: '<S43>/If' */

  /* Logic: '<S41>/Logical Operator1' incorporates:
   *  DataStoreWrite: '<S38>/Data Store Write1'
   *  DataStoreWrite: '<S41>/Data Store Write2'
   *  Logic: '<S41>/Logical Operator'
   */
  FlgLcLaunch = (uint8_T)((FlgLcDiag != 0) && (FlgSpringUp == 0));

  /* Logic: '<S40>/Logical Operator1' incorporates:
   *  Constant: '<S40>/LCGASIDLE'
   *  Constant: '<S40>/LCGEAREND'
   *  Constant: '<S40>/LCVEHSPDEND'
   *  DataStoreWrite: '<S38>/Data Store Write1'
   *  DataStoreWrite: '<S40>/Data Store Write2'
   *  Logic: '<S40>/Logical Operator'
   *  RelationalOperator: '<S40>/Relational Operator'
   *  RelationalOperator: '<S40>/Relational Operator1'
   *  RelationalOperator: '<S40>/Relational Operator2'
   */
  FlgLcEnd = (uint8_T)((((GasPosCC <= LCGASIDLE) || (GearPos >= LCGEAREND)) ||
                        (VehSpeedFront >= LCVEHSPDEND)) || (FlgLcDiag == 0));

  /* Logic: '<S44>/Logical Operator' incorporates:
   *  Constant: '<S44>/LCHYSCMIRET'
   *  DataStoreWrite: '<S44>/Data Store Write2'
   *  RelationalOperator: '<S44>/Relational Operator1'
   *  RelationalOperator: '<S44>/Relational Operator3'
   *  Sum: '<S44>/Add'
   *  Sum: '<S44>/Add1'
   */
  FlgLcRet = (uint8_T)((((int16_T)(CmiTargetP + LCHYSCMIRET)) >= CmiDriverP) &&
                       (((int16_T)(CmiTargetI + LCHYSCMIRET)) >= CmiDriverI));

  /* Logic: '<S44>/Logical Operator1' incorporates:
   *  Constant: '<S44>/LCCMETRGRED'
   *  Constant: '<S44>/LCGASIDLE'
   *  DataStoreRead: '<S44>/Data Store Read'
   *  DataStoreWrite: '<S44>/Data Store Write1'
   *  RelationalOperator: '<S44>/Relational Operator2'
   *  RelationalOperator: '<S44>/Relational Operator4'
   */
  FlgLcCmiLow = (uint8_T)((CmeLcSat <= LCCMETRGRED) || (GasPosCC <= LCGASIDLE));

  /* Switch: '<S28>/Switch1' incorporates:
   *  Constant: '<S28>/Constant'
   *  Constant: '<S28>/Constant1'
   *  Constant: '<S28>/LC_READY'
   *  DataStoreRead: '<S28>/Data Store Read'
   *  DataStoreRead: '<S28>/Data Store Read1'
   *  DataStoreWrite: '<S28>/Data Store Write'
   *  RelationalOperator: '<S28>/Relational Operator'
   *  Sum: '<S28>/Add'
   */
  if (StLc == ((uint8_T)LC_READY)) {
    TimLcToExit = (uint16_T)(TimLcToExit + 1U);
  } else {
    TimLcToExit = 0U;
  }

  /* End of Switch: '<S28>/Switch1' */

  /* Switch: '<S28>/Switch' incorporates:
   *  Constant: '<S28>/Constant2'
   *  Constant: '<S28>/ENLCTOEXITCAN'
   *  DataStoreWrite: '<S28>/Data Store Write'
   *  RelationalOperator: '<S28>/Relational Operator1'
   */
  if (ENLCTOEXITCAN != 0) {
    Switch = (LcToExitCAN != 0);
  } else {
    Switch = (TimLcToExit > 500);
  }

  /* End of Switch: '<S28>/Switch' */

  /* Chart: '<S4>/Chart_LaunchCtrl' incorporates:
   *  DataStoreWrite: '<S42>/Data Store Write3'
   */
  /* Gateway: LaunchCtrl/T10ms/Chart_LaunchCtrl */
  /* During: LaunchCtrl/T10ms/Chart_LaunchCtrl */
  if (LaunchCtrl_DWork.is_active_c3_LaunchCtrl == 0U) {
    /* Entry: LaunchCtrl/T10ms/Chart_LaunchCtrl */
    LaunchCtrl_DWork.is_active_c3_LaunchCtrl = 1U;

    /* Entry Internal: LaunchCtrl/T10ms/Chart_LaunchCtrl */
    /* Entry Internal 'LC_COND_CTRL': '<S29>:73' */
    /* Transition: '<S29>:18' */
    LaunchCtrl_DWork.cnt = 0U;

    /* Outputs for Function Call SubSystem: '<S4>/Lc_Dis' */
    /* Event: '<S29>:128' */
    LaunchCtrl_Lc_Dis();

    /* End of Outputs for SubSystem: '<S4>/Lc_Dis' */
    StLc = ((uint8_T)LC_DISABLE);
    LaunchCtrl_DWork.is_LC_COND_CTRL = LaunchCtrl_IN_LC_DISABLE;

    /* Entry Internal 'LC_OVERRIDE_CTRL': '<S29>:74' */
    /* Transition: '<S29>:178' */
    AwLevel0 = AwLevelCAN;
    SetTracCtrl0 = SetTracCtrlCAN;
    LaunchCtrl_DWork.is_LC_OVERRIDE_CTRL = LaunchCtrl_IN_LC_NONE;
  } else {
    /* DataStoreWrite: '<S43>/Data Store Write2' */
    LaunchCtrl_LC_COND_CTRL((&(FlgLcReady)), &Switch);

    /* During 'LC_OVERRIDE_CTRL': '<S29>:74' */
    switch (LaunchCtrl_DWork.is_LC_OVERRIDE_CTRL) {
     case LaunchCtrl_IN_LC_NONE:
      /* During 'LC_NONE': '<S29>:177' */
      /* Transition: '<S29>:181' */
      if (StLc == ((uint8_T)LC_WAIT_IDLE)) {
        /* Transition: '<S29>:182' */
        AwLevel0 = AwLevelCAN;
        SetTracCtrl0 = SetTracCtrlCAN;
        LaunchCtrl_DWork.is_LC_OVERRIDE_CTRL = Launc_IN_LC_OVERRIDE_PRE_LAUNCH;
      } else {
        /* Transition: '<S29>:183' */
        AwLevel0 = AwLevelCAN;
        SetTracCtrl0 = SetTracCtrlCAN;
      }
      break;

     case LaunchCtr_IN_LC_OVERRIDE_LAUNCH:
      /* During 'LC_OVERRIDE_LAUNCH': '<S29>:196' */
      /* Transition: '<S29>:202' */
      if (StLc == ((uint8_T)LC_RET)) {
        /* Transition: '<S29>:204' */
        /* Transition: '<S29>:214' */
        AwLevel0 = AwLevelCAN;
        SetTracCtrl0 = SetTracCtrlCAN;

        /* Transition: '<S29>:205' */
        LaunchCtrl_DWork.is_LC_OVERRIDE_CTRL = LaunchCtrl_IN_LC_NONE;
      } else {
        /* Transition: '<S29>:206' */
        if (VTAWOVRLEV[LcLevel] > AwLevelCAN) {
          AwLevel0 = VTAWOVRLEV[LcLevel];
        } else {
          AwLevel0 = AwLevelCAN;
        }

        if (VTTCOVRLEV[LcLevel] > SetTracCtrlCAN) {
          SetTracCtrl0 = VTTCOVRLEV[LcLevel];
        } else {
          SetTracCtrl0 = SetTracCtrlCAN;
        }
      }
      break;

     default:
      /* During 'LC_OVERRIDE_PRE_LAUNCH': '<S29>:179' */
      /* Transition: '<S29>:185' */
      if (StLc == ((uint8_T)LC_LAUNCH)) {
        /* Transition: '<S29>:198' */
        if (VTAWOVRLEV[LcLevel] > AwLevelCAN) {
          AwLevel0 = VTAWOVRLEV[LcLevel];
        } else {
          AwLevel0 = AwLevelCAN;
        }

        if (VTTCOVRLEV[LcLevel] > SetTracCtrlCAN) {
          SetTracCtrl0 = VTTCOVRLEV[LcLevel];
        } else {
          SetTracCtrl0 = SetTracCtrlCAN;
        }

        LaunchCtrl_DWork.is_LC_OVERRIDE_CTRL = LaunchCtr_IN_LC_OVERRIDE_LAUNCH;
      } else {
        /* Transition: '<S29>:186' */
        if (StLc == ((uint8_T)LC_RET)) {
          /* Transition: '<S29>:213' */
          /* Transition: '<S29>:214' */
          AwLevel0 = AwLevelCAN;
          SetTracCtrl0 = SetTracCtrlCAN;

          /* Transition: '<S29>:205' */
          LaunchCtrl_DWork.is_LC_OVERRIDE_CTRL = LaunchCtrl_IN_LC_NONE;
        } else {
          /* Transition: '<S29>:211' */
          if ((StLc == ((uint8_T)LC_DISABLE)) || (StLc == ((uint8_T)LC_ENABLING)))
          {
            /* Transition: '<S29>:187' */
            AwLevel0 = AwLevelCAN;
            SetTracCtrl0 = SetTracCtrlCAN;
            LaunchCtrl_DWork.is_LC_OVERRIDE_CTRL = LaunchCtrl_IN_LC_NONE;
          } else {
            /* Transition: '<S29>:200' */
            if (VTAWOVRLEV[0] > AwLevelCAN) {
              AwLevel0 = VTAWOVRLEV[0];
            } else {
              AwLevel0 = AwLevelCAN;
            }

            if (VTTCOVRLEV[0] > SetTracCtrlCAN) {
              SetTracCtrl0 = VTTCOVRLEV[0];
            } else {
              SetTracCtrl0 = SetTracCtrlCAN;
            }
          }
        }
      }
      break;
    }
  }

  /* End of Chart: '<S4>/Chart_LaunchCtrl' */

  /* Switch: '<S26>/Switch1' incorporates:
   *  Constant: '<S26>/ZERO2'
   *  DataStoreWrite: '<S26>/Data Store Write2'
   */
  if (rtb_LogicalOperator1) {
    rtb_LogicalOperator1_0 = 0U;
  } else {
    rtb_LogicalOperator1_0 = EnAwMaxLevel;
  }

  /* End of Switch: '<S26>/Switch1' */

  /* MinMax: '<S26>/MinMax' */
  if (rtb_LogicalOperator1_0 < AwLevel0) {
    AwLevel = rtb_LogicalOperator1_0;
  } else {
    AwLevel = AwLevel0;
  }

  /* End of MinMax: '<S26>/MinMax' */

  /* Logic: '<S26>/Logical Operator' incorporates:
   *  Constant: '<S37>/Constant'
   *  RelationalOperator: '<S37>/Compare'
   */
  rtb_LogicalOperator1 = ((TcDiagRec != 0) || (FlgYawRec == 1));

  /* Switch: '<S26>/Switch2' incorporates:
   *  Constant: '<S26>/VTENTCMAXLEVEL'
   *  DataStoreWrite: '<S26>/Data Store Write5'
   */
  for (rtb_DataTypeConversion_of2 = 0; rtb_DataTypeConversion_of2 < 5;
       rtb_DataTypeConversion_of2++) {
    if (rtb_LogicalOperator1) {
      EnTcMaxLevel[(rtb_DataTypeConversion_of2)] = 9U;
    } else {
      EnTcMaxLevel[(rtb_DataTypeConversion_of2)] =
        VTENTCMAXLEVEL[rtb_DataTypeConversion_of2];
    }
  }

  /* End of Switch: '<S26>/Switch2' */

  /* MinMax: '<S26>/MinMax1' */
  SetTracCtrl = SetTracCtrl0;

  /* Chart: '<S42>/Sel_EnLcMaxLevel' */
  /* Gateway: LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcLevel/Sel_EnLcMaxLevel */
  /* During: LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcLevel/Sel_EnLcMaxLevel */
  /* Entry Internal: LaunchCtrl/T10ms/Calc_Flag_Conditions/Calc_FlgLcLevel/Sel_EnLcMaxLevel */
  /* Transition: '<S49>:2' */
  if ((FlgYawRec != 0) && ((((StLc == ((uint8_T)LC_DISABLE)) || (StLc ==
          ((uint8_T)LC_ENABLING))) || (StLc == ((uint8_T)LC_TO_LIM))) || (StLc ==
        ((uint8_T)LC_TO_IDLE)))) {
    /* Transition: '<S49>:4' */
    AwLevel0 = 1U;
  } else {
    /* Transition: '<S49>:5' */
    AwLevel0 = 0U;
  }

  /* End of Chart: '<S42>/Sel_EnLcMaxLevel' */

  /* Switch: '<S42>/Switch' incorporates:
   *  Constant: '<S42>/ENLCMAXLEVEL'
   *  Constant: '<S42>/ZERO2'
   *  DataStoreWrite: '<S42>/Data Store Write2'
   */
  if (AwLevel0 != 0) {
    EnLcMaxLevel = 0U;
  } else {
    EnLcMaxLevel = ENLCMAXLEVEL;
  }

  /* End of Switch: '<S42>/Switch' */

  /* DataStoreWrite: '<S45>/Data Store Write2' */
  FlgEnLcKm = LaunchCtrl_DWork.enLcKm;

  /* user code (Output function Trailer) */

  /* System '<S1>/T10ms' */

  /* PILOTAGGIO USCITE - 10ms */
}

/* Output and update for function-call system: '<S1>/PreTDC' */
void LaunchCtrl_PreTDC(void)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  int16_T rtb_LookUp_IR_S16;
  int16_T rtb_RateLimiter_S16;
  int16_T rtb_DataStoreRead3;
  boolean_T rtb_LogicalOperator_cnq;
  uint16_T rtb_DataTypeConversion_h2i;
  int16_T rtb_LookUp_S16_S16;
  int16_T rtb_Conversion1[4];
  int16_T rtb_Switch_g3q[4];
  uint8_T rtb_Conversion3_baw;
  uint8_T rtb_DataTypeConversion8;
  int32_T i;

  {
    /* user code (Output function Header) */

    /* System '<S1>/PreTDC' */
    /* INIZIALIZZAZIONE AUTOMA - Funzione RTW */

    /* DataStoreRead: '<S3>/Data Store Read3' */
    rtb_DataStoreRead3 = RpmLcTrgCtf;

    /* If: '<S3>/If' incorporates:
     *  DataStoreRead: '<S3>/Data Store Read'
     */
    if (FlgCtfLc != 0) {
      /* Outputs for IfAction SubSystem: '<S3>/Calc_IdxLcCutOff' incorporates:
       *  ActionPort: '<S10>/Action Port'
       */
      /* Switch: '<S13>/Switch' incorporates:
       *  Constant: '<S18>/Constant'
       *  Constant: '<S19>/Constant'
       *  DataStoreRead: '<S3>/Data Store Read1'
       *  Logic: '<S13>/Logical Operator'
       *  RelationalOperator: '<S18>/Compare'
       *  RelationalOperator: '<S19>/Compare'
       */
      if ((StLc != ((uint8_T)LC_TO_IDLE)) && (StLc != ((uint8_T)LC_TO_LIM))) {
        /* DataTypeConversion: '<S20>/Conversion3' incorporates:
         *  Constant: '<S13>/BKRPMLCTRGWOT_dim'
         */
        rtb_Conversion3_baw = (uint8_T)BKRPMLCTRGWOT_dim;

        /* DataTypeConversion: '<S23>/Data Type Conversion8' incorporates:
         *  Constant: '<S17>/BKRPMLCTRGWOT_dim'
         */
        rtb_DataTypeConversion8 = (uint8_T)BKRPMLCTRGWOT_dim;

        /* Switch: '<S22>/Switch' incorporates:
         *  Constant: '<S22>/LCVEHAXINTSAT'
         *  RelationalOperator: '<S22>/Relational Operator'
         */
        if (VehSpeedFront >= LCVEHAXINTSAT) {
          rtb_DataTypeConversion_h2i = VehSpeedFront;
        } else {
          /* DataTypeConversion: '<S22>/Data Type Conversion' */
          i = VehRbVfAxInt;
          if (VehRbVfAxInt < 0) {
            i = 0;
          } else {
            if (VehRbVfAxInt > 65535) {
              i = 65535;
            }
          }

          rtb_DataTypeConversion_h2i = (uint16_T)i;

          /* End of DataTypeConversion: '<S22>/Data Type Conversion' */

          /* MinMax: '<S22>/MinMax' */
          if (LCVEHAXINTSAT < rtb_DataTypeConversion_h2i) {
            rtb_DataTypeConversion_h2i = LCVEHAXINTSAT;
          }

          /* End of MinMax: '<S22>/MinMax' */

          /* MinMax: '<S22>/MinMax1' incorporates:
           *  DataStoreRead: '<S22>/Data Store Read'
           */
          if (VehSpeedFront > rtb_DataTypeConversion_h2i) {
            rtb_DataTypeConversion_h2i = VehSpeedFront;
          }

          if (rtb_DataTypeConversion_h2i > VehLcAxIntSat) {
          } else {
            rtb_DataTypeConversion_h2i = VehLcAxIntSat;
          }

          /* End of MinMax: '<S22>/MinMax1' */
        }

        /* End of Switch: '<S22>/Switch' */

        /* S-Function (PreLookUpIdSearch_U16): '<S23>/PreLookUpIdSearch_U16' incorporates:
         *  Constant: '<S17>/BKRPMLCTRGWOT'
         */
        PreLookUpIdSearch_U16( &rtb_DataTypeConversion_h2i,
                              &rtb_PreLookUpIdSearch_U16_o2, (uint16_T)
                              rtb_DataTypeConversion_h2i, (uint16_T*)
                              &BKRPMLCTRGWOT[0], (uint8_T)
                              rtb_DataTypeConversion8);

        /* S-Function (LookUp_IR_S16): '<S20>/LookUp_IR_S16' incorporates:
         *  Constant: '<S13>/VTRPMLCTRGWOT'
         */
        LookUp_IR_S16( &rtb_LookUp_IR_S16, (int16_T*)&VTRPMLCTRGWOT[0],
                      (uint16_T)rtb_DataTypeConversion_h2i, (uint16_T)
                      rtb_PreLookUpIdSearch_U16_o2, (uint8_T)rtb_Conversion3_baw);
        rtb_DataStoreRead3 = rtb_LookUp_IR_S16;
      } else {
        /* S-Function (RateLimiter_S16): '<S21>/RateLimiter_S16' incorporates:
         *  Constant: '<S13>/LCRPMTRGRED'
         *  Constant: '<S13>/MAXRTLCRPMTRGRED'
         *  Constant: '<S13>/RTLCRPMTRGRED'
         */
        RateLimiter_S16( &rtb_RateLimiter_S16, (int16_T)LCRPMTRGRED, (int16_T)
                        rtb_DataStoreRead3, (int16_T)RTLCRPMTRGRED, (int16_T)
                        1000);
        rtb_DataStoreRead3 = rtb_RateLimiter_S16;
      }

      /* End of Switch: '<S13>/Switch' */

      /* DataTypeConversion: '<S12>/Data Type Conversion1' */
      rtb_LookUp_S16_S16 = (int16_T)Rpm;

      /* Sum: '<S12>/Add' */
      rtb_LookUp_S16_S16 = (int16_T)(rtb_DataStoreRead3 - rtb_LookUp_S16_S16);

      /* Relay: '<S14>/Relay'
       *
       * Block description for '<S14>/Relay':
       *  max: <DTRPMLCTRGWOT>
       *  min: <HYRPMLCTRGWOT>
       */
      LaunchCtrl_DWork.Relay_Mode = ((rtb_LookUp_S16_S16 >= DTRPMLCTRGWOT) ||
        ((rtb_LookUp_S16_S16 > HYRPMLCTRGWOT) && (LaunchCtrl_DWork.Relay_Mode)));

      /* Logic: '<S14>/Logical Operator' incorporates:
       *  Constant: '<S14>/THLCAXCTF'
       *  RelationalOperator: '<S14>/Relational Operator'
       *  Relay: '<S14>/Relay'
       *
       * Block description for '<S14>/Relay':
       *  max: <DTRPMLCTRGWOT>
       *  min: <HYRPMLCTRGWOT>
       */
      rtb_LogicalOperator_cnq = ((AxCAN > THLCAXCTF) ||
        (!LaunchCtrl_DWork.Relay_Mode));

      /* DataStoreWrite: '<S3>/Data Store Write2' incorporates:
       *  DataTypeConversion: '<S12>/Data Type Conversion3'
       */
      LcRpmErr = rtb_LookUp_S16_S16;
      for (i = 0; i < 4; i++) {
        /* DataTypeConversion: '<S15>/Conversion1' incorporates:
         *  Constant: '<S12>/VTLCIDXCTF'
         *  DataTypeConversion: '<S12>/Data Type Conversion2'
         */
        rtb_Conversion1[i] = VTLCIDXCTF[i];

        /* Switch: '<S12>/Switch' incorporates:
         *  Constant: '<S12>/BKLCIDXCTF'
         *  Constant: '<S12>/BKLCIDXCTF2'
         */
        if (rtb_LogicalOperator_cnq) {
          rtb_Switch_g3q[i] = BKLCIDXCTF2[i];
        } else {
          rtb_Switch_g3q[i] = BKLCIDXCTF[i];
        }

        /* End of Switch: '<S12>/Switch' */
      }

      /* DataTypeConversion: '<S15>/Conversion3' incorporates:
       *  Constant: '<S12>/BKLCIDXCTF_dim'
       */
      rtb_Conversion3_baw = (uint8_T)BKLCIDXCTF_dim;

      /* S-Function (LookUp_S16_S16): '<S15>/LookUp_S16_S16' */
      LookUp_S16_S16( &rtb_LookUp_S16_S16, (int16_T*)&rtb_Conversion1[0],
                     (int16_T)rtb_LookUp_S16_S16, (int16_T*)&rtb_Switch_g3q[0],
                     (uint8_T)rtb_Conversion3_baw);

      /* DataStoreWrite: '<S3>/Data Store Write1' incorporates:
       *  DataTypeConversion: '<S12>/Data Type Conversion4'
       *  DataTypeConversion: '<S16>/Conversion'
       */
      IdxLcCutOff = (uint8_T)rtb_LookUp_S16_S16;

      /* DataStoreWrite: '<S3>/Data Store Write3' incorporates:
       *  DataTypeConversion: '<S12>/Data Type Conversion5'
       */
      RpmLcTrgCtf = rtb_DataStoreRead3;

      /* End of Outputs for SubSystem: '<S3>/Calc_IdxLcCutOff' */
    } else {
      /* Outputs for IfAction SubSystem: '<S3>/Calc_IdxLcCutOff1' incorporates:
       *  ActionPort: '<S11>/Action Port'
       */
      /* DataStoreWrite: '<S3>/Data Store Write1' incorporates:
       *  Constant: '<S11>/ZERO'
       */
      IdxLcCutOff = 0U;

      /* DataStoreWrite: '<S3>/Data Store Write3' incorporates:
       *  Constant: '<S11>/ZERO1'
       */
      RpmLcTrgCtf = 0;

      /* DataStoreWrite: '<S3>/Data Store Write2' incorporates:
       *  Constant: '<S11>/ZERO2'
       */
      LcRpmErr = 0;

      /* End of Outputs for SubSystem: '<S3>/Calc_IdxLcCutOff1' */
    }

    /* End of If: '<S3>/If' */
    /* user code (Output function Trailer) */

    /* System '<S1>/PreTDC' */

    /* PILOTAGGIO USCITE - INIT */
    /* Read static variables */
  }
}

/* Model step function */
void LaunchCtrl_step(void)
{
  /* DataTypeConversion: '<Root>/Data Type Conversion2' incorporates:
   *  Inport: '<Root>/CmiDriverP'
   */
  CmiDriverP = LaunchCtrl_U.CmiDriverP_p4a;

  /* DataTypeConversion: '<Root>/Data Type Conversion6' incorporates:
   *  Inport: '<Root>/CmiDriverI'
   */
  CmiDriverI = LaunchCtrl_U.CmiDriverI_lto;

  /* DataTypeConversion: '<Root>/Data Type Conversion7' incorporates:
   *  Inport: '<Root>/Rpm'
   */
  Rpm = LaunchCtrl_U.Rpm_m33;

  /* DataTypeConversion: '<Root>/Data Type Conversion9' incorporates:
   *  Inport: '<Root>/StDiag'
   */
  memcpy((&(StDiag[0])), &LaunchCtrl_U.StDiag_hib[0], 80U * (sizeof(uint8_T)));

  /* DataTypeConversion: '<Root>/Data Type Conversion3' incorporates:
   *  Inport: '<Root>/GearPos'
   */
  GearPos = LaunchCtrl_U.GearPos_bze;

  /* DataTypeConversion: '<Root>/Data Type Conversion25' incorporates:
   *  Inport: '<Root>/VehSpeedFront'
   */
  VehSpeedFront = LaunchCtrl_U.VehSpeedFront_mjd;

  /* DataTypeConversion: '<Root>/Data Type Conversion8' incorporates:
   *  Inport: '<Root>/LcLevelCAN'
   */
  LcLevelCAN = LaunchCtrl_U.LcLevelCAN_hc3;

  /* DataTypeConversion: '<Root>/Data Type Conversion10' incorporates:
   *  Inport: '<Root>/LcToExitCAN'
   */
  LcToExitCAN = LaunchCtrl_U.LcToExitCAN_non;

  /* DataTypeConversion: '<Root>/Data Type Conversion12' incorporates:
   *  Inport: '<Root>/FlgSpringUp'
   */
  FlgSpringUp = LaunchCtrl_U.FlgSpringUp_apb;

  /* DataTypeConversion: '<Root>/Data Type Conversion17' incorporates:
   *  Inport: '<Root>/GasPosCC'
   */
  GasPosCC = LaunchCtrl_U.GasPosCC_nbq;

  /* DataTypeConversion: '<Root>/Data Type Conversion18' incorporates:
   *  Inport: '<Root>/ClutchSignal'
   */
  ClutchSignal = LaunchCtrl_U.ClutchSignal_ps5;

  /* DataTypeConversion: '<Root>/Data Type Conversion19' incorporates:
   *  Inport: '<Root>/EndStartFlg'
   */
  EndStartFlg = LaunchCtrl_U.EndStartFlg_cdu;

  /* DataTypeConversion: '<Root>/Data Type Conversion13' incorporates:
   *  Inport: '<Root>/CmiTargetP'
   */
  CmiTargetP = LaunchCtrl_U.CmiTargetP_gqa;

  /* DataTypeConversion: '<Root>/Data Type Conversion22' incorporates:
   *  Inport: '<Root>/CmiTargetI'
   */
  CmiTargetI = LaunchCtrl_U.CmiTargetI_kfl;

  /* DataTypeConversion: '<Root>/Data Type Conversion15' incorporates:
   *  Inport: '<Root>/AwLevelCAN'
   */
  AwLevelCAN = LaunchCtrl_U.AwLevelCAN_knf;

  /* DataTypeConversion: '<Root>/Data Type Conversion16' incorporates:
   *  Inport: '<Root>/SetTracCtrlCAN'
   */
  SetTracCtrlCAN = LaunchCtrl_U.SetTracCtrlCAN_pm0;

  /* DataTypeConversion: '<Root>/Data Type Conversion23' incorporates:
   *  Inport: '<Root>/VehRbVfAxInt'
   */
  VehRbVfAxInt = LaunchCtrl_U.VehRbVfAxInt_hyu;

  /* DataTypeConversion: '<Root>/Data Type Conversion26' incorporates:
   *  Inport: '<Root>/AxCAN'
   */
  AxCAN = LaunchCtrl_U.AxCAN_imv;

  /* DataTypeConversion: '<Root>/Data Type Conversion27' incorporates:
   *  Inport: '<Root>/FlgYawRec'
   */
  FlgYawRec = LaunchCtrl_U.FlgYawRec_avt;

  /* DataTypeConversion: '<Root>/Data Type Conversion28' incorporates:
   *  Inport: '<Root>/StSatAw'
   */
  StSatAw = LaunchCtrl_U.StSatAw_dio;

  /* DataTypeConversion: '<Root>/Data Type Conversion29' incorporates:
   *  Inport: '<Root>/TWater'
   */
  TWater = LaunchCtrl_U.TWater_mf3;

  /* DataTypeConversion: '<Root>/Data Type Conversion30' incorporates:
   *  Inport: '<Root>/Odometer'
   */
  Odometer = LaunchCtrl_U.Odometer_lae;

  /* DataTypeConversion: '<Root>/Data Type Conversion31' incorporates:
   *  Inport: '<Root>/CmeDriverP'
   */
  CmeDriverP = LaunchCtrl_U.CmeDriverP_hcq;

  /* DataTypeConversion: '<Root>/Data Type Conversion33' incorporates:
   *  Inport: '<Root>/TcDiagRec'
   */
  TcDiagRec = LaunchCtrl_U.TcDiagRec_a0d;

  /* DataTypeConversion: '<Root>/Data Type Conversion11' incorporates:
   *  Inport: '<Root>/VehSpeedCtrlCAN'
   */
  VehSpeedCtrlCAN = LaunchCtrl_U.VehSpeedCtrlCAN_fee;

  /* Outputs for Atomic SubSystem: '<Root>/LaunchCtrl' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc1' incorporates:
   *  TriggerPort: '<S5>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOn' */
  if ((LaunchCtrl_U.ev_PowerOn > 0) &&
      (LaunchCtrl_PrevZCSigState.trig_to_fc1_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S5>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Init'
     */
    LaunchCtrl_Init();

    /* End of Outputs for S-Function (fcncallgen): '<S5>/Function-Call Generator' */
  }

  LaunchCtrl_PrevZCSigState.trig_to_fc1_Trig_ZCE = (ZCSigState)
    (LaunchCtrl_U.ev_PowerOn > 0);

  /* End of Inport: '<Root>/ev_PowerOn' */
  /* End of Outputs for SubSystem: '<S1>/trig_to_fc1' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc3' incorporates:
   *  TriggerPort: '<S7>/Trigger'
   */
  /* Inport: '<Root>/ev_PreTDC' */
  if ((LaunchCtrl_U.ev_PreTDC > 0) &&
      (LaunchCtrl_PrevZCSigState.trig_to_fc3_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S7>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/PreTDC'
     */
    LaunchCtrl_PreTDC();

    /* End of Outputs for S-Function (fcncallgen): '<S7>/Function-Call Generator' */
  }

  LaunchCtrl_PrevZCSigState.trig_to_fc3_Trig_ZCE = (ZCSigState)
    (LaunchCtrl_U.ev_PreTDC > 0);

  /* End of Inport: '<Root>/ev_PreTDC' */
  /* End of Outputs for SubSystem: '<S1>/trig_to_fc3' */

  /* Outputs for Triggered SubSystem: '<S1>/trig_to_fc2' incorporates:
   *  TriggerPort: '<S6>/Trigger'
   */
  /* Inport: '<Root>/ev_T10ms' */
  if ((LaunchCtrl_U.ev_T10ms > 0) &&
      (LaunchCtrl_PrevZCSigState.trig_to_fc2_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S6>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/T10ms'
     */
    LaunchCtrl_T10ms();

    /* End of Outputs for S-Function (fcncallgen): '<S6>/Function-Call Generator' */
  }

  LaunchCtrl_PrevZCSigState.trig_to_fc2_Trig_ZCE = (ZCSigState)
    (LaunchCtrl_U.ev_T10ms > 0);

  /* End of Inport: '<Root>/ev_T10ms' */
  /* End of Outputs for SubSystem: '<S1>/trig_to_fc2' */

  /* End of Outputs for SubSystem: '<Root>/LaunchCtrl' */

  /* DataTypeConversion: '<Root>/Data Type Conversion32' incorporates:
   *  Inport: '<Root>/RidingMode'
   */
  RidingMode = LaunchCtrl_U.RidingMode_jw4;
}

/* Model initialize function */
void LaunchCtrl_initialize(void)
{
  /* Registration code */

  /* block I/O */

  /* custom signals */
  IDLaunchCtrl = 0U;

  /* states (dwork) */
  (void) memset((void *)&LaunchCtrl_DWork, 0,
                sizeof(D_Work_LaunchCtrl_T));

  /* custom states */
  LcRpmErr = 0;
  RpmLcTrgCtf = 0;
  RpmLcTrgCme = 0U;
  TimLcToExit = 0U;
  CmeLcSat = 0;
  CmeLcSatOffI = 0;
  VehLcAxIntSat = 0U;
  LcTrip = 0;
  StLc = 0U;
  FlgLcCmiLow = 0U;
  FlgLcTrg = 0U;
  FlgLcLevel = 0U;
  FlgLcLim = 0U;
  AwLevel = 0U;
  SetTracCtrl = 0U;
  FlgLcDiag = 0U;
  LcLevel = 0U;
  EnLcMaxLevel = 0U;
  EnAwMaxLevel = 0U;

  {
    int32_T i;
    for (i = 0; i < 5; i++) {
      EnTcMaxLevel[i] = 0U;
    }
  }

  FlgEnLcKm = 0U;
  FlgLcEn = 0U;
  LcActive = 0U;
  FlgLcReady = 0U;
  FlgLcLaunch = 0U;
  FlgLcEnd = 0U;
  FlgLcRet = 0U;
  IdxLcCutOff = 0U;
  FlgCtfLc = 0U;

  /* external inputs */
  (void)memset(&LaunchCtrl_U, 0, sizeof(ExternalInputs_LaunchCtrl_T));
  LaunchCtrl_PrevZCSigState.trig_to_fc1_Trig_ZCE = POS_ZCSIG;
  LaunchCtrl_PrevZCSigState.trig_to_fc2_Trig_ZCE = POS_ZCSIG;
  LaunchCtrl_PrevZCSigState.trig_to_fc3_Trig_ZCE = POS_ZCSIG;

  /* SystemInitialize for Atomic SubSystem: '<Root>/LaunchCtrl' */

  /* SystemInitialize for Triggered SubSystem: '<S1>/trig_to_fc2' */
  /* SystemInitialize for S-Function (fcncallgen): '<S6>/Function-Call Generator' incorporates:
   *  SubSystem: '<S1>/T10ms'
   */
  LaunchCtrl_T10ms_Init();

  /* End of SystemInitialize for S-Function (fcncallgen): '<S6>/Function-Call Generator' */
  /* End of SystemInitialize for SubSystem: '<S1>/trig_to_fc2' */

  /* End of SystemInitialize for SubSystem: '<Root>/LaunchCtrl' */
}

/* user code (bottom of source file) */
/* System '<Root>/LaunchCtrl' */
#endif                                 // _BUILD_LAUNCHCTRL_

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
