/*
 * File: TracCtrl.c
 *
 * Code generated for Simulink model 'TracCtrl'.
 *
 * Model version                  : 1.898
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu Jul 27 09:50:58 2023
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "TracCtrl.h"
#include "TracCtrl_private.h"
#include "mul_s32_loSR_sat.h"

/* Named constants for Chart: '<S50>/init_rate' */
#define TracCtrl_IN_ST_ACC_CONV        ((uint8_T)1U)
#define TracCtrl_IN_ST_ACC_RETURN      ((uint8_T)2U)

/* Named constants for Chart: '<S73>/Prop_Acc_To_Smooth' */
#define TracCtrl_IN_OTHER_TO_ACC       ((uint8_T)1U)
#define TracCtrl_IN_SMOOTH             ((uint8_T)2U)

/* Named constants for Chart: '<S9>/Calc_TracCtrl_DVS' */
#define TracCtrl_IN_CHANGE             ((uint8_T)1U)
#define TracCtrl_IN_GEAR_CHANGE        ((uint8_T)1U)
#define TracCtrl_IN_NO_ACTIVE_CHILD_id ((uint8_T)0U)
#define TracCtrl_IN_NO_CHANGE          ((uint8_T)2U)
#define TracCtrl_IN_OBSERVE_CHANGE     ((uint8_T)1U)
#define TracCtrl_IN_OBSERVE_TC_STATUS  ((uint8_T)1U)
#define TracCtrl_IN_SPRING_UP          ((uint8_T)2U)
#define TracCtrl_IN_TC_ACC             ((uint8_T)1U)
#define TracCtrl_IN_TC_ACC_GEARUP      ((uint8_T)2U)
#define TracCtrl_IN_TC_ACC_GEARUP_FILT ((uint8_T)3U)
#define TracCtrl_IN_TC_DEC             ((uint8_T)5U)
#define TracCtrl_IN_TC_DISABLED        ((uint8_T)1U)
#define TracCtrl_IN_TC_DIS_BY_VEHSPEED ((uint8_T)2U)
#define TracCtrl_IN_TC_ENABLED         ((uint8_T)2U)
#define TracCtrl_IN_TC_GEAR_DISABLE    ((uint8_T)2U)
#define TracCtrl_IN_TC_REC_SMOOTH      ((uint8_T)3U)
#define TracCtrl_IN_TC_SMOOTH          ((uint8_T)4U)
#define TracCtrl_IN_TC_WAIT_ACC        ((uint8_T)6U)

/* Named constants for Chart: '<S98>/Chart' */
#define TracCtrl_IN_CUSTOM_MAP_VEH     ((uint8_T)1U)
#define TracCtrl_IN_FO_ERROR           ((uint8_T)2U)
#define TracCtrl_IN_NOT_CUSTOM         ((uint8_T)3U)

/* user code (top of source file) */
/* System '<Root>/TracCtrl' */
#ifdef _BUILD_TRACCTRL_

extern uint8_T StDiag[DIAG_NUMBER];

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_TracCtrl TracCtrl_PrevZCSigState;

/* External inputs (root inport signals with default storage) */
ExternalInputs_TracCtrl TracCtrl_U;

/* Exported data definition */

/* Const memory section */
/* Definition for custom storage class: Const */
const uint16_T BK_0_2_TC[3] = { 0U, 1U, 2U } ;

const uint16_T BK_0_7_TC[8] = { 0U, 1U, 2U, 3U, 4U, 5U, 6U, 7U } ;

/* Definition for custom storage class: ExportToFile */
int16_T AccDVSpdCtrl;

/* Acc DeltaVehSpeed error */
int16_T AccDVSpdCtrlF;

/* Acc DeltaVehSpeed error filtered */
int16_T AccDVSpdCtrlFMem;

/* Acc DeltaVehSpeed error filtered */
int16_T AccDeltaVehSpeedFO;

/* AccDeltaVehSpeed Forced */
int16_T CmeTcSmooth;

/* Cme SmoothRatio */
uint16_T CmeTcSmoothCorr;

/* Cme SmoothRatio */
uint16_T CmeTcSmoothStp;

/* Cme SmoothRatio */
int16_T CmiSmoothRatio;

/* Cmi SmoothRatio */
int16_T CmiTracCtrl;

/* Cmi saturation for TC */
int16_T CmiTracCtrlI;

/* Cmi saturation for TC */
int16_T CmiTracCtrlInitI;

/* CmiTracCtrl init value - I torque */
int16_T CmiTracCtrlInitP;

/* CmiTracCtrl init value - P torque */
int16_T CmiTracCtrlP;

/* Cmi saturation for TC */
int16_T CmiTracI;

/* Instantaneous CMI target after TC */
int16_T CmiTracINoSat;

/* Instantaneous CMI target after TC - not saturated */
int16_T CmiTracP;

/* Predicted CMI target after TC */
int16_T CmiTracPNoSat;

/* Predicted CMI target after TC - not saturated */
uint32_T CntTcSmooth;

/* TracCtrl smooth */
int32_T CntTimSmooth;

/* Counter to select smooth gain */
int16_T DVSCtrlErr;

/* error */
int16_T DVSCtrlErrMax;

/* Delta Error Max */
int16_T DVSModel;

/* Delta VehSpeed threshold model */
int16_T DVSModelGain;

/* Gain DVSModel */
int16_T DVSRollCorr;

/* Delta VehSpeed target value correction for RollCAN */
int16_T DVSSmInt;

/* Delta VehSpeed smooth int */
int16_T DVSSmIntGain;

/* Delta VehSpeed smooth int gain */
int16_T DVSSmoothLev;

/* Delta VehSpeed threshold smooth */
int16_T DVSTarg;

/* Delata VehSpeed target value */
int16_T DeltaVehSpeedFO;

/* DeltaVehSpeed Forced */
uint8_T DiagFlg01FO;

/* Forced flag */
uint8_T FlgDisGear;

/* Traction control disabled for gear change */
uint8_T FlgTracCtrl;

/* Traction control presence flag */
uint8_T FlgYawRecFO;

/* Forced flag */
uint16_T GainTcCtf;

/* gain */
int16_T GainTcDrift;

/* gain */
uint32_T IDTracCtrlVer;

/* ID Version */
uint16_T IdTracCtrl;

/* SetTracCtrl converted as index */
uint16_T IdTracCtrlRid;

/* SetTracCtrl converted as index reduced */
uint8_T IdxTcCutOff;

/* Index of Trac CutOff */
int16_T OffCMITRACI;

/* Offset I */
int16_T OffCMITRACP;

/* Offset P */
uint8_T StTcGear;

/* Clutch_Gear control state */
int8_T StTracCtrl;

/* Traction control state */
int8_T StTracCtrlTrn;

/* Traction control state */
int32_T TCIntTerm;

/* TCIntTerm */
int32_T TCPropITerm;

/* TCPropITerm */
int32_T TCPropPTerm;

/* TCPropPTerm */
int16_T TbThrDvSpd;

/* gain */
int16_T TbThrDvSpdComp;

/* gain */
uint8_T TcDiagRec;

/* Tc Disable for External Recovery */
uint8_T TrgAccVSMax;

/* Acc max */
uint8_T flg_end_acc;

/* End acc by cmi */
uint8_T flg_end_smooth;

/* End Smooth */
int32_T tcintfactdvs;

/* tcintfact */

/* Forward declaration for local functions */
static void TracCtrl_TC_ENABLED(const int16_T *Merge);

/* Declare variables for internal data of system '<S1>/T10ms' */
rtB_T10ms_TracCtrl TracCtrl_T10ms_B;
rtDW_T10ms_TracCtrl TracCtrl_T10ms_DW;

/* Output and update for function-call system: '<S9>/fc_TC_AccWheelCalc' */
void TracCtrl_fc_TC_AccWheelCalc(uint8_T rtu_flg_reset_filt, uint8_T
  rtu_TyreTypeCAN, int16_T rtu_CmeEstWheelF, int16_T rtu_AccDeltaVehSpeed,
  int16_T rtu_RollCAN, uint16_T rtu_Rpm, uint16_T rtu_VehSpeedFront, int16_T
  rtu_DeltaVehSpeed, int16_T rtu_DeltaCmeEstWheelF, uint8_T rtu_FlgYawRec,
  uint8_T rtu_SetTracCtrl, int16_T rtu_WzCAN, rtB_fc_TC_AccWheelCalc_TracCtrl
  *localB, const rtC_fc_TC_AccWheelCalc_TracCtrl *localC,
  rtDW_fc_TC_AccWheelCalc_TracCtr *localDW, uint16_T *rtd_index_RollCAN,
  uint16_T *rtd_ratio_RollCAN)
{
  /* local block i/o variables */
  int32_T rtb_FOF_Reset_S16_FXP_o2;
  uint16_T rtb_PreLookUpIdSearch_U16_o1;
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_PreLookUpIdSearch_S16_o1;
  uint16_T rtb_PreLookUpIdSearch_S16_o2;
  uint16_T rtb_PreLookUpIdSearch_S16_o1_l;
  uint16_T rtb_PreLookUpIdSearch_S16_o2_g;
  uint16_T rtb_Look2D_U16_S16_U16;
  uint16_T rtb_Look2D_U16_S16_U16_i;
  uint16_T rtb_Look2D_U16_S16_U16_a;
  uint16_T rtb_PreLookUpIdSearch_S16_o1_j;
  int16_T rtb_Look2D_IR_S16_b;
  int16_T rtb_Look2D_IR_S16_k;
  int16_T rtb_Look2D_IR_S16_n;
  int16_T rtb_Look2D_IR_S16_a;
  int16_T rtb_Product1_nm;
  boolean_T rtb_RelationalOperator_b;
  boolean_T rtb_LogicalOperator_kx;
  boolean_T rtb_MultiportSwitch_l;
  uint8_T rtb_flg_rate_stop;
  uint8_T rtb_Conversion7_m3;
  int32_T rtb_Memory;
  uint16_T rtb_PreLookUpIdSearch_S16_o2_m;
  int16_T rtb_Look2D_IR_S16_g;
  int16_T rtb_Selector_j;
  uint16_T rtb_Conversion1[3];
  uint16_T rtb_Sum9;
  int16_T rtb_Abs_d;
  int16_T rtb_CmiRpmLimitNoSat1_d;
  uint16_T rtb_Conversion5_j[15];

  /* Sum: '<S20>/Sum9' incorporates:
   *  Constant: '<S20>/ONE'
   */
  rtb_Memory = rtu_SetTracCtrl - 1;
  if (rtb_Memory < 0) {
    rtb_Memory = 0;
  }

  rtb_Sum9 = (uint16_T)rtb_Memory;

  /* End of Sum: '<S20>/Sum9' */

  /* DataTypeConversion: '<S60>/Conversion6' incorporates:
   *  Constant: '<S56>/BKIDTRACCTRL_dim'
   */
  rtb_Conversion7_m3 = (uint8_T)((uint16_T)BKIDTRACCTRL_dim);

  /* Memory: '<S49>/Memory' */
  rtb_Memory = localDW->Memory_PreviousInput;

  /* S-Function (FOF_Reset_S16_FXP): '<S54>/FOF_Reset_S16_FXP' incorporates:
   *  Constant: '<S49>/KFILTACCDVS'
   */
  FOF_Reset_S16_FXP( &rtb_Product1_nm, &rtb_FOF_Reset_S16_FXP_o2,
                    rtu_AccDeltaVehSpeed, KFILTACCDVS, rtu_AccDeltaVehSpeed,
                    rtu_flg_reset_filt, rtb_Memory);

  /* DataTypeConversion: '<S49>/Data Type Conversion3' incorporates:
   *  Constant: '<S49>/DELTAACCDVSMAX'
   */
  rtb_PreLookUpIdSearch_S16_o2_m = DELTAACCDVSMAX;
  if (DELTAACCDVSMAX > 32767) {
    rtb_PreLookUpIdSearch_S16_o2_m = 32767U;
  }

  rtb_Abs_d = (int16_T)rtb_PreLookUpIdSearch_S16_o2_m;

  /* End of DataTypeConversion: '<S49>/Data Type Conversion3' */

  /* MinMax: '<S49>/MinMax1' */
  if (rtb_Product1_nm < rtb_Abs_d) {
    rtb_Look2D_IR_S16_g = rtb_Product1_nm;
  } else {
    rtb_Look2D_IR_S16_g = rtb_Abs_d;
  }

  /* End of MinMax: '<S49>/MinMax1' */

  /* Gain: '<S49>/Gain' */
  rtb_Product1_nm = (int16_T)-rtb_Abs_d;

  /* MinMax: '<S49>/MinMax2' incorporates:
   *  DataStoreWrite: '<S49>/Data Store Write4'
   */
  if (rtb_Look2D_IR_S16_g > rtb_Product1_nm) {
    AccDVSpdCtrlF = rtb_Look2D_IR_S16_g;
  } else {
    AccDVSpdCtrlF = rtb_Product1_nm;
  }

  /* End of MinMax: '<S49>/MinMax2' */

  /* RelationalOperator: '<S48>/Relational Operator' incorporates:
   *  Constant: '<S48>/TC_ACC'
   *  DataStoreRead: '<S48>/cmirpmlimiter_old2'
   */
  rtb_RelationalOperator_b = (StTracCtrl != ((int8_T)TC_ACC));

  /* Logic: '<S48>/Logical Operator' incorporates:
   *  Constant: '<S48>/TC_ACC_GEARUP'
   *  Constant: '<S48>/TC_ACC_GEARUP_FILT'
   *  Constant: '<S48>/TC_SMOOTH'
   *  DataStoreRead: '<S48>/cmirpmlimiter_old2'
   *  RelationalOperator: '<S48>/Relational Operator1'
   *  RelationalOperator: '<S48>/Relational Operator2'
   *  RelationalOperator: '<S48>/Relational Operator3'
   */
  rtb_LogicalOperator_kx = (rtb_RelationalOperator_b && (StTracCtrl != ((int8_T)
    TC_SMOOTH)) && (StTracCtrl != ((int8_T)TC_ACC_GEARUP_FILT)) && (StTracCtrl
    != ((int8_T)TC_ACC_GEARUP)));

  /* MultiPortSwitch: '<S48>/Multiport Switch' incorporates:
   *  Constant: '<S48>/ALL'
   *  Constant: '<S48>/SELACCDVSPDFRZ'
   */
  switch (SELACCDVSPDFRZ) {
   case 0:
    rtb_MultiportSwitch_l = true;
    break;

   case 1:
    rtb_MultiportSwitch_l = rtb_RelationalOperator_b;
    break;

   default:
    rtb_MultiportSwitch_l = rtb_LogicalOperator_kx;
    break;
  }

  /* End of MultiPortSwitch: '<S48>/Multiport Switch' */

  /* Switch: '<S48>/Switch3' incorporates:
   *  DataStoreRead: '<S21>/cmirpmlimiter_old3'
   *  DataStoreWrite: '<S49>/Data Store Write4'
   *  MinMax: '<S48>/MinMax1'
   */
  if (rtb_MultiportSwitch_l) {
    rtb_Abs_d = AccDVSpdCtrlF;
  } else if (AccDVSpdCtrlF > AccDVSpdCtrlFMem) {
    /* MinMax: '<S48>/MinMax1' incorporates:
     *  DataStoreWrite: '<S49>/Data Store Write4'
     */
    rtb_Abs_d = AccDVSpdCtrlF;
  } else {
    rtb_Abs_d = AccDVSpdCtrlFMem;
  }

  /* End of Switch: '<S48>/Switch3' */

  /* Chart: '<S50>/init_rate' */
  /* Gateway: TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_DeltaAcc/AccDVSpdCtrlFMem_Rate/Return_AccDVSpdCtrl/init_rate */
  /* During: TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_DeltaAcc/AccDVSpdCtrlFMem_Rate/Return_AccDVSpdCtrl/init_rate */
  if (localDW->bitsForTID0.is_active_c7_TracCtrl == 0U) {
    /* Entry: TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_DeltaAcc/AccDVSpdCtrlFMem_Rate/Return_AccDVSpdCtrl/init_rate */
    localDW->bitsForTID0.is_active_c7_TracCtrl = 1U;

    /* Entry Internal: TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_AccWheelCalc/Calc_DeltaAcc/AccDVSpdCtrlFMem_Rate/Return_AccDVSpdCtrl/init_rate */
    /* Transition: '<S52>:2' */
    localDW->bitsForTID0.is_c7_TracCtrl = TracCtrl_IN_ST_ACC_CONV;

    /* Switch: '<S50>/Switch2' */
    rtb_Look2D_IR_S16_g = rtb_Abs_d;
  } else if (localDW->bitsForTID0.is_c7_TracCtrl == TracCtrl_IN_ST_ACC_CONV) {
    /* During 'ST_ACC_CONV': '<S52>:1' */
    /* Transition: '<S52>:9' */
    if (!rtb_RelationalOperator_b) {
      /* Transition: '<S52>:10' */
      localDW->bitsForTID0.is_c7_TracCtrl = TracCtrl_IN_ST_ACC_RETURN;

      /* Switch: '<S50>/Switch2' incorporates:
       *  DataStoreRead: '<S21>/cmirpmlimiter_old3'
       */
      rtb_Look2D_IR_S16_g = AccDVSpdCtrlFMem;
    } else {
      /* Transition: '<S52>:11' */
      /* Switch: '<S50>/Switch2' */
      rtb_Look2D_IR_S16_g = rtb_Abs_d;
    }
  } else {
    /* During 'ST_ACC_RETURN': '<S52>:3' */
    /* Transition: '<S52>:6' */
    if ((rtb_Abs_d >= AccDVSpdCtrlFMem) && rtb_LogicalOperator_kx) {
      /* Transition: '<S52>:5' */
      localDW->bitsForTID0.is_c7_TracCtrl = TracCtrl_IN_ST_ACC_CONV;

      /* Switch: '<S50>/Switch2' */
      rtb_Look2D_IR_S16_g = rtb_Abs_d;
    } else {
      /* Transition: '<S52>:7' */
      /* Switch: '<S50>/Switch2' incorporates:
       *  DataStoreRead: '<S21>/cmirpmlimiter_old3'
       */
      rtb_Look2D_IR_S16_g = AccDVSpdCtrlFMem;
    }
  }

  /* End of Chart: '<S50>/init_rate' */

  /* RelationalOperator: '<S22>/Relational Operator1' incorporates:
   *  Constant: '<S22>/SETTRACCTRL2'
   */
  rtb_flg_rate_stop = (uint8_T)(rtu_SetTracCtrl > SETTRACCTRL2);

  /* Sum: '<S22>/Sum5' incorporates:
   *  Constant: '<S22>/SETTRACCTRL1'
   *  RelationalOperator: '<S22>/Relational Operator'
   *  Sum: '<S22>/Sum6'
   */
  localB->Sum5 = (uint16_T)((uint32_T)(rtu_SetTracCtrl > SETTRACCTRL1) +
    rtb_flg_rate_stop);

  /* Selector: '<S50>/Selector' incorporates:
   *  Constant: '<S50>/VTMINRTACCDVSFM'
   */
  rtb_Selector_j = VTMINRTACCDVSFM[localB->Sum5];

  /* S-Function (RateLimiter_S16): '<S51>/RateLimiter_S16' incorporates:
   *  Constant: '<S50>/MAX_RATE'
   */
  RateLimiter_S16( &rtb_Look2D_IR_S16_g, rtb_Abs_d, rtb_Look2D_IR_S16_g,
                  rtb_Selector_j, 16384);

  /* DataTypeConversion: '<S53>/Conversion' */
  localB->Conversion = rtb_Look2D_IR_S16_g;

  /* DataTypeConversion: '<S60>/Conversion2' */
  rtb_PreLookUpIdSearch_S16_o2_m = (uint16_T)localB->Conversion;

  /* DataTypeConversion: '<S60>/Conversion1' incorporates:
   *  Constant: '<S56>/BKGNACCTCCTF'
   */
  rtb_Conversion1[0] = (uint16_T)BKGNACCTCCTF[0];
  rtb_Conversion1[1] = (uint16_T)BKGNACCTCCTF[1];
  rtb_Conversion1[2] = (uint16_T)BKGNACCTCCTF[2];

  /* DataTypeConversion: '<S60>/Conversion7' incorporates:
   *  Constant: '<S56>/BKGNACCTCCTF_dim'
   */
  rtb_flg_rate_stop = (uint8_T)((uint16_T)BKGNACCTCCTF_dim);

  /* S-Function (Look2D_U8_U16_U16): '<S60>/Look2D_U8_U16_U16' incorporates:
   *  Constant: '<S56>/BK_0_7_TC'
   *  Constant: '<S56>/TBGAINTCCTF'
   */
  Look2D_U8_U16_U16( &rtb_PreLookUpIdSearch_S16_o2_m, &TBGAINTCCTF[0], rtb_Sum9,
                    (&(BK_0_7_TC[0])), rtb_Conversion7_m3,
                    rtb_PreLookUpIdSearch_S16_o2_m, &rtb_Conversion1[0],
                    rtb_flg_rate_stop);

  /* DataTypeConversion: '<S56>/Data Type Conversion' incorporates:
   *  DataStoreWrite: '<S23>/Data Store Write1'
   */
  GainTcCtf = (uint16_T)((uint32_T)rtb_PreLookUpIdSearch_S16_o2_m >> 8);

  /* DataTypeConversion: '<S67>/Data Type Conversion8' incorporates:
   *  Constant: '<S24>/BKDCMEESTWHEEL_dim'
   */
  rtb_flg_rate_stop = (uint8_T)((uint16_T)BKDCMEESTWHEEL_dim);

  /* S-Function (PreLookUpIdSearch_S16): '<S67>/PreLookUpIdSearch_S16' incorporates:
   *  Constant: '<S24>/BKDCMEESTWHEEL'
   */
  PreLookUpIdSearch_S16( &rtb_PreLookUpIdSearch_S16_o2_m,
                        &rtb_PreLookUpIdSearch_S16_o1_j, rtu_DeltaCmeEstWheelF,
                        &BKDCMEESTWHEEL[0], rtb_flg_rate_stop);

  /* DataTypeConversion: '<S40>/Conversion6' incorporates:
   *  Constant: '<S29>/BKDCMEESTWHEEL_dim'
   */
  rtb_flg_rate_stop = (uint8_T)((uint16_T)BKDCMEESTWHEEL_dim);

  /* DataTypeConversion: '<S70>/Data Type Conversion8' incorporates:
   *  Constant: '<S24>/BKIDTRACCTRLRID_dim'
   */
  rtb_Conversion7_m3 = (uint8_T)((uint16_T)BKIDTRACCTRLRID_dim);

  /* S-Function (PreLookUpIdSearch_U16): '<S70>/PreLookUpIdSearch_U16' */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1,
                        &rtb_PreLookUpIdSearch_U16_o2, localB->Sum5,
                        &localC->DataTypeConversion[0], rtb_Conversion7_m3);

  /* DataTypeConversion: '<S40>/Conversion7' incorporates:
   *  Constant: '<S29>/BKIDTRACCTRLRID_dim'
   */
  rtb_Conversion7_m3 = (uint8_T)((uint16_T)BKIDTRACCTRLRID_dim);

  /* S-Function (Look2D_IR_S16): '<S40>/Look2D_IR_S16' incorporates:
   *  Constant: '<S29>/TBTHRDVSPDGAIN'
   */
  Look2D_IR_S16( &rtb_Look2D_IR_S16_g, &TBTHRDVSPDGAIN[0],
                rtb_PreLookUpIdSearch_S16_o2_m, rtb_PreLookUpIdSearch_S16_o1_j,
                rtb_flg_rate_stop, rtb_PreLookUpIdSearch_U16_o1,
                rtb_PreLookUpIdSearch_U16_o2, rtb_Conversion7_m3);

  /* DataTypeConversion: '<S65>/Data Type Conversion8' incorporates:
   *  Constant: '<S24>/BKCMEESTWHEEL_dim'
   */
  rtb_flg_rate_stop = (uint8_T)((uint16_T)BKCMEESTWHEEL_dim);

  /* S-Function (PreLookUpIdSearch_S16): '<S65>/PreLookUpIdSearch_S16' incorporates:
   *  Constant: '<S24>/BKCMEESTWHEEL'
   */
  PreLookUpIdSearch_S16( &rtb_PreLookUpIdSearch_S16_o1,
                        &rtb_PreLookUpIdSearch_S16_o2, rtu_CmeEstWheelF,
                        &BKCMEESTWHEEL[0], rtb_flg_rate_stop);

  /* Switch: '<S24>/Switch2' incorporates:
   *  Constant: '<S24>/ROLL_REC_40DEG'
   *  Constant: '<S64>/Constant'
   *  RelationalOperator: '<S64>/Compare'
   */
  if (rtu_FlgYawRec == 1) {
    rtb_Memory = ((int16_T)ROLL_REC_40DEG) * 100;
    if (rtb_Memory > 32767) {
      rtb_Memory = 32767;
    } else {
      if (rtb_Memory < -32768) {
        rtb_Memory = -32768;
      }
    }

    rtb_Abs_d = (int16_T)rtb_Memory;
  } else {
    rtb_Abs_d = rtu_RollCAN;
  }

  /* End of Switch: '<S24>/Switch2' */

  /* Abs: '<S24>/Abs' */
  if (rtb_Abs_d < 0) {
    rtb_Abs_d = (int16_T)-rtb_Abs_d;
  }

  /* End of Abs: '<S24>/Abs' */

  /* DataTypeConversion: '<S68>/Data Type Conversion8' incorporates:
   *  Constant: '<S24>/BKROLLANGLETARGCORR_dim'
   */
  rtb_flg_rate_stop = (uint8_T)((uint16_T)BKROLLANGLETARGCORR_dim);

  /* S-Function (PreLookUpIdSearch_S16): '<S68>/PreLookUpIdSearch_S16' incorporates:
   *  Constant: '<S24>/BKROLLANGLETARGCORR'
   */
  PreLookUpIdSearch_S16( &rtb_PreLookUpIdSearch_S16_o1_l,
                        &rtb_PreLookUpIdSearch_S16_o2_g, rtb_Abs_d,
                        &BKROLLANGLETARGCORR[0], rtb_flg_rate_stop);

  /* MultiPortSwitch: '<S29>/Multiport Switch' incorporates:
   *  DataStoreWrite: '<S29>/Data Store Write1'
   */
  switch (localB->Sum5) {
   case 0:
    /* DataTypeConversion: '<S38>/Conversion7' incorporates:
     *  Constant: '<S29>/BKROLLANGLETARGCORR_dim1'
     */
    rtb_Conversion7_m3 = (uint8_T)((uint16_T)BKROLLANGLETARGCORR_dim);

    /* DataTypeConversion: '<S38>/Conversion6' incorporates:
     *  Constant: '<S29>/BKCMEESTWHEEL_dim'
     */
    rtb_flg_rate_stop = (uint8_T)((uint16_T)BKCMEESTWHEEL_dim);

    /* S-Function (Look2D_IR_S16): '<S38>/Look2D_IR_S16' incorporates:
     *  Constant: '<S29>/TBTHRDVSPD0'
     */
    Look2D_IR_S16( &rtb_Look2D_IR_S16_b, &TBTHRDVSPD0[0],
                  rtb_PreLookUpIdSearch_S16_o1, rtb_PreLookUpIdSearch_S16_o2,
                  rtb_flg_rate_stop, rtb_PreLookUpIdSearch_S16_o1_l,
                  rtb_PreLookUpIdSearch_S16_o2_g, rtb_Conversion7_m3);
    TbThrDvSpd = rtb_Look2D_IR_S16_b;
    break;

   case 1:
    /* DataTypeConversion: '<S39>/Conversion7' incorporates:
     *  Constant: '<S29>/BKROLLANGLETARGCORR_dim1'
     */
    rtb_Conversion7_m3 = (uint8_T)((uint16_T)BKROLLANGLETARGCORR_dim);

    /* DataTypeConversion: '<S39>/Conversion6' incorporates:
     *  Constant: '<S29>/BKCMEESTWHEEL_dim'
     */
    rtb_flg_rate_stop = (uint8_T)((uint16_T)BKCMEESTWHEEL_dim);

    /* S-Function (Look2D_IR_S16): '<S39>/Look2D_IR_S16' incorporates:
     *  Constant: '<S29>/TBTHRDVSPD1'
     */
    Look2D_IR_S16( &rtb_Look2D_IR_S16_k, &TBTHRDVSPD1[0],
                  rtb_PreLookUpIdSearch_S16_o1, rtb_PreLookUpIdSearch_S16_o2,
                  rtb_flg_rate_stop, rtb_PreLookUpIdSearch_S16_o1_l,
                  rtb_PreLookUpIdSearch_S16_o2_g, rtb_Conversion7_m3);
    TbThrDvSpd = rtb_Look2D_IR_S16_k;
    break;

   default:
    /* DataTypeConversion: '<S41>/Conversion7' incorporates:
     *  Constant: '<S29>/BKROLLANGLETARGCORR_dim1'
     */
    rtb_Conversion7_m3 = (uint8_T)((uint16_T)BKROLLANGLETARGCORR_dim);

    /* DataTypeConversion: '<S41>/Conversion6' incorporates:
     *  Constant: '<S29>/BKCMEESTWHEEL_dim'
     */
    rtb_flg_rate_stop = (uint8_T)((uint16_T)BKCMEESTWHEEL_dim);

    /* S-Function (Look2D_IR_S16): '<S41>/Look2D_IR_S16' incorporates:
     *  Constant: '<S29>/TBTHRDVSPD2'
     */
    Look2D_IR_S16( &rtb_Look2D_IR_S16_n, &TBTHRDVSPD2[0],
                  rtb_PreLookUpIdSearch_S16_o1, rtb_PreLookUpIdSearch_S16_o2,
                  rtb_flg_rate_stop, rtb_PreLookUpIdSearch_S16_o1_l,
                  rtb_PreLookUpIdSearch_S16_o2_g, rtb_Conversion7_m3);
    TbThrDvSpd = rtb_Look2D_IR_S16_n;
    break;
  }

  /* End of MultiPortSwitch: '<S29>/Multiport Switch' */

  /* Switch: '<S29>/Switch' incorporates:
   *  DataStoreWrite: '<S29>/Data Store Write1'
   *  DataStoreWrite: '<S29>/Data Store Write9'
   *  Product: '<S29>/Product1'
   */
  if (rtu_TyreTypeCAN != 0) {
    /* DataTypeConversion: '<S42>/Conversion7' incorporates:
     *  Constant: '<S29>/BKIDTRACCTRLRID_dim'
     */
    rtb_Conversion7_m3 = (uint8_T)((uint16_T)BKIDTRACCTRLRID_dim);

    /* DataTypeConversion: '<S42>/Conversion6' incorporates:
     *  Constant: '<S29>/BKROLLANGLETARGCORR_dim1'
     */
    rtb_flg_rate_stop = (uint8_T)((uint16_T)BKROLLANGLETARGCORR_dim);

    /* S-Function (Look2D_IR_S16): '<S42>/Look2D_IR_S16' incorporates:
     *  Constant: '<S29>/TBTCTYREOPTGAIN'
     */
    Look2D_IR_S16( &rtb_Look2D_IR_S16_a, &TBTCTYREOPTGAIN[0],
                  rtb_PreLookUpIdSearch_S16_o1_l, rtb_PreLookUpIdSearch_S16_o2_g,
                  rtb_flg_rate_stop, rtb_PreLookUpIdSearch_U16_o1,
                  rtb_PreLookUpIdSearch_U16_o2, rtb_Conversion7_m3);
    TbThrDvSpdComp = (int16_T)((TbThrDvSpd * rtb_Look2D_IR_S16_a) >> 7);
  } else {
    TbThrDvSpdComp = TbThrDvSpd;
  }

  /* End of Switch: '<S29>/Switch' */

  /* Product: '<S29>/Product' incorporates:
   *  DataStoreWrite: '<S20>/Data Store Write8'
   *  DataStoreWrite: '<S29>/Data Store Write9'
   */
  DVSModel = (int16_T)((rtb_Look2D_IR_S16_g * TbThrDvSpdComp) >> 7);

  /* DataTypeConversion: '<S69>/Data Type Conversion8' incorporates:
   *  Constant: '<S24>/BKVEHSPEEDTRAC_dim'
   */
  rtb_flg_rate_stop = (uint8_T)((uint16_T)BKVEHSPEEDTRAC_dim);

  /* S-Function (PreLookUpIdSearch_U16): '<S69>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S24>/BKVEHSPEEDTRAC'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_S16_o1_j,
                        &rtb_PreLookUpIdSearch_S16_o2_m, rtu_VehSpeedFront,
                        &BKVEHSPEEDTRAC[0], rtb_flg_rate_stop);

  /* DataTypeConversion: '<S32>/Conversion6' incorporates:
   *  Constant: '<S28>/BKVEHSPEEDTRAC_dim'
   */
  rtb_flg_rate_stop = (uint8_T)((uint16_T)BKVEHSPEEDTRAC_dim);

  /* DataTypeConversion: '<S32>/Conversion7' incorporates:
   *  Constant: '<S28>/BKIDTRACCTRL_dim'
   */
  rtb_Conversion7_m3 = (uint8_T)((uint16_T)BKIDTRACCTRL_dim);

  /* S-Function (Look2D_IR_S16): '<S32>/Look2D_IR_S16' incorporates:
   *  Constant: '<S28>/TBDVSTARG'
   *  Constant: '<S28>/ZERO2'
   */
  Look2D_IR_S16( &rtb_Product1_nm, &TBDVSTARG[0], rtb_PreLookUpIdSearch_S16_o1_j,
                rtb_PreLookUpIdSearch_S16_o2_m, rtb_flg_rate_stop, rtb_Sum9,
                ((uint16_T)0U), rtb_Conversion7_m3);

  /* DataTypeConversion: '<S33>/Conversion6' incorporates:
   *  Constant: '<S28>/BKROLLANGLETARGCORR_dim'
   */
  rtb_flg_rate_stop = (uint8_T)((uint16_T)BKROLLANGLETARGCORR_dim);

  /* DataTypeConversion: '<S33>/Conversion7' incorporates:
   *  Constant: '<S28>/BKIDTRACCTRL_dim'
   */
  rtb_Conversion7_m3 = (uint8_T)((uint16_T)BKIDTRACCTRL_dim);

  /* S-Function (Look2D_IR_U16): '<S33>/Look2D_IR_U16' incorporates:
   *  Constant: '<S28>/TBDVSTARGCORR'
   *  Constant: '<S28>/ZERO2'
   */
  Look2D_IR_U16( &rtb_PreLookUpIdSearch_S16_o1_j, &TBDVSTARGCORR[0],
                rtb_PreLookUpIdSearch_S16_o1_l, rtb_PreLookUpIdSearch_S16_o2_g,
                rtb_flg_rate_stop, rtb_Sum9, ((uint16_T)0U), rtb_Conversion7_m3);

  /* DataTypeConversion: '<S28>/Conversion3' */
  rtb_Selector_j = (int16_T)rtb_PreLookUpIdSearch_S16_o1_j;

  /* DataStoreRead: '<S28>/CmiRpmLimitNoSat1' */
  rtb_CmiRpmLimitNoSat1_d = DVSRollCorr;

  /* S-Function (RateLimiter_S16): '<S34>/RateLimiter_S16' incorporates:
   *  Constant: '<S28>/DVSTARGRATEMAX'
   *  Constant: '<S28>/DVSTARGRATEMIN'
   */
  RateLimiter_S16( &rtb_Selector_j, rtb_Selector_j, rtb_CmiRpmLimitNoSat1_d,
                  DVSTARGRATEMIN, DVSTARGRATEMAX);

  /* Product: '<S28>/Product' */
  rtb_Memory = rtb_Product1_nm * rtb_Selector_j;

  /* Sum: '<S28>/Sum1' incorporates:
   *  DataStoreWrite: '<S20>/Data Store Write8'
   *  Product: '<S28>/Product'
   */
  rtb_Memory = (int16_T)(((rtb_Memory < 0 ? 127 : 0) + rtb_Memory) >> 7) +
    DVSModel;
  if (rtb_Memory > 32767) {
    rtb_Memory = 32767;
  } else {
    if (rtb_Memory < -32768) {
      rtb_Memory = -32768;
    }
  }

  rtb_Product1_nm = (int16_T)rtb_Memory;

  /* End of Sum: '<S28>/Sum1' */

  /* MinMax: '<S28>/MinMax' incorporates:
   *  Constant: '<S28>/ZERO1'
   *  DataStoreWrite: '<S20>/Data Store Write4'
   */
  if (rtb_Product1_nm > 0) {
    DVSTarg = rtb_Product1_nm;
  } else {
    DVSTarg = 0;
  }

  /* End of MinMax: '<S28>/MinMax' */

  /* Sum: '<S25>/Sum2' incorporates:
   *  DataStoreWrite: '<S20>/Data Store Write4'
   */
  rtb_Memory = rtu_DeltaVehSpeed - DVSTarg;
  if (rtb_Memory > 32767) {
    rtb_Memory = 32767;
  } else {
    if (rtb_Memory < -32768) {
      rtb_Memory = -32768;
    }
  }

  localB->Sum2 = (int16_T)rtb_Memory;

  /* End of Sum: '<S25>/Sum2' */

  /* Switch: '<S23>/Switch3' incorporates:
   *  Constant: '<S23>/TC_ACC'
   *  Constant: '<S23>/TC_ACC_GEARUP_FILT'
   *  Constant: '<S23>/ZERO_IDX'
   *  DataStoreRead: '<S23>/cmirpmlimiter_old3'
   *  DataStoreWrite: '<S23>/Data Store Write7'
   *  Logic: '<S23>/Logical Operator'
   *  RelationalOperator: '<S23>/Relational Operator'
   *  RelationalOperator: '<S23>/Relational Operator3'
   */
  if ((StTracCtrl == ((int8_T)TC_ACC)) || (StTracCtrl == ((int8_T)
        TC_ACC_GEARUP_FILT))) {
    /* MultiPortSwitch: '<S23>/Multiport Switch' incorporates:
     *  DataTypeConversion: '<S61>/Conversion'
     *  DataTypeConversion: '<S62>/Conversion'
     *  DataTypeConversion: '<S63>/Conversion'
     */
    switch (localB->Sum5) {
     case 0:
      /* DataTypeConversion: '<S59>/Conversion7' incorporates:
       *  Constant: '<S23>/BKTCRPMCUTOFF_dim'
       */
      rtb_Conversion7_m3 = (uint8_T)((uint16_T)BKTCRPMCUTOFF_dim);

      /* DataTypeConversion: '<S59>/Conversion6' incorporates:
       *  Constant: '<S23>/BKDVSERRCUTOFF_dim'
       */
      rtb_flg_rate_stop = (uint8_T)((uint16_T)BKDVSERRCUTOFF_dim);

      /* DataTypeConversion: '<S59>/Conversion5' incorporates:
       *  Constant: '<S23>/TBIDXTCDVSCUTOFF0'
       */
      for (rtb_Memory = 0; rtb_Memory < 15; rtb_Memory++) {
        rtb_Conversion5_j[rtb_Memory] = TBIDXTCDVSCUTOFF0[rtb_Memory];
      }

      /* End of DataTypeConversion: '<S59>/Conversion5' */

      /* S-Function (Look2D_U16_S16_U16): '<S59>/Look2D_U16_S16_U16' incorporates:
       *  Constant: '<S23>/BKDVSERRCUTOFF'
       *  Constant: '<S23>/BKTCRPMCUTOFF'
       */
      Look2D_U16_S16_U16( &rtb_Look2D_U16_S16_U16, &rtb_Conversion5_j[0],
                         localB->Sum2, &BKDVSERRCUTOFF[0], rtb_flg_rate_stop,
                         rtu_Rpm, &BKTCRPMCUTOFF[0], rtb_Conversion7_m3);
      rtb_Conversion7_m3 = (uint8_T)rtb_Look2D_U16_S16_U16;
      break;

     case 1:
      /* DataTypeConversion: '<S57>/Conversion7' incorporates:
       *  Constant: '<S23>/BKTCRPMCUTOFF_dim'
       */
      rtb_Conversion7_m3 = (uint8_T)((uint16_T)BKTCRPMCUTOFF_dim);

      /* DataTypeConversion: '<S57>/Conversion6' incorporates:
       *  Constant: '<S23>/BKDVSERRCUTOFF_dim'
       */
      rtb_flg_rate_stop = (uint8_T)((uint16_T)BKDVSERRCUTOFF_dim);

      /* DataTypeConversion: '<S57>/Conversion5' incorporates:
       *  Constant: '<S23>/TBIDXTCDVSCUTOFF1'
       */
      for (rtb_Memory = 0; rtb_Memory < 15; rtb_Memory++) {
        rtb_Conversion5_j[rtb_Memory] = TBIDXTCDVSCUTOFF1[rtb_Memory];
      }

      /* End of DataTypeConversion: '<S57>/Conversion5' */

      /* S-Function (Look2D_U16_S16_U16): '<S57>/Look2D_U16_S16_U16' incorporates:
       *  Constant: '<S23>/BKDVSERRCUTOFF'
       *  Constant: '<S23>/BKTCRPMCUTOFF'
       */
      Look2D_U16_S16_U16( &rtb_Look2D_U16_S16_U16_i, &rtb_Conversion5_j[0],
                         localB->Sum2, &BKDVSERRCUTOFF[0], rtb_flg_rate_stop,
                         rtu_Rpm, &BKTCRPMCUTOFF[0], rtb_Conversion7_m3);
      rtb_Conversion7_m3 = (uint8_T)rtb_Look2D_U16_S16_U16_i;
      break;

     default:
      /* DataTypeConversion: '<S58>/Conversion7' incorporates:
       *  Constant: '<S23>/BKTCRPMCUTOFF_dim'
       */
      rtb_Conversion7_m3 = (uint8_T)((uint16_T)BKTCRPMCUTOFF_dim);

      /* DataTypeConversion: '<S58>/Conversion6' incorporates:
       *  Constant: '<S23>/BKDVSERRCUTOFF_dim'
       */
      rtb_flg_rate_stop = (uint8_T)((uint16_T)BKDVSERRCUTOFF_dim);

      /* DataTypeConversion: '<S58>/Conversion5' incorporates:
       *  Constant: '<S23>/TBIDXTCDVSCUTOFF2'
       */
      for (rtb_Memory = 0; rtb_Memory < 15; rtb_Memory++) {
        rtb_Conversion5_j[rtb_Memory] = TBIDXTCDVSCUTOFF2[rtb_Memory];
      }

      /* End of DataTypeConversion: '<S58>/Conversion5' */

      /* S-Function (Look2D_U16_S16_U16): '<S58>/Look2D_U16_S16_U16' incorporates:
       *  Constant: '<S23>/BKDVSERRCUTOFF'
       *  Constant: '<S23>/BKTCRPMCUTOFF'
       */
      Look2D_U16_S16_U16( &rtb_Look2D_U16_S16_U16_a, &rtb_Conversion5_j[0],
                         localB->Sum2, &BKDVSERRCUTOFF[0], rtb_flg_rate_stop,
                         rtu_Rpm, &BKTCRPMCUTOFF[0], rtb_Conversion7_m3);
      rtb_Conversion7_m3 = (uint8_T)rtb_Look2D_U16_S16_U16_a;
      break;
    }

    /* End of MultiPortSwitch: '<S23>/Multiport Switch' */

    /* Product: '<S23>/Product' incorporates:
     *  DataStoreWrite: '<S23>/Data Store Write1'
     */
    rtb_flg_rate_stop = (uint8_T)(((uint32_T)GainTcCtf * rtb_Conversion7_m3) >>
      4);

    /* MultiPortSwitch: '<S23>/Multiport Switch2' incorporates:
     *  Constant: '<S23>/IDXTCDVSCUTOFF0'
     *  Constant: '<S23>/IDXTCDVSCUTOFF1'
     *  Constant: '<S23>/IDXTCDVSCUTOFF2'
     */
    switch (rtb_Sum9) {
     case 0:
      rtb_Conversion7_m3 = IDXTCDVSCUTOFF0;

      /* MultiPortSwitch: '<S23>/Multiport Switch1' incorporates:
       *  Constant: '<S23>/IDXTCDVSCUTOFF0'
       *  Constant: '<S23>/THRTCWZCTF0'
       */
      rtb_Product1_nm = THRTCWZCTF0;
      break;

     case 1:
      rtb_Conversion7_m3 = IDXTCDVSCUTOFF1;

      /* MultiPortSwitch: '<S23>/Multiport Switch1' incorporates:
       *  Constant: '<S23>/IDXTCDVSCUTOFF1'
       *  Constant: '<S23>/THRTCWZCTF1'
       */
      rtb_Product1_nm = THRTCWZCTF1;
      break;

     default:
      rtb_Conversion7_m3 = IDXTCDVSCUTOFF2;

      /* MultiPortSwitch: '<S23>/Multiport Switch1' incorporates:
       *  Constant: '<S23>/IDXTCDVSCUTOFF2'
       *  Constant: '<S23>/THRTCWZCTF2'
       */
      rtb_Product1_nm = THRTCWZCTF2;
      break;
    }

    /* End of MultiPortSwitch: '<S23>/Multiport Switch2' */

    /* Abs: '<S23>/Abs' */
    if (rtu_WzCAN < 0) {
      rtb_CmiRpmLimitNoSat1_d = (int16_T)-rtu_WzCAN;
    } else {
      rtb_CmiRpmLimitNoSat1_d = rtu_WzCAN;
    }

    /* End of Abs: '<S23>/Abs' */

    /* MinMax: '<S23>/MinMax1' incorporates:
     *  Product: '<S23>/Product1'
     *  RelationalOperator: '<S23>/Relational Operator1'
     */
    rtb_Conversion7_m3 = (uint8_T)(rtb_Product1_nm < rtb_CmiRpmLimitNoSat1_d ?
      (int32_T)rtb_Conversion7_m3 : 0);
    if (rtb_Conversion7_m3 > rtb_flg_rate_stop) {
      rtb_flg_rate_stop = rtb_Conversion7_m3;
    }

    /* End of MinMax: '<S23>/MinMax1' */

    /* MinMax: '<S23>/MinMax' */
    if (7 < rtb_flg_rate_stop) {
      IdxTcCutOff = 7U;
    } else {
      IdxTcCutOff = rtb_flg_rate_stop;
    }

    /* End of MinMax: '<S23>/MinMax' */
  } else {
    IdxTcCutOff = 0U;
  }

  /* End of Switch: '<S23>/Switch3' */

  /* Product: '<S26>/Product1' incorporates:
   *  Constant: '<S26>/VTGNSMOOTLEVEL'
   *  DataStoreWrite: '<S20>/Data Store Write4'
   *  Selector: '<S26>/Selector1'
   */
  rtb_Memory = VTGNSMOOTLEVEL[localB->Sum5] * DVSTarg;
  rtb_Product1_nm = (int16_T)(((rtb_Memory < 0 ? 15 : 0) + rtb_Memory) >> 4);

  /* Sum: '<S26>/Add' incorporates:
   *  Constant: '<S26>/ONE'
   *  Constant: '<S26>/VTGNSMOOTLEVEL'
   *  Selector: '<S26>/Selector1'
   */
  rtb_CmiRpmLimitNoSat1_d = (int16_T)(32 - VTGNSMOOTLEVEL[localB->Sum5]);

  /* Product: '<S26>/Product2' incorporates:
   *  DataStoreWrite: '<S20>/Data Store Write8'
   */
  rtb_Memory = rtb_CmiRpmLimitNoSat1_d * DVSModel;

  /* Sum: '<S26>/Sum3' incorporates:
   *  DataStoreWrite: '<S20>/Data Store Write1'
   *  Product: '<S26>/Product2'
   */
  DVSSmoothLev = (int16_T)(((int16_T)(((rtb_Memory < 0 ? 15 : 0) + rtb_Memory) >>
    4) + rtb_Product1_nm) >> 1);

  /* DataStoreWrite: '<S20>/Data Store Write2' */
  DVSCtrlErr = localB->Sum2;

  /* MultiPortSwitch: '<S27>/Multiport Switch' incorporates:
   *  Constant: '<S27>/OFFCMITRACP0'
   *  Constant: '<S27>/OFFCMITRACP1'
   *  Constant: '<S27>/OFFCMITRACP2'
   *  DataStoreWrite: '<S20>/Data Store Write3'
   */
  switch (localB->Sum5) {
   case 0:
    OffCMITRACP = OFFCMITRACP0;
    break;

   case 1:
    OffCMITRACP = OFFCMITRACP1;
    break;

   default:
    OffCMITRACP = OFFCMITRACP2;
    break;
  }

  /* End of MultiPortSwitch: '<S27>/Multiport Switch' */

  /* DataTypeConversion: '<S66>/Data Type Conversion8' incorporates:
   *  Constant: '<S24>/BKROLLTCINIT_dim'
   */
  rtb_flg_rate_stop = (uint8_T)((uint16_T)BKROLLTCINIT_dim);

  /* S-Function (PreLookUpIdSearch_S16): '<S66>/PreLookUpIdSearch_S16' incorporates:
   *  Constant: '<S24>/BKROLLTCINIT'
   */
  PreLookUpIdSearch_S16( &rtb_PreLookUpIdSearch_S16_o1_j,
                        &rtb_PreLookUpIdSearch_S16_o2_m, rtb_Abs_d,
                        &BKROLLTCINIT[0], rtb_flg_rate_stop);

  /* DataTypeConversion: '<S30>/Conversion6' incorporates:
   *  Constant: '<S27>/BKROLLTCINIT_dim'
   */
  rtb_flg_rate_stop = (uint8_T)((uint16_T)BKROLLTCINIT_dim);

  /* DataTypeConversion: '<S30>/Conversion7' incorporates:
   *  Constant: '<S27>/BKIDTRACCTRLRID_dim'
   */
  rtb_Conversion7_m3 = (uint8_T)((uint16_T)BKIDTRACCTRLRID_dim);

  /* S-Function (Look2D_IR_S16): '<S30>/Look2D_IR_S16' incorporates:
   *  Constant: '<S27>/TBOFFCMITRACI'
   */
  Look2D_IR_S16( &rtb_CmiRpmLimitNoSat1_d, &TBOFFCMITRACI[0],
                rtb_PreLookUpIdSearch_S16_o1_j, rtb_PreLookUpIdSearch_S16_o2_m,
                rtb_flg_rate_stop, rtb_PreLookUpIdSearch_U16_o1,
                rtb_PreLookUpIdSearch_U16_o2, rtb_Conversion7_m3);

  /* DataStoreWrite: '<S20>/Data Store Write5' */
  OffCMITRACI = rtb_CmiRpmLimitNoSat1_d;

  /* DataStoreWrite: '<S20>/Data Store Write6' */
  IdTracCtrl = rtb_Sum9;

  /* DataStoreWrite: '<S20>/Data Store Write7' */
  DVSModelGain = rtb_Look2D_IR_S16_g;

  /* DataStoreWrite: '<S28>/Data Store Write5' */
  DVSRollCorr = rtb_Selector_j;

  /* Switch: '<S20>/Switch' incorporates:
   *  Constant: '<S20>/SELTHSMOOTHEXIT'
   *  DataStoreWrite: '<S20>/Data Store Write1'
   *  DataStoreWrite: '<S20>/Data Store Write8'
   */
  if (SELTHSMOOTHEXIT != 0) {
    localB->Switch = DVSModel;
  } else {
    localB->Switch = DVSSmoothLev;
  }

  /* End of Switch: '<S20>/Switch' */

  /* RelationalOperator: '<S48>/Relational Operator4' incorporates:
   *  DataStoreWrite: '<S21>/Data Store Write2'
   *  DataStoreWrite: '<S49>/Data Store Write4'
   */
  TrgAccVSMax = (uint8_T)(AccDVSpdCtrlF <= localB->Conversion);

  /* DataStoreWrite: '<S21>/Data Store Write3' */
  AccDVSpdCtrl = rtu_AccDeltaVehSpeed;

  /* DataStoreWrite: '<S21>/Data Store Write4' */
  AccDVSpdCtrlFMem = localB->Conversion;

  /* DataStoreWrite: '<S22>/Data Store Write2' */
  IdTracCtrlRid = localB->Sum5;

  /* DataStoreWrite: '<S24>/Data Store Write1' */
  *rtd_ratio_RollCAN = rtb_PreLookUpIdSearch_S16_o2_g;

  /* DataStoreWrite: '<S24>/Data Store Write4' */
  *rtd_index_RollCAN = rtb_PreLookUpIdSearch_S16_o1_l;

  /* Update for Memory: '<S49>/Memory' */
  localDW->Memory_PreviousInput = rtb_FOF_Reset_S16_FXP_o2;
}

/*
 * Output and update for atomic system:
 *    '<S73>/Prop_Acc_To_Smooth'
 *    '<S74>/Prop_Acc_To_Smooth'
 */
void TracCtrl_Prop_Acc_To_Smooth(int32_T rtu_TCPropPTerm_in,
  rtB_Prop_Acc_To_Smooth_TracCtrl *localB, rtDW_Prop_Acc_To_Smooth_TracCtr
  *localDW)
{
  /* Chart: '<S73>/Prop_Acc_To_Smooth' */
  /* Gateway: TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_CmiControl/Calc_VTKPTCGAINI/Prop_Acc_To_Smooth */
  /* During: TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_CmiControl/Calc_VTKPTCGAINI/Prop_Acc_To_Smooth */
  if (localDW->bitsForTID0.is_active_c8_TracCtrl == 0U) {
    /* Entry: TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_CmiControl/Calc_VTKPTCGAINI/Prop_Acc_To_Smooth */
    localDW->bitsForTID0.is_active_c8_TracCtrl = 1U;

    /* Entry Internal: TracCtrl/T10ms/DeltaVehSpeed_Model/fc_TC_CmiControl/Calc_VTKPTCGAINI/Prop_Acc_To_Smooth */
    /* Transition: '<S81>:3' */
    localB->TCPropPTerm_out = rtu_TCPropPTerm_in;
    localDW->bitsForTID0.is_c8_TracCtrl = TracCtrl_IN_OTHER_TO_ACC;
  } else if (localDW->bitsForTID0.is_c8_TracCtrl == TracCtrl_IN_OTHER_TO_ACC) {
    /* During 'OTHER_TO_ACC': '<S81>:2' */
    /* Transition: '<S81>:9' */
    if (StTracCtrlTrn == ((int8_T)TC_SMOOTH)) {
      /* Transition: '<S81>:10' */
      if (rtu_TCPropPTerm_in < 0) {
        localB->TCPropPTerm_out = rtu_TCPropPTerm_in;
      } else {
        localB->TCPropPTerm_out = 0;
      }

      localDW->bitsForTID0.is_c8_TracCtrl = TracCtrl_IN_SMOOTH;
    } else {
      /* Transition: '<S81>:11' */
      localB->TCPropPTerm_out = rtu_TCPropPTerm_in;
    }
  } else {
    /* During 'SMOOTH': '<S81>:1' */
    /* Transition: '<S81>:6' */
    if ((StTracCtrlTrn != ((int8_T)TC_SMOOTH)) && (TrgAccVSMax == 0)) {
      /* Transition: '<S81>:5' */
      localB->TCPropPTerm_out = rtu_TCPropPTerm_in;
      localDW->bitsForTID0.is_c8_TracCtrl = TracCtrl_IN_OTHER_TO_ACC;
    } else {
      /* Transition: '<S81>:7' */
      if (rtu_TCPropPTerm_in < 0) {
        localB->TCPropPTerm_out = rtu_TCPropPTerm_in;
      } else {
        localB->TCPropPTerm_out = 0;
      }
    }
  }

  /* End of Chart: '<S73>/Prop_Acc_To_Smooth' */
}

/* Output and update for function-call system: '<S9>/fc_TC_CmiControl' */
void TracCtrl_fc_TC_CmiControl(int16_T rtu_AccDVSpdCtrlFMem, int16_T
  rtu_DVSCtrlErr, uint8_T rtu_localgear, uint16_T rtu_IdTracCtrlRid, uint16_T
  rtu_VehSpeedFront, rtB_fc_TC_CmiControl_TracCtrl *localB, const
  rtC_fc_TC_CmiControl_TracCtrl *localC, rtDW_fc_TC_CmiControl_TracCtrl *localDW,
  uint16_T *rtd_index_RollCAN, uint16_T *rtd_ratio_RollCAN)
{
  /* local block i/o variables */
  int16_T rtb_Look2D_S16_U16_U16;
  int16_T rtb_Look2D_IR_S16_m;
  int16_T rtb_Look2D_IR_S16_d;
  int16_T rtb_Sum8;
  int32_T rtb_Switch1;
  uint16_T rtb_DataStoreRead_n;
  uint8_T rtb_Conversion6_c;
  uint8_T rtb_Conversion7_k;
  uint16_T rtb_cmirpmlimiter_old6;
  int32_T q1;

  /* DataStoreRead: '<S71>/Data Store Read' */
  rtb_DataStoreRead_n = IdTracCtrl;

  /* DataTypeConversion: '<S77>/Conversion7' incorporates:
   *  Constant: '<S71>/BKVEHTCDRIFT_dim'
   */
  rtb_Conversion6_c = (uint8_T)((uint16_T)BKVEHTCDRIFT_dim);

  /* S-Function (Look2D_S16_U16_U16): '<S77>/Look2D_S16_U16_U16' incorporates:
   *  Constant: '<S71>/BKVEHTCDRIFT'
   *  Constant: '<S71>/BK_0_2_TC'
   *  Constant: '<S71>/TBGAINTCDRIFT'
   */
  Look2D_S16_U16_U16( &rtb_Look2D_S16_U16_U16, &TBGAINTCDRIFT[0],
                     rtb_DataStoreRead_n, (&(BK_0_2_TC[0])), localC->Conversion6,
                     rtu_VehSpeedFront, &BKVEHTCDRIFT[0], rtb_Conversion6_c);

  /* DataStoreWrite: '<S71>/Data Store Write1' */
  GainTcDrift = rtb_Look2D_S16_U16_U16;

  /* DataTypeConversion: '<S87>/Conversion6' incorporates:
   *  Constant: '<S76>/BKDVSERR_dim'
   */
  rtb_Conversion6_c = (uint8_T)((uint16_T)BKDVSERR_dim);

  /* DataTypeConversion: '<S87>/Conversion7' incorporates:
   *  Constant: '<S76>/BKACCDVS_dim'
   */
  rtb_Conversion7_k = (uint8_T)((uint16_T)BKACCDVS_dim);

  /* S-Function (Look2D_S16_S16_S16): '<S87>/Look2D_S16_S16_S16' incorporates:
   *  Constant: '<S76>/BKACCDVS'
   *  Constant: '<S76>/BKDVSERR'
   *  Constant: '<S76>/TBGAINCMITRACDVS'
   */
  Look2D_S16_S16_S16( &rtb_Sum8, &TBGAINCMITRACDVS[0], rtu_DVSCtrlErr,
                     &BKDVSERR[0], rtb_Conversion6_c, rtu_AccDVSpdCtrlFMem,
                     &BKACCDVS[0], rtb_Conversion7_k);

  /* Product: '<S76>/Product2' */
  rtb_Switch1 = (rtb_Sum8 * rtb_Look2D_S16_U16_U16) >> 6;
  if (rtb_Switch1 > 32767) {
    rtb_Switch1 = 32767;
  } else {
    if (rtb_Switch1 < -32768) {
      rtb_Switch1 = -32768;
    }
  }

  /* Product: '<S76>/Product1' incorporates:
   *  DataStoreRead: '<S76>/CmiRpmLimitNoSat1'
   *  Product: '<S76>/Product2'
   */
  rtb_Switch1 = (CmiTracCtrlInitP * rtb_Switch1) >> 5;
  if (rtb_Switch1 > 32767) {
    rtb_Switch1 = 32767;
  } else {
    if (rtb_Switch1 < -32768) {
      rtb_Switch1 = -32768;
    }
  }

  rtb_Sum8 = (int16_T)rtb_Switch1;

  /* End of Product: '<S76>/Product1' */

  /* Switch: '<S73>/Switch1' incorporates:
   *  Constant: '<S73>/KPTRACLEVIDX'
   *  Constant: '<S79>/Constant'
   *  DataStoreRead: '<S73>/cmirpmlimiter_old3'
   *  DataStoreRead: '<S73>/cmirpmlimiter_old5'
   *  Logic: '<S73>/Logical Operator'
   *  Product: '<S73>/Product1'
   *  RelationalOperator: '<S73>/Relational Operator'
   *  RelationalOperator: '<S79>/Compare'
   */
  if ((IdxTcCutOff == 0) || (IdxTcCutOff >= KPTRACLEVIDX)) {
    /* DataTypeConversion: '<S80>/Conversion7' incorporates:
     *  Constant: '<S73>/BKTCLEVIDX_dim'
     */
    rtb_Conversion6_c = (uint8_T)((uint16_T)BKTCLEVIDX_dim);

    /* DataTypeConversion: '<S80>/Conversion6' incorporates:
     *  Constant: '<S73>/BKROLLANGLETARGCORR_dim'
     */
    rtb_Conversion7_k = (uint8_T)((uint16_T)BKROLLANGLETARGCORR_dim);

    /* DataStoreRead: '<S73>/cmirpmlimiter_old8' */
    rtb_DataStoreRead_n = *rtd_ratio_RollCAN;

    /* DataStoreRead: '<S73>/cmirpmlimiter_old6' */
    rtb_cmirpmlimiter_old6 = *rtd_index_RollCAN;

    /* S-Function (Look2D_IR_S16): '<S80>/Look2D_IR_S16' incorporates:
     *  Constant: '<S73>/TBKPTCGAINI'
     *  Constant: '<S73>/ZERO'
     */
    Look2D_IR_S16( &rtb_Look2D_IR_S16_d, &TBKPTCGAINI[0], rtb_cmirpmlimiter_old6,
                  rtb_DataStoreRead_n, rtb_Conversion7_k, rtu_IdTracCtrlRid,
                  ((uint16_T)0U), rtb_Conversion6_c);
    rtb_Switch1 = rtb_Sum8 * rtb_Look2D_IR_S16_d;
  } else {
    rtb_Switch1 = TCPropITerm;
  }

  /* End of Switch: '<S73>/Switch1' */

  /* Chart: '<S73>/Prop_Acc_To_Smooth' */
  TracCtrl_Prop_Acc_To_Smooth(rtb_Switch1, &localB->sf_Prop_Acc_To_Smooth,
    &localDW->sf_Prop_Acc_To_Smooth);

  /* DataStoreWrite: '<S73>/Data Store Write1' */
  TCPropITerm = localB->sf_Prop_Acc_To_Smooth.TCPropPTerm_out;

  /* Switch: '<S74>/Switch' incorporates:
   *  Constant: '<S74>/KPTRACLEVIDX'
   *  Constant: '<S83>/Constant'
   *  DataStoreRead: '<S74>/cmirpmlimiter_old1'
   *  DataStoreRead: '<S74>/cmirpmlimiter_old3'
   *  Logic: '<S74>/Logical Operator'
   *  Product: '<S74>/Product3'
   *  RelationalOperator: '<S74>/Relational Operator'
   *  RelationalOperator: '<S83>/Compare'
   */
  if ((IdxTcCutOff == 0) || (IdxTcCutOff >= KPTRACLEVIDX)) {
    /* DataTypeConversion: '<S84>/Conversion7' incorporates:
     *  Constant: '<S74>/BKTCLEVIDX_dim'
     */
    rtb_Conversion6_c = (uint8_T)((uint16_T)BKTCLEVIDX_dim);

    /* DataTypeConversion: '<S84>/Conversion6' incorporates:
     *  Constant: '<S74>/BKROLLANGLETARGCORR_dim'
     */
    rtb_Conversion7_k = (uint8_T)((uint16_T)BKROLLANGLETARGCORR_dim);

    /* DataStoreRead: '<S74>/cmirpmlimiter_old4' */
    rtb_DataStoreRead_n = *rtd_ratio_RollCAN;

    /* DataStoreRead: '<S74>/cmirpmlimiter_old2' */
    rtb_cmirpmlimiter_old6 = *rtd_index_RollCAN;

    /* S-Function (Look2D_IR_S16): '<S84>/Look2D_IR_S16' incorporates:
     *  Constant: '<S74>/TBKPTCGAINP'
     *  Constant: '<S74>/ZERO'
     */
    Look2D_IR_S16( &rtb_Look2D_IR_S16_m, &TBKPTCGAINP[0], rtb_cmirpmlimiter_old6,
                  rtb_DataStoreRead_n, rtb_Conversion7_k, rtu_IdTracCtrlRid,
                  ((uint16_T)0U), rtb_Conversion6_c);
    rtb_Switch1 = rtb_Sum8 * rtb_Look2D_IR_S16_m;
  } else {
    rtb_Switch1 = TCPropPTerm;
  }

  /* End of Switch: '<S74>/Switch' */

  /* Chart: '<S74>/Prop_Acc_To_Smooth' */
  TracCtrl_Prop_Acc_To_Smooth(rtb_Switch1, &localB->sf_Prop_Acc_To_Smooth_o,
    &localDW->sf_Prop_Acc_To_Smooth_o);

  /* DataStoreWrite: '<S74>/Data Store Write5' */
  TCPropPTerm = localB->sf_Prop_Acc_To_Smooth_o.TCPropPTerm_out;

  /* Product: '<S75>/Product5' incorporates:
   *  Constant: '<S75>/KITRACCTRL'
   */
  tcintfactdvs = (KITRACCTRL * rtu_DVSCtrlErr) >> 4;

  /* Switch: '<S75>/Switch' incorporates:
   *  Constant: '<S75>/ZERO'
   *  DataStoreRead: '<S75>/cmirpmlimiter_old1'
   */
  if (IdxTcCutOff != 0) {
    rtb_Switch1 = 0;
  } else {
    rtb_Switch1 = tcintfactdvs;
  }

  /* End of Switch: '<S75>/Switch' */

  /* Product: '<S75>/Product2' */
  rtb_Switch1 = mul_s32_loSR_sat(rtb_Switch1, rtb_Look2D_S16_U16_U16, 15U);
  if (rtb_Switch1 > 32767) {
    rtb_Switch1 = 32767;
  } else {
    if (rtb_Switch1 < -32768) {
      rtb_Switch1 = -32768;
    }
  }

  /* Sum: '<S75>/Sum5' incorporates:
   *  DataStoreRead: '<S75>/cmirpmlimiter_old3'
   *  Product: '<S75>/Product2'
   */
  q1 = rtb_Switch1 << 9;
  if ((TCIntTerm < 0) && (q1 < MIN_int32_T - TCIntTerm)) {
    q1 = MIN_int32_T;
  } else if ((TCIntTerm > 0) && (q1 > MAX_int32_T - TCIntTerm)) {
    q1 = MAX_int32_T;
  } else {
    q1 += TCIntTerm;
  }

  /* DataStoreWrite: '<S75>/Data Store Write5' incorporates:
   *  Sum: '<S75>/Sum5'
   */
  TCIntTerm = q1;

  /* Sum: '<S15>/Sum1' incorporates:
   *  DataTypeConversion: '<S75>/Data Type Conversion1'
   *  Sum: '<S15>/Sum8'
   *  Sum: '<S75>/Sum5'
   */
  rtb_Switch1 = q1 >> 11;
  q1 = localB->sf_Prop_Acc_To_Smooth.TCPropPTerm_out >> 9;
  if ((rtb_Switch1 < 0) && (q1 < MIN_int32_T - rtb_Switch1)) {
    q1 = MIN_int32_T;
  } else if ((rtb_Switch1 > 0) && (q1 > MAX_int32_T - rtb_Switch1)) {
    q1 = MAX_int32_T;
  } else {
    q1 += rtb_Switch1;
  }

  if (q1 > 32767) {
    q1 = 32767;
  } else {
    if (q1 < -32768) {
      q1 = -32768;
    }
  }

  rtb_Sum8 = (int16_T)q1;

  /* End of Sum: '<S15>/Sum1' */

  /* Product: '<S15>/Product2' incorporates:
   *  Constant: '<S72>/VTGAINTCGEAR'
   *  Selector: '<S72>/Selector'
   */
  localB->Product2 = (int16_T)((rtb_Sum8 * VTGAINTCGEAR[rtu_localgear]) >> 7);

  /* Sum: '<S15>/Sum8' */
  q1 = localB->sf_Prop_Acc_To_Smooth_o.TCPropPTerm_out >> 9;
  if ((q1 < 0) && (rtb_Switch1 < MIN_int32_T - q1)) {
    q1 = MIN_int32_T;
  } else if ((q1 > 0) && (rtb_Switch1 > MAX_int32_T - q1)) {
    q1 = MAX_int32_T;
  } else {
    q1 += rtb_Switch1;
  }

  if (q1 > 32767) {
    q1 = 32767;
  } else {
    if (q1 < -32768) {
      q1 = -32768;
    }
  }

  rtb_Sum8 = (int16_T)q1;

  /* Product: '<S15>/Product4' incorporates:
   *  Constant: '<S72>/VTGAINTCGEAR'
   *  Selector: '<S72>/Selector'
   */
  localB->Product4 = (int16_T)((rtb_Sum8 * VTGAINTCGEAR[rtu_localgear]) >> 7);
}

/* Output and update for function-call system: '<S9>/fc_TC_Assign' */
void TracCtrl_fc_TC_Assign(void)
{
  /* DataStoreRead: '<S14>/Data Store Read' incorporates:
   *  DataStoreWrite: '<S14>/Data Store Write2'
   */
  CmiTracI = CmiTracINoSat;

  /* DataStoreWrite: '<S14>/Data Store Write1' incorporates:
   *  DataStoreRead: '<S14>/Data Store Read1'
   */
  CmiTracP = CmiTracPNoSat;
}

/* Output and update for function-call system: '<S9>/fc_TC_Limit' */
void TracCtrl_fc_TC_Limit(void)
{
  int16_T rtb_Sum1;
  int16_T rtb_Switch3;
  int32_T tmp;

  /* Sum: '<S17>/Sum4' incorporates:
   *  DataStoreRead: '<S17>/CmiRpmLimitNoSat1'
   */
  tmp = CmiTracCtrlInitP + TracCtrl_T10ms_B.fc_TC_CmiControl.Product4;
  if (tmp > 32767) {
    tmp = 32767;
  } else {
    if (tmp < -32768) {
      tmp = -32768;
    }
  }

  /* MinMax: '<S17>/MinMax' incorporates:
   *  Inport: '<Root>/CmfP'
   *  Sum: '<S17>/Sum4'
   */
  if ((int16_T)tmp > CmfP) {
    rtb_Switch3 = (int16_T)tmp;
  } else {
    rtb_Switch3 = CmfP;
  }

  /* End of MinMax: '<S17>/MinMax' */

  /* MinMax: '<S17>/MinMax2' incorporates:
   *  DataStoreWrite: '<S17>/Data Store Write1'
   *  Inport: '<Root>/CmiSatP'
   */
  if (CmiSatP < rtb_Switch3) {
    CmiTracPNoSat = CmiSatP;
  } else {
    CmiTracPNoSat = rtb_Switch3;
  }

  /* End of MinMax: '<S17>/MinMax2' */

  /* Switch: '<S17>/Switch3' incorporates:
   *  DataStoreWrite: '<S17>/Data Store Write1'
   *  Inport: '<Root>/CmiSatI'
   */
  if (TracCtrl_T10ms_B.flg_cmi_virtual_sat != 0) {
    rtb_Switch3 = CmiTracPNoSat;
  } else {
    rtb_Switch3 = CmiSatI;
  }

  /* End of Switch: '<S17>/Switch3' */

  /* Sum: '<S17>/Sum1' incorporates:
   *  DataStoreRead: '<S17>/CmiRpmLimitNoSat2'
   */
  tmp = CmiTracCtrlInitI + TracCtrl_T10ms_B.fc_TC_CmiControl.Product2;
  if (tmp > 32767) {
    tmp = 32767;
  } else {
    if (tmp < -32768) {
      tmp = -32768;
    }
  }

  rtb_Sum1 = (int16_T)tmp;

  /* MinMax: '<S17>/MinMax1' incorporates:
   *  Constant: '<S17>/ZERO1'
   *  Sum: '<S17>/Sum1'
   */
  if ((int16_T)tmp <= 0) {
    rtb_Sum1 = 0;
  }

  /* End of MinMax: '<S17>/MinMax1' */

  /* MinMax: '<S17>/MinMax3' incorporates:
   *  DataStoreWrite: '<S17>/Data Store Write2'
   */
  if (rtb_Switch3 < rtb_Sum1) {
    CmiTracINoSat = rtb_Switch3;
  } else {
    CmiTracINoSat = rtb_Sum1;
  }

  /* End of MinMax: '<S17>/MinMax3' */

  /* DataStoreWrite: '<S17>/Data Store Write3' */
  CmiTracCtrlP = TracCtrl_T10ms_B.fc_TC_CmiControl.Product4;

  /* DataStoreWrite: '<S17>/Data Store Write4' */
  CmiTracCtrlI = TracCtrl_T10ms_B.fc_TC_CmiControl.Product2;

  /* Logic: '<S17>/Logical Operator' incorporates:
   *  Constant: '<S17>/CMIIDBTCACC'
   *  Constant: '<S17>/CMIPDBTCACC'
   *  DataStoreWrite: '<S17>/Data Store Write1'
   *  DataStoreWrite: '<S17>/Data Store Write2'
   *  DataStoreWrite: '<S17>/Data Store Write6'
   *  Inport: '<Root>/CmiSatI'
   *  Inport: '<Root>/CmiSatP'
   *  RelationalOperator: '<S17>/Relational Operator2'
   *  RelationalOperator: '<S17>/Relational Operator3'
   *  Sum: '<S17>/Sum3'
   *  Sum: '<S17>/Sum5'
   */
  flg_end_acc = (uint8_T)(((int16_T)(CmiSatP - (CMIPDBTCACC << 3)) <=
    CmiTracPNoSat) && ((int16_T)(CmiSatI - (CMIIDBTCACC << 3)) <= CmiTracINoSat));
}

/* Output and update for function-call system: '<S9>/fc_TC_NoLimit' */
void TracCtrl_fc_TC_NoLimit(void)
{
  /* DataStoreWrite: '<S18>/Data Store Write2' incorporates:
   *  Inport: '<Root>/CmiSatI'
   */
  CmiTracI = CmiSatI;

  /* DataStoreWrite: '<S18>/Data Store Write1' incorporates:
   *  Inport: '<Root>/CmiSatP'
   */
  CmiTracP = CmiSatP;

  /* DataStoreWrite: '<S18>/Data Store Write3' incorporates:
   *  Constant: '<S18>/ZERO1'
   */
  CmiTracCtrlP = 0;

  /* DataStoreWrite: '<S18>/Data Store Write4' incorporates:
   *  Constant: '<S18>/ZERO3'
   */
  CmiTracCtrlI = 0;
}

/* Output and update for function-call system: '<S9>/fc_TC_Smooth' */
void TracCtrl_fc_TC_Smooth(void)
{
  /* local block i/o variables */
  uint16_T rtb_LookUp_U8_S16;
  uint16_T rtb_Look2D_U8_S16_S16;
  int16_T rtb_LookUp_S16_S16;
  int16_T rtb_Switch;
  uint8_T rtb_Conversion6;
  int16_T rtb_cmirpmlimiter_old2;
  uint8_T rtb_Conversion7;
  int32_T tmp;

  /* DataStoreRead: '<S91>/cmirpmlimiter_old2' */
  rtb_cmirpmlimiter_old2 = DVSSmInt;

  /* DataTypeConversion: '<S93>/Conversion3' incorporates:
   *  Constant: '<S91>/BKSMINTGAIN_dim'
   */
  rtb_Conversion6 = (uint8_T)((uint16_T)BKSMINTGAIN_dim);

  /* S-Function (LookUp_S16_S16): '<S93>/LookUp_S16_S16' incorporates:
   *  Constant: '<S91>/BKSMINTGAIN'
   *  Constant: '<S91>/VTSMINTGAIN'
   */
  LookUp_S16_S16( &rtb_LookUp_S16_S16, &VTSMINTGAIN[0], rtb_cmirpmlimiter_old2,
                 &BKSMINTGAIN[0], rtb_Conversion6);

  /* DataStoreRead: '<S92>/cmirpmlimiter_old1' */
  rtb_cmirpmlimiter_old2 = DVSCtrlErrMax;

  /* DataTypeConversion: '<S97>/Conversion3' incorporates:
   *  Constant: '<S92>/BKDVSERR_dim'
   */
  rtb_Conversion6 = (uint8_T)((uint16_T)BKDVSERR_dim);

  /* S-Function (LookUp_U8_S16): '<S97>/LookUp_U8_S16' incorporates:
   *  Constant: '<S92>/BKDVSERR'
   *  Constant: '<S92>/VTDELTATCSMOOTHDVS'
   */
  LookUp_U8_S16( &rtb_LookUp_U8_S16, &VTDELTATCSMOOTHDVS[0],
                rtb_cmirpmlimiter_old2, &BKDVSERR[0], rtb_Conversion6);

  /* DataTypeConversion: '<S92>/Data Type Conversion' */
  rtb_cmirpmlimiter_old2 = (int16_T)TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Sum5;

  /* DataTypeConversion: '<S96>/Conversion6' incorporates:
   *  Constant: '<S92>/BKTCLEVIDX_dim'
   */
  rtb_Conversion6 = (uint8_T)((uint16_T)BKTCLEVIDX_dim);

  /* Switch: '<S92>/Switch2' incorporates:
   *  Constant: '<S92>/ROLL_REC_40DEG'
   *  Constant: '<S95>/Constant'
   *  Inport: '<Root>/RollCAN'
   *  RelationalOperator: '<S95>/Compare'
   */
  if (TracCtrl_T10ms_B.Merge4 == 1) {
    tmp = ((int16_T)ROLL_REC_40DEG) * 100;
    if (tmp > 32767) {
      tmp = 32767;
    } else {
      if (tmp < -32768) {
        tmp = -32768;
      }
    }

    rtb_Switch = (int16_T)tmp;
  } else {
    rtb_Switch = RollCAN;
  }

  /* End of Switch: '<S92>/Switch2' */

  /* Abs: '<S92>/Abs' */
  if (rtb_Switch < 0) {
    rtb_Switch = (int16_T)-rtb_Switch;
  }

  /* End of Abs: '<S92>/Abs' */

  /* DataTypeConversion: '<S96>/Conversion7' incorporates:
   *  Constant: '<S92>/BKROLLANGLETARGCORR_dim'
   */
  rtb_Conversion7 = (uint8_T)((uint16_T)BKROLLANGLETARGCORR_dim);

  /* S-Function (Look2D_U8_S16_S16): '<S96>/Look2D_U8_S16_S16' incorporates:
   *  Constant: '<S92>/BKROLLANGLETARGCORR'
   *  Constant: '<S92>/BKTCLEVIDX'
   *  Constant: '<S92>/TBSMOOTHGAIN'
   */
  Look2D_U8_S16_S16( &rtb_Look2D_U8_S16_S16, &TBSMOOTHGAIN[0],
                    rtb_cmirpmlimiter_old2, &BKTCLEVIDX[0], rtb_Conversion6,
                    rtb_Switch, &BKROLLANGLETARGCORR[0], rtb_Conversion7);

  /* Product: '<S92>/Product1' incorporates:
   *  DataStoreWrite: '<S90>/Data Store Write2'
   *  DataTypeConversion: '<S92>/Data Type Conversion1'
   *  DataTypeConversion: '<S92>/Data Type Conversion2'
   */
  CmeTcSmooth = (int16_T)((((uint32_T)rtb_LookUp_U8_S16 >> 6) * ((uint32_T)
    rtb_Look2D_U8_S16_S16 >> 8)) >> 5);

  /* Selector: '<S90>/Selector' incorporates:
   *  MinMax: '<S90>/MinMax1'
   */
  if (TracCtrl_T10ms_B.localgear > 1) {
    rtb_Conversion6 = TracCtrl_T10ms_B.localgear;
  } else {
    rtb_Conversion6 = 1U;
  }

  /* Product: '<S90>/Product' incorporates:
   *  DataStoreWrite: '<S90>/Data Store Write2'
   *  Inport: '<Root>/GearRatio'
   *  Selector: '<S90>/Selector'
   */
  tmp = (CmeTcSmooth * GearRatio[rtb_Conversion6]) >> 11;
  if (tmp > 32767) {
    tmp = 32767;
  } else {
    if (tmp < -32768) {
      tmp = -32768;
    }
  }

  rtb_Switch = (int16_T)tmp;

  /* End of Product: '<S90>/Product' */

  /* Product: '<S90>/Product1' incorporates:
   *  DataStoreWrite: '<S90>/Data Store Write3'
   */
  tmp = (rtb_LookUp_S16_S16 * rtb_Switch) >> 7;
  if (tmp > 32767) {
    tmp = 32767;
  } else {
    if (tmp < -32768) {
      tmp = -32768;
    }
  }

  CmiSmoothRatio = (int16_T)tmp;

  /* End of Product: '<S90>/Product1' */

  /* Switch: '<S19>/Switch' incorporates:
   *  Constant: '<S19>/CMISMOOTHRATREC'
   *  DataStoreWrite: '<S90>/Data Store Write3'
   */
  if (TracCtrl_T10ms_B.recSmooth != 0) {
    rtb_Switch = CMISMOOTHRATREC;
  } else {
    rtb_Switch = CmiSmoothRatio;
  }

  /* End of Switch: '<S19>/Switch' */

  /* Sum: '<S89>/Sum1' incorporates:
   *  DataStoreRead: '<S89>/cmirpmlimiter_old1'
   */
  rtb_cmirpmlimiter_old2 = (int16_T)(CmiTracINoSat + rtb_Switch);

  /* MinMax: '<S89>/MinMax2' incorporates:
   *  DataStoreWrite: '<S89>/Data Store Write1'
   *  Inport: '<Root>/CmiSatI'
   */
  if (CmiSatI < rtb_cmirpmlimiter_old2) {
    CmiTracI = CmiSatI;
  } else {
    CmiTracI = rtb_cmirpmlimiter_old2;
  }

  /* End of MinMax: '<S89>/MinMax2' */

  /* Sum: '<S89>/Sum2' incorporates:
   *  DataStoreRead: '<S89>/cmirpmlimiter_old'
   */
  rtb_cmirpmlimiter_old2 = (int16_T)(CmiTracPNoSat + rtb_Switch);

  /* MinMax: '<S89>/MinMax1' incorporates:
   *  DataStoreWrite: '<S89>/Data Store Write2'
   *  Inport: '<Root>/CmiSatP'
   */
  if (CmiSatP < rtb_cmirpmlimiter_old2) {
    CmiTracPNoSat = CmiSatP;
  } else {
    CmiTracPNoSat = rtb_cmirpmlimiter_old2;
  }

  /* End of MinMax: '<S89>/MinMax1' */

  /* DataStoreWrite: '<S89>/Data Store Write3' incorporates:
   *  DataStoreWrite: '<S89>/Data Store Write1'
   */
  CmiTracINoSat = CmiTracI;

  /* DataStoreWrite: '<S89>/Data Store Write4' incorporates:
   *  DataStoreWrite: '<S89>/Data Store Write2'
   */
  CmiTracP = CmiTracPNoSat;

  /* Logic: '<S89>/Logical Operator' incorporates:
   *  Constant: '<S89>/CMIIDBTCSMOOTH'
   *  Constant: '<S89>/CMIPDBTCSMOOTH'
   *  DataStoreWrite: '<S89>/Data Store Write1'
   *  DataStoreWrite: '<S89>/Data Store Write2'
   *  DataStoreWrite: '<S89>/Data Store Write6'
   *  Inport: '<Root>/CmiSatI'
   *  Inport: '<Root>/CmiSatP'
   *  RelationalOperator: '<S89>/Relational Operator'
   *  RelationalOperator: '<S89>/Relational Operator1'
   *  Sum: '<S89>/Sum4'
   *  Sum: '<S89>/Sum5'
   */
  flg_end_smooth = (uint8_T)(((int16_T)(CmiSatI - (CMIIDBTCSMOOTH << 3)) <=
    CmiTracI) && ((int16_T)(CmiSatP - (CMIPDBTCSMOOTH << 3)) <= CmiTracPNoSat));

  /* DataStoreWrite: '<S89>/Data Store Write5' incorporates:
   *  Constant: '<S89>/ZERO3'
   */
  CmiTracCtrlP = 0;

  /* DataStoreWrite: '<S89>/Data Store Write7' incorporates:
   *  Constant: '<S89>/ZERO1'
   */
  CmiTracCtrlI = 0;

  /* DataStoreWrite: '<S90>/Data Store Write1' */
  DVSSmIntGain = rtb_LookUp_S16_S16;

  /* DataStoreWrite: '<S92>/Data Store Write1' */
  CmeTcSmoothCorr = rtb_Look2D_U8_S16_S16;

  /* DataStoreWrite: '<S92>/Data Store Write2' */
  CmeTcSmoothStp = rtb_LookUp_U8_S16;
}

/* Output and update for function-call system: '<S9>/fc_TC_GrUp_Filt_Assign' */
void TracCtrl_fc_TC_GrUp_Filt_Assign(void)
{
  /* DataStoreWrite: '<S16>/Data Store Write1' incorporates:
   *  DataStoreRead: '<S16>/Data Store Read1'
   */
  CmiTracP = CmiTracPNoSat;

  /* DataStoreWrite: '<S16>/Data Store Write2' incorporates:
   *  Inport: '<Root>/CmiSatI'
   */
  CmiTracI = CmiSatI;
}

/* Output and update for function-call system: '<S98>/Function-Call Subsystem' */
void TracCtrl_FunctionCallSubsystem(uint8_T rtu_reset, int16_T rtu_DeltaVehSpeed,
  int16_T rtu_AccDeltaVehSpeed, rtB_FunctionCallSubsystem_TracC *localB,
  rtDW_FunctionCallSubsystem_Trac *localDW)
{
  /* local block i/o variables */
  int32_T rtb_FOF_Reset_S16_FXP_o2_n;
  int32_T rtb_FOF_Reset_S16_FXP_o2_g;
  boolean_T rtb_Compare_o;
  uint8_T rtb_Conversion3;
  int16_T rtb_FOF_Reset_S16_FXP_o1;
  int32_T rtb_Memory1;
  int16_T rtb_FOF_Reset_S16_FXP_o1_k;

  /* Switch: '<S103>/Switch' incorporates:
   *  Constant: '<S103>/Constant1'
   *  Constant: '<S103>/FODVSOFF'
   */
  if (rtu_reset >= 2) {
    rtb_FOF_Reset_S16_FXP_o1_k = 240;
  } else {
    rtb_FOF_Reset_S16_FXP_o1_k = 0;
  }

  /* End of Switch: '<S103>/Switch' */

  /* Sum: '<S103>/Add' */
  rtb_FOF_Reset_S16_FXP_o1 = (int16_T)(rtu_DeltaVehSpeed +
    rtb_FOF_Reset_S16_FXP_o1_k);

  /* RelationalOperator: '<S104>/Compare' incorporates:
   *  Constant: '<S104>/Constant'
   */
  rtb_Compare_o = (rtu_reset != 0);

  /* DataTypeConversion: '<S105>/Conversion3' */
  rtb_Conversion3 = rtb_Compare_o;

  /* Memory: '<S103>/Memory' */
  rtb_Memory1 = localDW->Memory_PreviousInput;

  /* S-Function (FOF_Reset_S16_FXP): '<S105>/FOF_Reset_S16_FXP' incorporates:
   *  Constant: '<S103>/KFFOTCREC1'
   */
  FOF_Reset_S16_FXP( &rtb_FOF_Reset_S16_FXP_o1, &rtb_FOF_Reset_S16_FXP_o2_n,
                    rtb_FOF_Reset_S16_FXP_o1, KFFOTCREC1,
                    rtb_FOF_Reset_S16_FXP_o1, rtb_Conversion3, rtb_Memory1);

  /* DataStoreWrite: '<S103>/Data Store Write' */
  DeltaVehSpeedFO = rtb_FOF_Reset_S16_FXP_o1;

  /* Switch: '<S103>/Switch1' incorporates:
   *  Constant: '<S103>/Constant2'
   *  Constant: '<S103>/FOACCDVSOFF'
   */
  if (rtu_reset >= 2) {
    rtb_FOF_Reset_S16_FXP_o1_k = 800;
  } else {
    rtb_FOF_Reset_S16_FXP_o1_k = 0;
  }

  /* End of Switch: '<S103>/Switch1' */

  /* Sum: '<S103>/Add1' */
  rtb_FOF_Reset_S16_FXP_o1_k += rtu_AccDeltaVehSpeed;

  /* DataTypeConversion: '<S106>/Conversion3' */
  rtb_Conversion3 = rtb_Compare_o;

  /* Memory: '<S103>/Memory1' */
  rtb_Memory1 = localDW->Memory1_PreviousInput;

  /* S-Function (FOF_Reset_S16_FXP): '<S106>/FOF_Reset_S16_FXP' incorporates:
   *  Constant: '<S103>/KFFOTCREC2'
   */
  FOF_Reset_S16_FXP( &rtb_FOF_Reset_S16_FXP_o1_k, &rtb_FOF_Reset_S16_FXP_o2_g,
                    rtb_FOF_Reset_S16_FXP_o1_k, KFFOTCREC2,
                    rtb_FOF_Reset_S16_FXP_o1_k, rtb_Conversion3, rtb_Memory1);

  /* DataStoreWrite: '<S103>/Data Store Write1' */
  AccDeltaVehSpeedFO = rtb_FOF_Reset_S16_FXP_o1_k;

  /* DataTypeConversion: '<S103>/Data Type Conversion' */
  localB->DataTypeConversion = rtb_FOF_Reset_S16_FXP_o1;

  /* DataTypeConversion: '<S103>/Data Type Conversion1' */
  localB->DataTypeConversion1 = rtb_FOF_Reset_S16_FXP_o1_k;

  /* Update for Memory: '<S103>/Memory' */
  localDW->Memory_PreviousInput = rtb_FOF_Reset_S16_FXP_o2_n;

  /* Update for Memory: '<S103>/Memory1' */
  localDW->Memory1_PreviousInput = rtb_FOF_Reset_S16_FXP_o2_g;
}

/* Function for Chart: '<S9>/Calc_TracCtrl_DVS' */
static void TracCtrl_TC_ENABLED(const int16_T *Merge)
{
  int32_T tmpDVSSmInt;
  int16_T tmpCmiTracCtrlInitI;
  boolean_T guard1 = false;

  /* During 'TC_ENABLED': '<S12>:214' */
  guard1 = false;
  if (TcDiagRec != 0) {
    /* Transition: '<S12>:704' */
    if (TracCtrl_T10ms_B.recSmooth != 0) {
      /* Transition: '<S12>:698' */
      StTracCtrl = ((int8_T)TC_REC_SMOOTH);
      DVSSmInt = 0;

      /* Exit Internal 'TC_ENABLED': '<S12>:214' */
      TracCtrl_T10ms_DW.bitsForTID0.is_TC_ENABLED =
        TracCtrl_IN_NO_ACTIVE_CHILD_id;
      TracCtrl_T10ms_DW.bitsForTID0.is_TRAC_CTRL_FSM = TracCtrl_IN_TC_REC_SMOOTH;
    } else {
      /* Transition: '<S12>:705' */
      StTracCtrl = ((int8_T)TC_DISABLED);
      guard1 = true;
    }
  } else {
    /*   */
    if ((ENTRACCTRL == 0) || (SetTracCtrl == 0) || (FlgDisGear != 0) ||
        (FlgCmeLow != 0)) {
      /* Transition: '<S12>:306' */
      FlgTracCtrl = ENTRACCTRL;
      StTracCtrl = ((int8_T)TC_DISABLED);
      guard1 = true;
    } else {
      switch (TracCtrl_T10ms_DW.bitsForTID0.is_TC_ENABLED) {
       case TracCtrl_IN_TC_ACC:
        /* During 'TC_ACC': '<S12>:228' */
        /* Transition: '<S12>:233' */
        TracCtrl_T10ms_B.flg_reset_filt = 0U;

        /* Outputs for Function Call SubSystem: '<S9>/fc_TC_AccWheelCalc' */
        /* Inport: '<Root>/TyreTypeCAN' incorporates:
         *  Inport: '<Root>/CmeEstWheelF'
         *  Inport: '<Root>/RollCAN'
         *  Inport: '<Root>/Rpm'
         *  Inport: '<Root>/SetTracCtrl'
         *  Inport: '<Root>/VehSpeedFront'
         *  Inport: '<Root>/WzCAN'
         */
        /* Event: '<S12>:252' */
        TracCtrl_fc_TC_AccWheelCalc(TracCtrl_T10ms_B.flg_reset_filt, TyreTypeCAN,
          CmeEstWheelF, TracCtrl_T10ms_B.Merge1, RollCAN, Rpm, VehSpeedFront,
          *Merge, TracCtrl_T10ms_B.Switch, TracCtrl_T10ms_B.Merge4, SetTracCtrl,
          WzCAN, &TracCtrl_T10ms_B.fc_TC_AccWheelCalc,
          &TracCtrl_T10ms_C.fc_TC_AccWheelCalc,
          &TracCtrl_T10ms_DW.fc_TC_AccWheelCalc,
          &TracCtrl_T10ms_DW.index_RollCAN, &TracCtrl_T10ms_DW.ratio_RollCAN);

        /* End of Outputs for SubSystem: '<S9>/fc_TC_AccWheelCalc' */
        if (DVSCtrlErr < MAXDVSPDSAT) {
          tmpCmiTracCtrlInitI = DVSCtrlErr;
        } else {
          tmpCmiTracCtrlInitI = MAXDVSPDSAT;
        }

        if (tmpCmiTracCtrlInitI > DVSCtrlErrMax) {
          DVSCtrlErrMax = tmpCmiTracCtrlInitI;
        }

        StTracCtrlTrn = ((int8_T)TC_ACC);
        if (StQShift == QSHIFT_CTF_REQUEST) {
          /* Transition: '<S12>:539' */
          StTracCtrl = ((int8_T)TC_ACC_GEARUP);
          TracCtrl_T10ms_B.flg_cmi_virtual_sat = 1U;

          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_CmiControl' */
          /* Inport: '<Root>/VehSpeedFront' */
          /* Event: '<S12>:253' */
          TracCtrl_fc_TC_CmiControl
            (TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Conversion,
             TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Sum2,
             TracCtrl_T10ms_B.localgear,
             TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Sum5, VehSpeedFront,
             &TracCtrl_T10ms_B.fc_TC_CmiControl,
             &TracCtrl_T10ms_C.fc_TC_CmiControl,
             &TracCtrl_T10ms_DW.fc_TC_CmiControl,
             &TracCtrl_T10ms_DW.index_RollCAN, &TracCtrl_T10ms_DW.ratio_RollCAN);

          /* End of Outputs for SubSystem: '<S9>/fc_TC_CmiControl' */

          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_Limit' */
          /* Event: '<S12>:254' */
          TracCtrl_fc_TC_Limit();

          /* End of Outputs for SubSystem: '<S9>/fc_TC_Limit' */

          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_GrUp_Filt_Assign' */
          /* Event: '<S12>:579' */
          TracCtrl_fc_TC_GrUp_Filt_Assign();

          /* End of Outputs for SubSystem: '<S9>/fc_TC_GrUp_Filt_Assign' */
          TracCtrl_T10ms_DW.bitsForTID0.is_TC_ENABLED =
            TracCtrl_IN_TC_ACC_GEARUP;
        } else if (*Merge < DVSSmoothLev) {
          /* Transition: '<S12>:235' */
          DVSSmInt = 0;

          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_Smooth' */
          /* Event: '<S12>:256' */
          TracCtrl_fc_TC_Smooth();

          /* End of Outputs for SubSystem: '<S9>/fc_TC_Smooth' */
          if ((flg_end_smooth != 0) && (*Merge < DVSModel)) {
            /* Transition: '<S12>:536' */
            StTracCtrl = ((int8_T)TC_WAIT_ACC);

            /* Transition: '<S12>:621' */
            /*  Smooth finished */
            TracCtrl_T10ms_DW.bitsForTID0.is_TC_ENABLED =
              TracCtrl_IN_TC_WAIT_ACC;
          } else {
            /* Transition: '<S12>:535' */
            StTracCtrl = ((int8_T)TC_SMOOTH);
            TracCtrl_T10ms_DW.bitsForTID0.is_TC_ENABLED = TracCtrl_IN_TC_SMOOTH;
          }
        } else {
          /* Inport: '<Root>/CmiSatI' */
          /* Transition: '<S12>:244' */
          if (CmiSatI < CmiTracCtrlInitI) {
            CmiTracCtrlInitI = CmiSatI;
          }

          CmiTracCtrlInitP = (int16_T)(CmiTracCtrlInitI + OffCMITRACP);

          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_CmiControl' */
          /* Inport: '<Root>/VehSpeedFront' */
          /* Event: '<S12>:253' */
          TracCtrl_fc_TC_CmiControl
            (TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Conversion,
             TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Sum2,
             TracCtrl_T10ms_B.localgear,
             TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Sum5, VehSpeedFront,
             &TracCtrl_T10ms_B.fc_TC_CmiControl,
             &TracCtrl_T10ms_C.fc_TC_CmiControl,
             &TracCtrl_T10ms_DW.fc_TC_CmiControl,
             &TracCtrl_T10ms_DW.index_RollCAN, &TracCtrl_T10ms_DW.ratio_RollCAN);

          /* End of Outputs for SubSystem: '<S9>/fc_TC_CmiControl' */

          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_Limit' */
          /* Event: '<S12>:254' */
          TracCtrl_fc_TC_Limit();

          /* End of Outputs for SubSystem: '<S9>/fc_TC_Limit' */

          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_Assign' */
          /* Event: '<S12>:268' */
          TracCtrl_fc_TC_Assign();

          /* End of Outputs for SubSystem: '<S9>/fc_TC_Assign' */
          if ((flg_end_acc != 0) && (*Merge < DVSTarg)) {
            /* Transition: '<S12>:627' */
            StTracCtrl = ((int8_T)TC_WAIT_ACC);

            /* Transition: '<S12>:621' */
            /*  Smooth finished */
            TracCtrl_T10ms_DW.bitsForTID0.is_TC_ENABLED =
              TracCtrl_IN_TC_WAIT_ACC;
          } else {
            /* Transition: '<S12>:626' */
          }
        }
        break;

       case TracCtrl_IN_TC_ACC_GEARUP:
        /* During 'TC_ACC_GEARUP': '<S12>:530' */
        /* Transition: '<S12>:542' */
        TracCtrl_T10ms_B.flg_reset_filt = 0U;

        /* Outputs for Function Call SubSystem: '<S9>/fc_TC_AccWheelCalc' */
        /* Inport: '<Root>/TyreTypeCAN' incorporates:
         *  Inport: '<Root>/CmeEstWheelF'
         *  Inport: '<Root>/RollCAN'
         *  Inport: '<Root>/Rpm'
         *  Inport: '<Root>/SetTracCtrl'
         *  Inport: '<Root>/VehSpeedFront'
         *  Inport: '<Root>/WzCAN'
         */
        /* Event: '<S12>:252' */
        TracCtrl_fc_TC_AccWheelCalc(TracCtrl_T10ms_B.flg_reset_filt, TyreTypeCAN,
          CmeEstWheelF, TracCtrl_T10ms_B.Merge1, RollCAN, Rpm, VehSpeedFront,
          *Merge, TracCtrl_T10ms_B.Switch, TracCtrl_T10ms_B.Merge4, SetTracCtrl,
          WzCAN, &TracCtrl_T10ms_B.fc_TC_AccWheelCalc,
          &TracCtrl_T10ms_C.fc_TC_AccWheelCalc,
          &TracCtrl_T10ms_DW.fc_TC_AccWheelCalc,
          &TracCtrl_T10ms_DW.index_RollCAN, &TracCtrl_T10ms_DW.ratio_RollCAN);

        /* End of Outputs for SubSystem: '<S9>/fc_TC_AccWheelCalc' */
        if (DVSCtrlErr < MAXDVSPDSAT) {
          tmpCmiTracCtrlInitI = DVSCtrlErr;
        } else {
          tmpCmiTracCtrlInitI = MAXDVSPDSAT;
        }

        if (tmpCmiTracCtrlInitI > DVSCtrlErrMax) {
          DVSCtrlErrMax = tmpCmiTracCtrlInitI;
        }

        StTracCtrlTrn = ((int8_T)TC_ACC_GEARUP);

        /* Inport: '<Root>/StQShift' */
        if (StQShift != QSHIFT_CTF_REQUEST) {
          /* Transition: '<S12>:540' */
          StTracCtrl = ((int8_T)TC_ACC_GEARUP_FILT);

          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_CmiControl' */
          /* Inport: '<Root>/VehSpeedFront' */
          /* Event: '<S12>:253' */
          TracCtrl_fc_TC_CmiControl
            (TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Conversion,
             TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Sum2,
             TracCtrl_T10ms_B.localgear,
             TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Sum5, VehSpeedFront,
             &TracCtrl_T10ms_B.fc_TC_CmiControl,
             &TracCtrl_T10ms_C.fc_TC_CmiControl,
             &TracCtrl_T10ms_DW.fc_TC_CmiControl,
             &TracCtrl_T10ms_DW.index_RollCAN, &TracCtrl_T10ms_DW.ratio_RollCAN);

          /* End of Outputs for SubSystem: '<S9>/fc_TC_CmiControl' */

          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_Limit' */
          /* Event: '<S12>:254' */
          TracCtrl_fc_TC_Limit();

          /* End of Outputs for SubSystem: '<S9>/fc_TC_Limit' */

          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_GrUp_Filt_Assign' */
          /* Event: '<S12>:579' */
          TracCtrl_fc_TC_GrUp_Filt_Assign();

          /* End of Outputs for SubSystem: '<S9>/fc_TC_GrUp_Filt_Assign' */
          TracCtrl_T10ms_DW.bitsForTID0.is_TC_ENABLED =
            TracCtrl_IN_TC_ACC_GEARUP_FILT;
        } else {
          /* Inport: '<Root>/CmiSatP' */
          /* Transition: '<S12>:544' */
          if (CmiTracP >= CmiSatP) {
            CmiTracP = CmiSatP;
          }

          /* Inport: '<Root>/CmiSatI' */
          CmiTracI = CmiSatI;
        }
        break;

       case TracCtrl_IN_TC_ACC_GEARUP_FILT:
        /* Outputs for Function Call SubSystem: '<S9>/fc_TC_AccWheelCalc' */
        /* Inport: '<Root>/TyreTypeCAN' incorporates:
         *  Inport: '<Root>/CmeEstWheelF'
         *  Inport: '<Root>/RollCAN'
         *  Inport: '<Root>/Rpm'
         *  Inport: '<Root>/SetTracCtrl'
         *  Inport: '<Root>/VehSpeedFront'
         *  Inport: '<Root>/WzCAN'
         */
        /* During 'TC_ACC_GEARUP_FILT': '<S12>:569' */
        /* Transition: '<S12>:571' */
        /* Event: '<S12>:252' */
        TracCtrl_fc_TC_AccWheelCalc(TracCtrl_T10ms_B.flg_reset_filt, TyreTypeCAN,
          CmeEstWheelF, TracCtrl_T10ms_B.Merge1, RollCAN, Rpm, VehSpeedFront,
          *Merge, TracCtrl_T10ms_B.Switch, TracCtrl_T10ms_B.Merge4, SetTracCtrl,
          WzCAN, &TracCtrl_T10ms_B.fc_TC_AccWheelCalc,
          &TracCtrl_T10ms_C.fc_TC_AccWheelCalc,
          &TracCtrl_T10ms_DW.fc_TC_AccWheelCalc,
          &TracCtrl_T10ms_DW.index_RollCAN, &TracCtrl_T10ms_DW.ratio_RollCAN);

        /* End of Outputs for SubSystem: '<S9>/fc_TC_AccWheelCalc' */
        if (DVSCtrlErr < MAXDVSPDSAT) {
          tmpCmiTracCtrlInitI = DVSCtrlErr;
        } else {
          tmpCmiTracCtrlInitI = MAXDVSPDSAT;
        }

        if (tmpCmiTracCtrlInitI > DVSCtrlErrMax) {
          DVSCtrlErrMax = tmpCmiTracCtrlInitI;
        }

        StTracCtrlTrn = ((int8_T)TC_ACC_GEARUP_FILT);

        /* Inport: '<Root>/StQShift' incorporates:
         *  Inport: '<Root>/CmiSatI'
         */
        if (StQShift == QSHIFT_CTF_REQUEST) {
          /* Transition: '<S12>:574' */
          StTracCtrl = ((int8_T)TC_ACC_GEARUP);

          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_CmiControl' */
          /* Inport: '<Root>/VehSpeedFront' */
          /* Event: '<S12>:253' */
          TracCtrl_fc_TC_CmiControl
            (TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Conversion,
             TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Sum2,
             TracCtrl_T10ms_B.localgear,
             TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Sum5, VehSpeedFront,
             &TracCtrl_T10ms_B.fc_TC_CmiControl,
             &TracCtrl_T10ms_C.fc_TC_CmiControl,
             &TracCtrl_T10ms_DW.fc_TC_CmiControl,
             &TracCtrl_T10ms_DW.index_RollCAN, &TracCtrl_T10ms_DW.ratio_RollCAN);

          /* End of Outputs for SubSystem: '<S9>/fc_TC_CmiControl' */

          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_Limit' */
          /* Event: '<S12>:254' */
          TracCtrl_fc_TC_Limit();

          /* End of Outputs for SubSystem: '<S9>/fc_TC_Limit' */

          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_GrUp_Filt_Assign' */
          /* Event: '<S12>:579' */
          TracCtrl_fc_TC_GrUp_Filt_Assign();

          /* End of Outputs for SubSystem: '<S9>/fc_TC_GrUp_Filt_Assign' */
          TracCtrl_T10ms_DW.bitsForTID0.is_TC_ENABLED =
            TracCtrl_IN_TC_ACC_GEARUP;
        } else if (CmiSatI + OFFCMIGRUFIL >= CmiTracINoSat) {
          /* Transition: '<S12>:573' */
          StTracCtrl = ((int8_T)TC_ACC);
          TracCtrl_T10ms_B.flg_cmi_virtual_sat = 0U;

          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_CmiControl' */
          /* Inport: '<Root>/VehSpeedFront' */
          /* Event: '<S12>:253' */
          TracCtrl_fc_TC_CmiControl
            (TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Conversion,
             TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Sum2,
             TracCtrl_T10ms_B.localgear,
             TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Sum5, VehSpeedFront,
             &TracCtrl_T10ms_B.fc_TC_CmiControl,
             &TracCtrl_T10ms_C.fc_TC_CmiControl,
             &TracCtrl_T10ms_DW.fc_TC_CmiControl,
             &TracCtrl_T10ms_DW.index_RollCAN, &TracCtrl_T10ms_DW.ratio_RollCAN);

          /* End of Outputs for SubSystem: '<S9>/fc_TC_CmiControl' */

          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_Limit' */
          /* Event: '<S12>:254' */
          TracCtrl_fc_TC_Limit();

          /* End of Outputs for SubSystem: '<S9>/fc_TC_Limit' */

          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_Assign' */
          /* Event: '<S12>:268' */
          TracCtrl_fc_TC_Assign();

          /* End of Outputs for SubSystem: '<S9>/fc_TC_Assign' */
          TracCtrl_T10ms_DW.bitsForTID0.is_TC_ENABLED = TracCtrl_IN_TC_ACC;
        } else {
          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_CmiControl' */
          /* Inport: '<Root>/VehSpeedFront' */
          /* Transition: '<S12>:572' */
          /* Event: '<S12>:253' */
          TracCtrl_fc_TC_CmiControl
            (TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Conversion,
             TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Sum2,
             TracCtrl_T10ms_B.localgear,
             TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Sum5, VehSpeedFront,
             &TracCtrl_T10ms_B.fc_TC_CmiControl,
             &TracCtrl_T10ms_C.fc_TC_CmiControl,
             &TracCtrl_T10ms_DW.fc_TC_CmiControl,
             &TracCtrl_T10ms_DW.index_RollCAN, &TracCtrl_T10ms_DW.ratio_RollCAN);

          /* End of Outputs for SubSystem: '<S9>/fc_TC_CmiControl' */

          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_Limit' */
          /* Event: '<S12>:254' */
          TracCtrl_fc_TC_Limit();

          /* End of Outputs for SubSystem: '<S9>/fc_TC_Limit' */

          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_GrUp_Filt_Assign' */
          /* Event: '<S12>:579' */
          TracCtrl_fc_TC_GrUp_Filt_Assign();

          /* End of Outputs for SubSystem: '<S9>/fc_TC_GrUp_Filt_Assign' */
        }
        break;

       case TracCtrl_IN_TC_SMOOTH:
        /* Outputs for Function Call SubSystem: '<S9>/fc_TC_AccWheelCalc' */
        /* Inport: '<Root>/TyreTypeCAN' incorporates:
         *  Inport: '<Root>/CmeEstWheelF'
         *  Inport: '<Root>/RollCAN'
         *  Inport: '<Root>/Rpm'
         *  Inport: '<Root>/SetTracCtrl'
         *  Inport: '<Root>/VehSpeedFront'
         *  Inport: '<Root>/WzCAN'
         */
        /* During 'TC_SMOOTH': '<S12>:234' */
        /* Transition: '<S12>:269' */
        /* Transition: '<S12>:643' */
        /* Event: '<S12>:252' */
        TracCtrl_fc_TC_AccWheelCalc(TracCtrl_T10ms_B.flg_reset_filt, TyreTypeCAN,
          CmeEstWheelF, TracCtrl_T10ms_B.Merge1, RollCAN, Rpm, VehSpeedFront,
          *Merge, TracCtrl_T10ms_B.Switch, TracCtrl_T10ms_B.Merge4, SetTracCtrl,
          WzCAN, &TracCtrl_T10ms_B.fc_TC_AccWheelCalc,
          &TracCtrl_T10ms_C.fc_TC_AccWheelCalc,
          &TracCtrl_T10ms_DW.fc_TC_AccWheelCalc,
          &TracCtrl_T10ms_DW.index_RollCAN, &TracCtrl_T10ms_DW.ratio_RollCAN);

        /* End of Outputs for SubSystem: '<S9>/fc_TC_AccWheelCalc' */
        StTracCtrlTrn = ((int8_T)TC_SMOOTH);
        tmpDVSSmInt = (((DVSSmInt << 1) + DVSSmoothLev) - *Merge) >> 1;
        if (tmpDVSSmInt >= ((int16_T)MAX_sat)) {
          tmpDVSSmInt = ((int16_T)MAX_sat);
        }

        DVSSmInt = (int16_T)tmpDVSSmInt;

        /* Inport: '<Root>/StQShift' */
        if (StQShift == QSHIFT_CTF_REQUEST) {
          /* Transition: '<S12>:538' */
          StTracCtrl = ((int8_T)TC_ACC_GEARUP);

          /* Inport: '<Root>/CmiSatP' */
          if (CmiTracI < CmiSatP) {
            CmiTracCtrlInitI = CmiTracI;
          } else {
            CmiTracCtrlInitI = CmiSatP;
          }

          CmiTracCtrlInitP = (int16_T)(CmiTracCtrlInitI + OffCMITRACP);
          TracCtrl_T10ms_B.flg_cmi_virtual_sat = 1U;
          TCIntTerm = 0;

          /* Inport: '<Root>/CmiSatP' */
          if (CmiTracP >= CmiSatP) {
            CmiTracP = CmiSatP;
          }

          /* Inport: '<Root>/CmiSatI' */
          CmiTracI = CmiSatI;
          DVSCtrlErrMax = DVSCtrlErr;
          TracCtrl_T10ms_DW.bitsForTID0.is_TC_ENABLED =
            TracCtrl_IN_TC_ACC_GEARUP;
        } else {
          /* Transition: '<S12>:637' */
          if (*Merge > DVSTarg) {
            /* Transition: '<S12>:370' */
            StTracCtrl = ((int8_T)TC_ACC);

            /* Inport: '<Root>/CmiSatP' */
            if (CmiTracI < CmiSatP) {
              CmiTracCtrlInitI = CmiTracI;
            } else {
              CmiTracCtrlInitI = CmiSatP;
            }

            CmiTracCtrlInitP = (int16_T)(CmiTracCtrlInitI + OffCMITRACP);
            DVSCtrlErrMax = DVSCtrlErr;
            tmpDVSSmInt = TCIntTerm * ENTCINTTR;
            if (tmpDVSSmInt < 0) {
              TCIntTerm = tmpDVSSmInt;
            } else {
              TCIntTerm = 0;
            }

            /* Outputs for Function Call SubSystem: '<S9>/fc_TC_CmiControl' */
            /* Inport: '<Root>/VehSpeedFront' */
            /* Event: '<S12>:253' */
            TracCtrl_fc_TC_CmiControl
              (TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Conversion,
               TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Sum2,
               TracCtrl_T10ms_B.localgear,
               TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Sum5, VehSpeedFront,
               &TracCtrl_T10ms_B.fc_TC_CmiControl,
               &TracCtrl_T10ms_C.fc_TC_CmiControl,
               &TracCtrl_T10ms_DW.fc_TC_CmiControl,
               &TracCtrl_T10ms_DW.index_RollCAN,
               &TracCtrl_T10ms_DW.ratio_RollCAN);

            /* End of Outputs for SubSystem: '<S9>/fc_TC_CmiControl' */

            /* Outputs for Function Call SubSystem: '<S9>/fc_TC_Limit' */
            /* Event: '<S12>:254' */
            TracCtrl_fc_TC_Limit();

            /* End of Outputs for SubSystem: '<S9>/fc_TC_Limit' */

            /* Outputs for Function Call SubSystem: '<S9>/fc_TC_Assign' */
            /* Event: '<S12>:268' */
            TracCtrl_fc_TC_Assign();

            /* End of Outputs for SubSystem: '<S9>/fc_TC_Assign' */
            TracCtrl_T10ms_DW.bitsForTID0.is_TC_ENABLED = TracCtrl_IN_TC_ACC;
          } else {
            /* Transition: '<S12>:665' */
            if (*Merge >= DVSSmoothLev) {
              /* Transition: '<S12>:660' */
              DVSSmInt = 0;
            } else {
              /* Transition: '<S12>:666' */
            }

            /* Outputs for Function Call SubSystem: '<S9>/fc_TC_Smooth' */
            /* Transition: '<S12>:243' */
            /* Event: '<S12>:256' */
            TracCtrl_fc_TC_Smooth();

            /* End of Outputs for SubSystem: '<S9>/fc_TC_Smooth' */
            if ((flg_end_smooth != 0) && (*Merge <
                 TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Switch)) {
              /* Transition: '<S12>:237' */
              StTracCtrl = ((int8_T)TC_WAIT_ACC);

              /* Transition: '<S12>:621' */
              /*  Smooth finished */
              TracCtrl_T10ms_DW.bitsForTID0.is_TC_ENABLED =
                TracCtrl_IN_TC_WAIT_ACC;
            } else {
              /* Transition: '<S12>:238' */
            }
          }
        }
        break;

       case TracCtrl_IN_TC_DEC:
        /* Outputs for Function Call SubSystem: '<S9>/fc_TC_AccWheelCalc' */
        /* Inport: '<Root>/TyreTypeCAN' incorporates:
         *  Inport: '<Root>/CmeEstWheelF'
         *  Inport: '<Root>/RollCAN'
         *  Inport: '<Root>/Rpm'
         *  Inport: '<Root>/SetTracCtrl'
         *  Inport: '<Root>/VehSpeedFront'
         *  Inport: '<Root>/WzCAN'
         */
        /* During 'TC_DEC': '<S12>:309' */
        /* Transition: '<S12>:315' */
        /* Event: '<S12>:252' */
        TracCtrl_fc_TC_AccWheelCalc(TracCtrl_T10ms_B.flg_reset_filt, TyreTypeCAN,
          CmeEstWheelF, TracCtrl_T10ms_B.Merge1, RollCAN, Rpm, VehSpeedFront,
          *Merge, TracCtrl_T10ms_B.Switch, TracCtrl_T10ms_B.Merge4, SetTracCtrl,
          WzCAN, &TracCtrl_T10ms_B.fc_TC_AccWheelCalc,
          &TracCtrl_T10ms_C.fc_TC_AccWheelCalc,
          &TracCtrl_T10ms_DW.fc_TC_AccWheelCalc,
          &TracCtrl_T10ms_DW.index_RollCAN, &TracCtrl_T10ms_DW.ratio_RollCAN);

        /* End of Outputs for SubSystem: '<S9>/fc_TC_AccWheelCalc' */

        /* Outputs for Function Call SubSystem: '<S9>/fc_TC_NoLimit' */
        /* Event: '<S12>:255' */
        TracCtrl_fc_TC_NoLimit();

        /* End of Outputs for SubSystem: '<S9>/fc_TC_NoLimit' */
        StTracCtrlTrn = ((int8_T)TC_DEC);
        if (*Merge >= 0) {
          /* Transition: '<S12>:321' */
          StTracCtrl = ((int8_T)TC_WAIT_ACC);
          TracCtrl_T10ms_DW.bitsForTID0.is_TC_ENABLED = TracCtrl_IN_TC_WAIT_ACC;
        } else {
          /* Transition: '<S12>:322' */
        }
        break;

       default:
        /* During 'TC_WAIT_ACC': '<S12>:217' */
        /* Transition: '<S12>:227' */
        TracCtrl_T10ms_B.flg_reset_filt = 1U;

        /* Outputs for Function Call SubSystem: '<S9>/fc_TC_AccWheelCalc' */
        /* Inport: '<Root>/TyreTypeCAN' incorporates:
         *  Inport: '<Root>/CmeEstWheelF'
         *  Inport: '<Root>/RollCAN'
         *  Inport: '<Root>/Rpm'
         *  Inport: '<Root>/SetTracCtrl'
         *  Inport: '<Root>/VehSpeedFront'
         *  Inport: '<Root>/WzCAN'
         */
        /* Inport: '<Root>/StQShift' */
        /* Event: '<S12>:252' */
        TracCtrl_fc_TC_AccWheelCalc(TracCtrl_T10ms_B.flg_reset_filt, TyreTypeCAN,
          CmeEstWheelF, TracCtrl_T10ms_B.Merge1, RollCAN, Rpm, VehSpeedFront,
          *Merge, TracCtrl_T10ms_B.Switch, TracCtrl_T10ms_B.Merge4, SetTracCtrl,
          WzCAN, &TracCtrl_T10ms_B.fc_TC_AccWheelCalc,
          &TracCtrl_T10ms_C.fc_TC_AccWheelCalc,
          &TracCtrl_T10ms_DW.fc_TC_AccWheelCalc,
          &TracCtrl_T10ms_DW.index_RollCAN, &TracCtrl_T10ms_DW.ratio_RollCAN);

        /* End of Outputs for SubSystem: '<S9>/fc_TC_AccWheelCalc' */

        /* Outputs for Function Call SubSystem: '<S9>/fc_TC_NoLimit' */
        /* Event: '<S12>:255' */
        TracCtrl_fc_TC_NoLimit();

        /* End of Outputs for SubSystem: '<S9>/fc_TC_NoLimit' */
        StTracCtrlTrn = ((int8_T)TC_WAIT_ACC);
        if (*Merge < THRNEGDVS) {
          /* Transition: '<S12>:312' */
          StTracCtrl = ((int8_T)TC_DEC);
          TracCtrl_T10ms_DW.bitsForTID0.is_TC_ENABLED = TracCtrl_IN_TC_DEC;
        } else if ((*Merge > DVSTarg) && (StQShift != QSHIFT_CTF_REQUEST)) {
          /* Transition: '<S12>:229' */
          StTracCtrl = ((int8_T)TC_ACC);

          /* Inport: '<Root>/CmiSatI' incorporates:
           *  Inport: '<Root>/CmiSatP'
           */
          if (CmiSatI < CmiSatP) {
            tmpCmiTracCtrlInitI = CmiSatI;
          } else {
            tmpCmiTracCtrlInitI = CmiSatP;
          }

          /* Inport: '<Root>/CmiEst' */
          if (tmpCmiTracCtrlInitI < CmiEst) {
            CmiTracCtrlInitI = tmpCmiTracCtrlInitI;
          } else {
            CmiTracCtrlInitI = CmiEst;
          }

          /* End of Inport: '<Root>/CmiEst' */
          CmiTracCtrlInitI = (int16_T)(CmiTracCtrlInitI - OffCMITRACI);
          CmiTracCtrlInitP = (int16_T)(CmiTracCtrlInitI + OffCMITRACP);
          TracCtrl_T10ms_B.flg_cmi_virtual_sat = 0U;
          TCIntTerm = 0;
          DVSCtrlErrMax = DVSCtrlErr;

          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_CmiControl' */
          /* Inport: '<Root>/VehSpeedFront' */
          /* Event: '<S12>:253' */
          TracCtrl_fc_TC_CmiControl
            (TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Conversion,
             TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Sum2,
             TracCtrl_T10ms_B.localgear,
             TracCtrl_T10ms_B.fc_TC_AccWheelCalc.Sum5, VehSpeedFront,
             &TracCtrl_T10ms_B.fc_TC_CmiControl,
             &TracCtrl_T10ms_C.fc_TC_CmiControl,
             &TracCtrl_T10ms_DW.fc_TC_CmiControl,
             &TracCtrl_T10ms_DW.index_RollCAN, &TracCtrl_T10ms_DW.ratio_RollCAN);

          /* End of Outputs for SubSystem: '<S9>/fc_TC_CmiControl' */

          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_Limit' */
          /* Event: '<S12>:254' */
          TracCtrl_fc_TC_Limit();

          /* End of Outputs for SubSystem: '<S9>/fc_TC_Limit' */

          /* Outputs for Function Call SubSystem: '<S9>/fc_TC_Assign' */
          /* Event: '<S12>:268' */
          TracCtrl_fc_TC_Assign();

          /* End of Outputs for SubSystem: '<S9>/fc_TC_Assign' */
          /* Transition: '<S12>:578' */
          TracCtrl_T10ms_DW.bitsForTID0.is_TC_ENABLED = TracCtrl_IN_TC_ACC;
        } else {
          /* Transition: '<S12>:231' */
        }
        break;
      }
    }
  }

  if (guard1) {
    /* Exit Internal 'TC_ENABLED': '<S12>:214' */
    TracCtrl_T10ms_DW.bitsForTID0.is_TC_ENABLED = TracCtrl_IN_NO_ACTIVE_CHILD_id;
    TracCtrl_T10ms_DW.bitsForTID0.is_TRAC_CTRL_FSM = TracCtrl_IN_TC_DISABLED;
  }
}

/* Output and update for function-call system: '<S1>/T10ms' */
void TracCtrl_T10ms(void)
{
  uint8_T Merge3;
  uint8_T Merge2;
  int16_T Merge;
  int32_T tmp;
  boolean_T guard1 = false;

  /* If: '<S10>/If' incorporates:
   *  Constant: '<S10>/FOTCREC'
   *  Constant: '<S99>/Constant'
   *  Inport: '<Root>/AccDeltaVehSpeed'
   *  Inport: '<Root>/DeltaVehSpeed'
   *  Inport: '<Root>/DiagFlg00'
   *  Inport: '<Root>/DiagFlg01'
   *  Inport: '<Root>/FlgYawRec'
   *  Inport: '<S101>/AccDeltaVehSpeed'
   *  Inport: '<S101>/DeltaVehSpeed'
   *  Inport: '<S101>/DiagFlg00'
   *  Inport: '<S101>/DiagFlg01'
   *  Inport: '<S101>/FlgYawRec'
   *  RelationalOperator: '<S99>/Compare'
   *  S-Function (sfix_bitop): '<S10>/Bitwise Operator'
   */
  if ((FOTCREC & 4294967292U) != 30064440U) {
    /* Outputs for IfAction SubSystem: '<S10>/Normal_Signal' incorporates:
     *  ActionPort: '<S101>/Action Port'
     */
    /* DataStoreWrite: '<S101>/Data Store Write' incorporates:
     *  Constant: '<S101>/Constant'
     */
    FlgYawRecFO = 0U;
    Merge = DeltaVehSpeed;
    TracCtrl_T10ms_B.Merge1 = AccDeltaVehSpeed;
    Merge2 = DiagFlg00;
    Merge3 = DiagFlg01;
    TracCtrl_T10ms_B.Merge4 = FlgYawRec;

    /* End of Outputs for SubSystem: '<S10>/Normal_Signal' */
  } else {
    /* Outputs for IfAction SubSystem: '<S10>/FO_Rec' incorporates:
     *  ActionPort: '<S98>/Action Port'
     */
    /* Chart: '<S98>/Chart' incorporates:
     *  Inport: '<Root>/AccDeltaVehSpeed'
     *  Inport: '<Root>/DeltaVehSpeed'
     *  Inport: '<Root>/DiagFlg00'
     *  Inport: '<Root>/DiagFlg01'
     *  Inport: '<Root>/FlgYawRec'
     *  Inport: '<Root>/RidingMode'
     *  Inport: '<Root>/Rpm'
     *  Inport: '<Root>/StartSignal'
     *  S-Function (sfix_bitop): '<S10>/Bitwise Operator1'
     */
    /* Gateway: TracCtrl/T10ms/FO_Rec/FO_Rec/Chart */
    /* During: TracCtrl/T10ms/FO_Rec/FO_Rec/Chart */
    if (TracCtrl_T10ms_DW.bitsForTID0.is_active_c3_TracCtrl == 0U) {
      /* Entry: TracCtrl/T10ms/FO_Rec/FO_Rec/Chart */
      TracCtrl_T10ms_DW.bitsForTID0.is_active_c3_TracCtrl = 1U;

      /* Entry Internal: TracCtrl/T10ms/FO_Rec/FO_Rec/Chart */
      /* Transition: '<S102>:2' */
      TracCtrl_T10ms_B.DiagFlg00FO = DiagFlg00;
      TracCtrl_T10ms_B.DiagFlg01FO_j = DiagFlg01;
      TracCtrl_T10ms_B.FlgYawRecFO_e = 0U;
      TracCtrl_T10ms_B.outFlgYawRecFO = FlgYawRec;
      TracCtrl_T10ms_DW.oldStartSignal = StartSignal;
      TracCtrl_T10ms_B.reset = 1U;

      /* Outputs for Function Call SubSystem: '<S98>/Function-Call Subsystem' */
      /* Event: '<S102>:41' */
      TracCtrl_FunctionCallSubsystem(TracCtrl_T10ms_B.reset, DeltaVehSpeed,
        AccDeltaVehSpeed, &TracCtrl_T10ms_B.FunctionCallSubsystem,
        &TracCtrl_T10ms_DW.FunctionCallSubsystem);

      /* End of Outputs for SubSystem: '<S98>/Function-Call Subsystem' */
      TracCtrl_T10ms_DW.bitsForTID0.is_c3_TracCtrl = TracCtrl_IN_NOT_CUSTOM;
    } else {
      switch (TracCtrl_T10ms_DW.bitsForTID0.is_c3_TracCtrl) {
       case TracCtrl_IN_CUSTOM_MAP_VEH:
        /* During 'CUSTOM_MAP_VEH': '<S102>:3' */
        /* Transition: '<S102>:9' */
        TracCtrl_T10ms_B.DiagFlg00FO = DiagFlg00;
        TracCtrl_T10ms_B.DiagFlg01FO_j = DiagFlg01;
        TracCtrl_T10ms_B.outFlgYawRecFO = FlgYawRec;
        TracCtrl_T10ms_B.reset = 0U;

        /* Outputs for Function Call SubSystem: '<S98>/Function-Call Subsystem' */
        /* Event: '<S102>:41' */
        TracCtrl_FunctionCallSubsystem(TracCtrl_T10ms_B.reset, DeltaVehSpeed,
          AccDeltaVehSpeed, &TracCtrl_T10ms_B.FunctionCallSubsystem,
          &TracCtrl_T10ms_DW.FunctionCallSubsystem);

        /* End of Outputs for SubSystem: '<S98>/Function-Call Subsystem' */
        if (TracCtrl_T10ms_DW.cnt >= NUMWAITERR) {
          /* Transition: '<S102>:10' */
          if ((FOTCREC & 3U) == 0U) {
            /* Transition: '<S102>:23' */
            TracCtrl_T10ms_B.DiagFlg00FO = DiagFlg00;
            TracCtrl_T10ms_B.DiagFlg01FO_j = 1U;
            TracCtrl_T10ms_B.outFlgYawRecFO = FlgYawRec;
          } else {
            /* Transition: '<S102>:24' */
            TracCtrl_T10ms_B.DiagFlg00FO = DiagFlg00;
            TracCtrl_T10ms_B.DiagFlg01FO_j = DiagFlg01;
            TracCtrl_T10ms_B.FlgYawRecFO_e = 1U;
            TracCtrl_T10ms_B.outFlgYawRecFO = 1U;
          }

          TracCtrl_T10ms_DW.bitsForTID0.is_c3_TracCtrl = TracCtrl_IN_FO_ERROR;
        } else {
          /* Transition: '<S102>:11' */
          tmp = TracCtrl_T10ms_DW.cnt + 1;
          if (tmp > 65535) {
            tmp = 65535;
          }

          TracCtrl_T10ms_DW.cnt = (uint16_T)tmp;
        }
        break;

       case TracCtrl_IN_FO_ERROR:
        /* During 'FO_ERROR': '<S102>:12' */
        /* Transition: '<S102>:14' */
        if (RidingMode == 0) {
          /* Transition: '<S102>:16' */
          TracCtrl_T10ms_B.DiagFlg00FO = DiagFlg00;
          TracCtrl_T10ms_B.DiagFlg01FO_j = DiagFlg01;
          TracCtrl_T10ms_B.FlgYawRecFO_e = 0U;
          TracCtrl_T10ms_B.outFlgYawRecFO = FlgYawRec;
          TracCtrl_T10ms_DW.oldStartSignal = StartSignal;
          TracCtrl_T10ms_B.reset = 1U;

          /* Outputs for Function Call SubSystem: '<S98>/Function-Call Subsystem' */
          /* Event: '<S102>:41' */
          TracCtrl_FunctionCallSubsystem(TracCtrl_T10ms_B.reset, DeltaVehSpeed,
            AccDeltaVehSpeed, &TracCtrl_T10ms_B.FunctionCallSubsystem,
            &TracCtrl_T10ms_DW.FunctionCallSubsystem);

          /* End of Outputs for SubSystem: '<S98>/Function-Call Subsystem' */
          /* Transition: '<S102>:17' */
          TracCtrl_T10ms_DW.bitsForTID0.is_c3_TracCtrl = TracCtrl_IN_NOT_CUSTOM;
        } else {
          /* Outputs for Function Call SubSystem: '<S98>/Function-Call Subsystem' */
          /* Transition: '<S102>:19' */
          /* Event: '<S102>:41' */
          TracCtrl_FunctionCallSubsystem(TracCtrl_T10ms_B.reset, DeltaVehSpeed,
            AccDeltaVehSpeed, &TracCtrl_T10ms_B.FunctionCallSubsystem,
            &TracCtrl_T10ms_DW.FunctionCallSubsystem);

          /* End of Outputs for SubSystem: '<S98>/Function-Call Subsystem' */
        }
        break;

       default:
        /* During 'NOT_CUSTOM': '<S102>:1' */
        /* Transition: '<S102>:5' */
        TracCtrl_T10ms_B.DiagFlg00FO = DiagFlg00;
        TracCtrl_T10ms_B.DiagFlg01FO_j = DiagFlg01;
        TracCtrl_T10ms_B.outFlgYawRecFO = FlgYawRec;
        if ((Rpm > 1500) && (RidingMode == 4) && (StartSignal != 0) &&
            (TracCtrl_T10ms_DW.oldStartSignal == 0)) {
          /* Transition: '<S102>:7' */
          TracCtrl_T10ms_B.reset = 2U;
          TracCtrl_T10ms_DW.cnt = 0U;

          /* Outputs for Function Call SubSystem: '<S98>/Function-Call Subsystem' */
          /* Event: '<S102>:41' */
          TracCtrl_FunctionCallSubsystem(TracCtrl_T10ms_B.reset, DeltaVehSpeed,
            AccDeltaVehSpeed, &TracCtrl_T10ms_B.FunctionCallSubsystem,
            &TracCtrl_T10ms_DW.FunctionCallSubsystem);

          /* End of Outputs for SubSystem: '<S98>/Function-Call Subsystem' */
          TracCtrl_T10ms_DW.bitsForTID0.is_c3_TracCtrl =
            TracCtrl_IN_CUSTOM_MAP_VEH;
        } else {
          /* Transition: '<S102>:6' */
          TracCtrl_T10ms_DW.oldStartSignal = StartSignal;

          /* Outputs for Function Call SubSystem: '<S98>/Function-Call Subsystem' */
          /* Event: '<S102>:41' */
          TracCtrl_FunctionCallSubsystem(TracCtrl_T10ms_B.reset, DeltaVehSpeed,
            AccDeltaVehSpeed, &TracCtrl_T10ms_B.FunctionCallSubsystem,
            &TracCtrl_T10ms_DW.FunctionCallSubsystem);

          /* End of Outputs for SubSystem: '<S98>/Function-Call Subsystem' */
        }
        break;
      }
    }

    /* End of Chart: '<S98>/Chart' */

    /* DataStoreWrite: '<S98>/Data Store Write' */
    FlgYawRecFO = TracCtrl_T10ms_B.FlgYawRecFO_e;

    /* DataStoreWrite: '<S98>/Data Store Write1' */
    DiagFlg01FO = TracCtrl_T10ms_B.DiagFlg01FO_j;

    /* DataTypeConversion: '<S98>/Data Type Conversion' */
    Merge3 = TracCtrl_T10ms_B.DiagFlg01FO_j;

    /* DataTypeConversion: '<S98>/Data Type Conversion1' */
    TracCtrl_T10ms_B.Merge4 = TracCtrl_T10ms_B.outFlgYawRecFO;

    /* SignalConversion generated from: '<S98>/AccDeltaVehSpeedFO' */
    TracCtrl_T10ms_B.Merge1 =
      TracCtrl_T10ms_B.FunctionCallSubsystem.DataTypeConversion1;

    /* SignalConversion generated from: '<S98>/DeltaVehSpeedFO' */
    Merge = TracCtrl_T10ms_B.FunctionCallSubsystem.DataTypeConversion;

    /* SignalConversion generated from: '<S98>/DiagFlg00FO' */
    Merge2 = TracCtrl_T10ms_B.DiagFlg00FO;

    /* End of Outputs for SubSystem: '<S10>/FO_Rec' */
  }

  /* End of If: '<S10>/If' */

  /* Switch: '<S11>/Switch' incorporates:
   *  Constant: '<S11>/SELTCDELTCME'
   *  Inport: '<Root>/DeltaCmeDriverF'
   *  Inport: '<Root>/DeltaCmeEstWheelF'
   */
  if (SELTCDELTCME != 0) {
    TracCtrl_T10ms_B.Switch = DeltaCmeEstWheelF;
  } else {
    TracCtrl_T10ms_B.Switch = (int16_T)(DeltaCmeDriverF >> 1);
  }

  /* End of Switch: '<S11>/Switch' */

  /* Chart: '<S9>/Calc_TracCtrl_DVS' incorporates:
   *  Inport: '<Root>/ClutchSignal'
   *  Inport: '<Root>/CmeEstWheelF'
   *  Inport: '<Root>/FlgCmeLow'
   *  Inport: '<Root>/GearPos'
   *  Inport: '<Root>/RollCAN'
   *  Inport: '<Root>/Rpm'
   *  Inport: '<Root>/SetTracCtrl'
   *  Inport: '<Root>/StQShift'
   *  Inport: '<Root>/TyreTypeCAN'
   *  Inport: '<Root>/VehSpeed'
   *  Inport: '<Root>/VehSpeedFront'
   *  Inport: '<Root>/WzCAN'
   */
  /* Gateway: TracCtrl/T10ms/DeltaVehSpeed_Model/Calc_TracCtrl_DVS */
  /* During: TracCtrl/T10ms/DeltaVehSpeed_Model/Calc_TracCtrl_DVS */
  if (TracCtrl_T10ms_DW.bitsForTID0.is_active_c5_TracCtrl == 0U) {
    /* Entry: TracCtrl/T10ms/DeltaVehSpeed_Model/Calc_TracCtrl_DVS */
    TracCtrl_T10ms_DW.bitsForTID0.is_active_c5_TracCtrl = 1U;

    /* Entry Internal: TracCtrl/T10ms/DeltaVehSpeed_Model/Calc_TracCtrl_DVS */
    /* Entry Internal 'GEAR_MANAGE': '<S12>:331' */
    /* Transition: '<S12>:393' */
    TracCtrl_T10ms_B.localgear = (uint8_T)(((ClutchSignal != 0) || (DISTCCLUTCH
      != 0)) * GearPos);
    TracCtrl_T10ms_DW.localgear_old = TracCtrl_T10ms_B.localgear;
    TracCtrl_T10ms_DW.cnt_h = 0U;
    TracCtrl_T10ms_B.recSmooth = 0U;
    FlgDisGear = 0U;
    TcDiagRec = 0U;
    StTcGear = ((uint8_T)TC_NO_CHANGE);
    TracCtrl_T10ms_DW.bitsForTID0.is_GEAR_MANAGE = TracCtrl_IN_GEAR_CHANGE;

    /* Entry Internal 'GEAR_CHANGE': '<S12>:392' */
    /* Transition: '<S12>:335' */
    TracCtrl_T10ms_DW.bitsForTID0.is_GEAR_CHANGE = TracCtrl_IN_NO_CHANGE;

    /* Entry Internal 'NO_CHANGE': '<S12>:339' */
    /* Transition: '<S12>:440' */
    TracCtrl_T10ms_DW.bitsForTID0.is_NO_CHANGE = TracCtrl_IN_OBSERVE_CHANGE;

    /* Entry Internal 'TRAC_CTRL_FSM': '<S12>:330' */
    /* Transition: '<S12>:211' */
    FlgTracCtrl = ENTRACCTRL;
    StTracCtrlTrn = ((int8_T)TC_DISABLED);
    TracCtrl_T10ms_DW.bitsForTID0.is_TRAC_CTRL_FSM = TracCtrl_IN_TC_DISABLED;
  } else {
    /* During 'GEAR_MANAGE': '<S12>:331' */
    if (TracCtrl_T10ms_DW.bitsForTID0.is_GEAR_MANAGE == TracCtrl_IN_GEAR_CHANGE)
    {
      /* During 'GEAR_CHANGE': '<S12>:392' */
      if ((Merge2 != 0) || (Merge3 != 0)) {
        /* Transition: '<S12>:672' */
        StTcGear = ((uint8_T)TC_GEAR_DISABLE);

        /* Exit Internal 'GEAR_CHANGE': '<S12>:392' */
        /* Exit Internal 'NO_CHANGE': '<S12>:339' */
        /* Exit Internal 'SPRING_UP': '<S12>:430' */
        TracCtrl_T10ms_DW.bitsForTID0.is_SPRING_UP =
          TracCtrl_IN_NO_ACTIVE_CHILD_id;
        TracCtrl_T10ms_DW.bitsForTID0.is_NO_CHANGE =
          TracCtrl_IN_NO_ACTIVE_CHILD_id;
        TracCtrl_T10ms_DW.bitsForTID0.is_GEAR_CHANGE =
          TracCtrl_IN_NO_ACTIVE_CHILD_id;
        TracCtrl_T10ms_DW.bitsForTID0.is_GEAR_MANAGE =
          TracCtrl_IN_TC_GEAR_DISABLE;
      } else if (TracCtrl_T10ms_DW.bitsForTID0.is_GEAR_CHANGE ==
                 TracCtrl_IN_CHANGE) {
        /* During 'CHANGE': '<S12>:334' */
        if ((VehSpeedFront < THVSPRINGUP) && (VehSpeed < THVSPRINGUP)) {
          /* Transition: '<S12>:490' */
          /* Transition: '<S12>:506' */
          FlgDisGear = 1U;
          TracCtrl_T10ms_B.localgear = GearPos;
          TracCtrl_T10ms_DW.localgear_old = TracCtrl_T10ms_B.localgear;
          StTcGear = ((uint8_T)TC_SPRING_UP);
          TracCtrl_T10ms_DW.bitsForTID0.is_GEAR_CHANGE = TracCtrl_IN_NO_CHANGE;
          TracCtrl_T10ms_DW.bitsForTID0.is_NO_CHANGE = TracCtrl_IN_SPRING_UP;
          TracCtrl_T10ms_DW.bitsForTID0.is_SPRING_UP =
            TracCtrl_IN_TC_DIS_BY_VEHSPEED;
        } else {
          /* Transition: '<S12>:343' */
          TracCtrl_T10ms_B.localgear = (uint8_T)(((ClutchSignal != 0) ||
            (DISTCCLUTCH != 0)) * GearPos);
          if (IdTracCtrlRid == 0) {
            /* Transition: '<S12>:554' */
            Merge3 = CNTDISTRAC0;

            /* Transition: '<S12>:712' */
          } else if (IdTracCtrlRid == 1) {
            /* Transition: '<S12>:555' */
            Merge3 = CNTDISTRAC1;

            /* Transition: '<S12>:714' */
          } else {
            /* Transition: '<S12>:710' */
            Merge3 = CNTDISTRAC2;
          }

          /*   */
          if (((TracCtrl_T10ms_B.localgear != TracCtrl_T10ms_DW.localgear_old) &&
               (StQShift != QSHIFT_CTF_WAIT)) || ((ClutchSignal == 0) &&
               (DISTCCLUTCH == 0))) {
            /* Transition: '<S12>:344' */
            TracCtrl_T10ms_DW.localgear_old = TracCtrl_T10ms_B.localgear;
            TracCtrl_T10ms_DW.cnt_h = 0U;
          } else {
            /* Transition: '<S12>:346' */
            TracCtrl_T10ms_DW.cnt_h++;
            if (TracCtrl_T10ms_DW.cnt_h > Merge3) {
              /* Transition: '<S12>:348' */
              TracCtrl_T10ms_DW.localgear_old = TracCtrl_T10ms_B.localgear;
              FlgDisGear = 0U;
              StTcGear = ((uint8_T)TC_NO_CHANGE);
              TracCtrl_T10ms_DW.bitsForTID0.is_GEAR_CHANGE =
                TracCtrl_IN_NO_CHANGE;
              TracCtrl_T10ms_DW.bitsForTID0.is_NO_CHANGE =
                TracCtrl_IN_OBSERVE_CHANGE;
            } else {
              /* Transition: '<S12>:349' */
              TracCtrl_T10ms_DW.localgear_old = TracCtrl_T10ms_B.localgear;
            }
          }
        }
      } else {
        /* During 'NO_CHANGE': '<S12>:339' */
        if (TracCtrl_T10ms_DW.bitsForTID0.is_NO_CHANGE ==
            TracCtrl_IN_OBSERVE_CHANGE) {
          /* During 'OBSERVE_CHANGE': '<S12>:431' */
          if ((VehSpeedFront < THVSPRINGUP) && (VehSpeed < THVSPRINGUP)) {
            /* Transition: '<S12>:432' */
            /* Transition: '<S12>:506' */
            FlgDisGear = 1U;
            TracCtrl_T10ms_B.localgear = GearPos;
            TracCtrl_T10ms_DW.localgear_old = TracCtrl_T10ms_B.localgear;
            StTcGear = ((uint8_T)TC_SPRING_UP);
            TracCtrl_T10ms_DW.bitsForTID0.is_NO_CHANGE = TracCtrl_IN_SPRING_UP;
            TracCtrl_T10ms_DW.bitsForTID0.is_SPRING_UP =
              TracCtrl_IN_TC_DIS_BY_VEHSPEED;
          } else {
            /* Transition: '<S12>:338' */
            TracCtrl_T10ms_B.localgear = (uint8_T)(((ClutchSignal != 0) ||
              (DISTCCLUTCH != 0)) * GearPos);
            if (((StTracCtrl != ((int8_T)TC_ACC)) && (StTracCtrl != ((int8_T)
                   TC_SMOOTH)) && (StTracCtrl != ((int8_T)TC_ACC_GEARUP)) &&
                 (StTracCtrl != ((int8_T)TC_ACC_GEARUP_FILT)) &&
                 (((TracCtrl_T10ms_B.localgear !=
                    TracCtrl_T10ms_DW.localgear_old) && (StQShift !=
                    QSHIFT_CTF_REQUEST) && (StQShift != QSHIFT_CTF_WAIT)) ||
                  (StQShift == QSHIFT_CTF_REQUEST))) || ((ClutchSignal == 0) &&
                 (DISTCCLUTCH == 0))) {
              /* Transition: '<S12>:340' */
              TracCtrl_T10ms_DW.localgear_old = TracCtrl_T10ms_B.localgear;
              TracCtrl_T10ms_DW.cnt_h = 0U;
              FlgDisGear = 1U;
              StTcGear = ((uint8_T)TC_CHANGE);
              TracCtrl_T10ms_DW.bitsForTID0.is_NO_CHANGE =
                TracCtrl_IN_NO_ACTIVE_CHILD_id;
              TracCtrl_T10ms_DW.bitsForTID0.is_GEAR_CHANGE = TracCtrl_IN_CHANGE;
            } else {
              /* Transition: '<S12>:341' */
              TracCtrl_T10ms_DW.localgear_old = TracCtrl_T10ms_B.localgear;
            }
          }
        } else {
          /* During 'SPRING_UP': '<S12>:430' */
          if (TracCtrl_T10ms_DW.bitsForTID0.is_SPRING_UP ==
              TracCtrl_IN_OBSERVE_TC_STATUS) {
            /* During 'OBSERVE_TC_STATUS': '<S12>:454' */
            /* Transition: '<S12>:458' */
            TracCtrl_T10ms_B.localgear = GearPos;
            TracCtrl_T10ms_DW.localgear_old = TracCtrl_T10ms_B.localgear;
            if (TracCtrl_T10ms_DW.cntspu >= TIMSPRINGUP) {
              /* Transition: '<S12>:442' */
              if ((StTracCtrl == ((int8_T)TC_SMOOTH)) || (StTracCtrl == ((int8_T)
                    TC_ACC))) {
                /* Transition: '<S12>:462' */
                TracCtrl_T10ms_DW.cntspuexit = 0U;
              } else {
                /* Transition: '<S12>:470' */
                TracCtrl_T10ms_DW.cntspuexit++;
                if ((VehSpeedFront >= THVSNOCHANGE) ||
                    (TracCtrl_T10ms_DW.cntspuexit >= TIMSPEXIT)) {
                  /* Transition: '<S12>:463' */
                  TracCtrl_T10ms_B.localgear = (uint8_T)(((ClutchSignal != 0) ||
                    (DISTCCLUTCH != 0)) * GearPos);
                  TracCtrl_T10ms_DW.localgear_old = TracCtrl_T10ms_B.localgear;
                  StTcGear = ((uint8_T)TC_NO_CHANGE);
                  TracCtrl_T10ms_DW.bitsForTID0.is_SPRING_UP =
                    TracCtrl_IN_NO_ACTIVE_CHILD_id;
                  TracCtrl_T10ms_DW.bitsForTID0.is_NO_CHANGE =
                    TracCtrl_IN_OBSERVE_CHANGE;
                } else {
                  /* Transition: '<S12>:481' */
                }
              }
            } else {
              /* Transition: '<S12>:445' */
              TracCtrl_T10ms_DW.cntspu++;
            }
          } else {
            /* During 'TC_DIS_BY_VEHSPEED': '<S12>:446' */
            /* Transition: '<S12>:450' */
            TracCtrl_T10ms_B.localgear = GearPos;
            TracCtrl_T10ms_DW.localgear_old = TracCtrl_T10ms_B.localgear;
            if (VehSpeedFront >= THVSPRINGUP + DELTAVSSPRINGUP) {
              /* Transition: '<S12>:439' */
              FlgDisGear = 0U;
              TracCtrl_T10ms_DW.cntspu = 0U;
              TracCtrl_T10ms_DW.cntspuexit = 0U;
              TracCtrl_T10ms_DW.bitsForTID0.is_SPRING_UP =
                TracCtrl_IN_OBSERVE_TC_STATUS;
            } else {
              /* Transition: '<S12>:453' */
            }
          }
        }
      }
    } else {
      /* During 'TC_GEAR_DISABLE': '<S12>:671' */
      /* Transition: '<S12>:683' */
      TcDiagRec = 1U;
      guard1 = false;
      if ((StTracCtrl != ((int8_T)TC_DISABLED)) && (StTracCtrl != ((int8_T)
            TC_WAIT_ACC)) && (StTracCtrl != ((int8_T)TC_DEC))) {
        /* Transition: '<S12>:679' */
        TracCtrl_T10ms_B.recSmooth = 1U;
        if (flg_end_smooth != 0) {
          /* Transition: '<S12>:690' */
          guard1 = true;
        } else {
          /* Transition: '<S12>:689' */
        }
      } else {
        /* Transition: '<S12>:680' */
        guard1 = true;
      }

      if (guard1) {
        if ((Merge2 == 0) && (Merge3 == 0)) {
          /* Transition: '<S12>:673' */
          TcDiagRec = 0U;
          TracCtrl_T10ms_DW.localgear_old = TracCtrl_T10ms_B.localgear;
          FlgDisGear = 0U;
          TracCtrl_T10ms_B.recSmooth = 0U;
          StTcGear = ((uint8_T)TC_NO_CHANGE);
          TracCtrl_T10ms_DW.bitsForTID0.is_GEAR_MANAGE = TracCtrl_IN_GEAR_CHANGE;
          TracCtrl_T10ms_DW.bitsForTID0.is_GEAR_CHANGE = TracCtrl_IN_NO_CHANGE;
          TracCtrl_T10ms_DW.bitsForTID0.is_NO_CHANGE =
            TracCtrl_IN_OBSERVE_CHANGE;
        } else {
          /* Transition: '<S12>:696' */
        }
      }
    }

    /* During 'TRAC_CTRL_FSM': '<S12>:330' */
    switch (TracCtrl_T10ms_DW.bitsForTID0.is_TRAC_CTRL_FSM) {
     case TracCtrl_IN_TC_DISABLED:
      /* During 'TC_DISABLED': '<S12>:213' */
      /* Transition: '<S12>:224' */
      TracCtrl_T10ms_B.flg_reset_filt = 1U;

      /* Outputs for Function Call SubSystem: '<S9>/fc_TC_AccWheelCalc' */
      /* Event: '<S12>:252' */
      TracCtrl_fc_TC_AccWheelCalc(TracCtrl_T10ms_B.flg_reset_filt, TyreTypeCAN,
        CmeEstWheelF, TracCtrl_T10ms_B.Merge1, RollCAN, Rpm, VehSpeedFront,
        Merge, TracCtrl_T10ms_B.Switch, TracCtrl_T10ms_B.Merge4, SetTracCtrl,
        WzCAN, &TracCtrl_T10ms_B.fc_TC_AccWheelCalc,
        &TracCtrl_T10ms_C.fc_TC_AccWheelCalc,
        &TracCtrl_T10ms_DW.fc_TC_AccWheelCalc, &TracCtrl_T10ms_DW.index_RollCAN,
        &TracCtrl_T10ms_DW.ratio_RollCAN);

      /* End of Outputs for SubSystem: '<S9>/fc_TC_AccWheelCalc' */

      /* Outputs for Function Call SubSystem: '<S9>/fc_TC_NoLimit' */
      /* Event: '<S12>:255' */
      TracCtrl_fc_TC_NoLimit();

      /* End of Outputs for SubSystem: '<S9>/fc_TC_NoLimit' */
      StTracCtrlTrn = ((int8_T)TC_DISABLED);
      if (TcDiagRec == 0) {
        /* Transition: '<S12>:416' */
        if (ENTRACCTRL != 0) {
          /* Transition: '<S12>:222' */
          FlgTracCtrl = ENTRACCTRL;
          if ((SetTracCtrl > 0) && (FlgDisGear == 0) && (FlgCmeLow == 0)) {
            /* Transition: '<S12>:301' */
            StTracCtrl = ((int8_T)TC_WAIT_ACC);
            AccDVSpdCtrlFMem = TracCtrl_T10ms_B.Merge1;
            TracCtrl_T10ms_DW.bitsForTID0.is_TRAC_CTRL_FSM =
              TracCtrl_IN_TC_ENABLED;
            TracCtrl_T10ms_DW.bitsForTID0.is_TC_ENABLED =
              TracCtrl_IN_TC_WAIT_ACC;
          } else {
            /* Transition: '<S12>:305' */
          }
        } else {
          /* Transition: '<S12>:417' */
        }
      } else {
        /* Transition: '<S12>:225' */
        FlgTracCtrl = 0U;
      }
      break;

     case TracCtrl_IN_TC_ENABLED:
      TracCtrl_TC_ENABLED(&Merge);
      break;

     default:
      /* Outputs for Function Call SubSystem: '<S9>/fc_TC_AccWheelCalc' */
      /* During 'TC_REC_SMOOTH': '<S12>:697' */
      /* Transition: '<S12>:700' */
      /* Event: '<S12>:252' */
      TracCtrl_fc_TC_AccWheelCalc(TracCtrl_T10ms_B.flg_reset_filt, TyreTypeCAN,
        CmeEstWheelF, TracCtrl_T10ms_B.Merge1, RollCAN, Rpm, VehSpeedFront,
        Merge, TracCtrl_T10ms_B.Switch, TracCtrl_T10ms_B.Merge4, SetTracCtrl,
        WzCAN, &TracCtrl_T10ms_B.fc_TC_AccWheelCalc,
        &TracCtrl_T10ms_C.fc_TC_AccWheelCalc,
        &TracCtrl_T10ms_DW.fc_TC_AccWheelCalc, &TracCtrl_T10ms_DW.index_RollCAN,
        &TracCtrl_T10ms_DW.ratio_RollCAN);

      /* End of Outputs for SubSystem: '<S9>/fc_TC_AccWheelCalc' */

      /* Outputs for Function Call SubSystem: '<S9>/fc_TC_Smooth' */
      /* Event: '<S12>:256' */
      TracCtrl_fc_TC_Smooth();

      /* End of Outputs for SubSystem: '<S9>/fc_TC_Smooth' */
      StTracCtrlTrn = ((int8_T)TC_SMOOTH);
      if (flg_end_smooth != 0) {
        /* Transition: '<S12>:701' */
        StTracCtrl = ((int8_T)TC_DISABLED);
        TracCtrl_T10ms_DW.bitsForTID0.is_TRAC_CTRL_FSM = TracCtrl_IN_TC_DISABLED;
      } else {
        /* Transition: '<S12>:702' */
      }
      break;
    }
  }

  /* End of Chart: '<S9>/Calc_TracCtrl_DVS' */
  /* user code (Output function Trailer) */

  /* System '<S1>/T10ms' */

  /* PILOTAGGIO USCITE - T10ms */
}

/* Output and update for function-call system: '<S1>/Reset' */
void TracCtrl_Reset(void)
{
  /* Constant: '<S3>/ID_TRAC_CTRL' */
  IDTracCtrlVer = ID_TRAC_CTRL;

  /* Chart: '<S3>/fc_Reset' */
  /* Gateway: TracCtrl/Reset/fc_Reset */
  /* During: TracCtrl/Reset/fc_Reset */
  /* Entry Internal: TracCtrl/Reset/fc_Reset */
  /* Transition: '<S8>:1' */
  CmiTracP = 0;
  CmiTracI = 0;
  CmiTracCtrl = 0;
  CmiTracCtrlP = 0;
  CmiTracCtrlI = 0;
  CmiTracCtrlInitI = 0;
  CmiTracCtrlInitP = 0;
  TcDiagRec = 0U;
  CntTimSmooth = 0;
  FlgTracCtrl = ENTRACCTRL;
  CntTcSmooth = 0U;
  IdxTcCutOff = 0U;
  IdTracCtrlRid = 0U;
  FlgYawRecFO = 0U;

  /* Transition: '<S8>:32' */
  /*  Exit */
}

/* Output and update for function-call system: '<S1>/PreTDC' */
void TracCtrl_PreTDC(void)
{
  /* user code (Output function Trailer) */

  /* System '<S1>/PreTDC' */
  /* PILOTAGGIO USCITE - PreTdc */
}

/* Model step function */
void TracCtrl_step(void)
{
  /* Outputs for Atomic SubSystem: '<Root>/TracCtrl' */

  /* Outputs for Triggered SubSystem: '<S1>/Trig_2_fc1' incorporates:
   *  TriggerPort: '<S6>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOn' incorporates:
   *  Inport: '<Root>/ev_NoSync'
   */
  if (((TracCtrl_U.ev_PowerOn > 0) &&
       (TracCtrl_PrevZCSigState.Trig_2_fc1_Trig_ZCE[0] != POS_ZCSIG)) ||
      ((TracCtrl_U.ev_NoSync > 0) &&
       (TracCtrl_PrevZCSigState.Trig_2_fc1_Trig_ZCE[1] != POS_ZCSIG))) {
    /* S-Function (fcncallgen): '<S6>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Reset'
     */
    TracCtrl_Reset();

    /* End of Outputs for S-Function (fcncallgen): '<S6>/Function-Call Generator' */
  }

  TracCtrl_PrevZCSigState.Trig_2_fc1_Trig_ZCE[0] = (ZCSigState)
    (TracCtrl_U.ev_PowerOn > 0);

  /* End of Inport: '<Root>/ev_PowerOn' */

  /* Inport: '<Root>/ev_NoSync' */
  TracCtrl_PrevZCSigState.Trig_2_fc1_Trig_ZCE[1] = (ZCSigState)
    (TracCtrl_U.ev_NoSync > 0);

  /* End of Outputs for SubSystem: '<S1>/Trig_2_fc1' */

  /* Outputs for Triggered SubSystem: '<S1>/Trig_2_fc' incorporates:
   *  TriggerPort: '<S5>/Trigger'
   */
  /* Inport: '<Root>/ev_10ms' */
  if ((TracCtrl_U.ev_10ms > 0) && (TracCtrl_PrevZCSigState.Trig_2_fc_Trig_ZCE !=
       POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S5>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/T10ms'
     */
    TracCtrl_T10ms();

    /* End of Outputs for S-Function (fcncallgen): '<S5>/Function-Call Generator' */
  }

  TracCtrl_PrevZCSigState.Trig_2_fc_Trig_ZCE = (ZCSigState)(TracCtrl_U.ev_10ms >
    0);

  /* End of Inport: '<Root>/ev_10ms' */
  /* End of Outputs for SubSystem: '<S1>/Trig_2_fc' */

  /* Outputs for Triggered SubSystem: '<S1>/Trig_2_fc2' incorporates:
   *  TriggerPort: '<S7>/Trigger'
   */
  /* Inport: '<Root>/ev_PreTDC' */
  if ((TracCtrl_U.ev_PreTDC > 0) && (TracCtrl_PrevZCSigState.Trig_2_fc2_Trig_ZCE
       != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S7>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/PreTDC'
     */
    TracCtrl_PreTDC();

    /* End of Outputs for S-Function (fcncallgen): '<S7>/Function-Call Generator' */
  }

  TracCtrl_PrevZCSigState.Trig_2_fc2_Trig_ZCE = (ZCSigState)
    (TracCtrl_U.ev_PreTDC > 0);

  /* End of Inport: '<Root>/ev_PreTDC' */
  /* End of Outputs for SubSystem: '<S1>/Trig_2_fc2' */

  /* End of Outputs for SubSystem: '<Root>/TracCtrl' */
}

/* Model initialize function */
void TracCtrl_initialize(void)
{
  TracCtrl_PrevZCSigState.Trig_2_fc_Trig_ZCE = POS_ZCSIG;
  TracCtrl_PrevZCSigState.Trig_2_fc1_Trig_ZCE[0] = POS_ZCSIG;
  TracCtrl_PrevZCSigState.Trig_2_fc1_Trig_ZCE[1] = POS_ZCSIG;
  TracCtrl_PrevZCSigState.Trig_2_fc2_Trig_ZCE = POS_ZCSIG;
}

/* user code (bottom of source file) */
/* System '<Root>/TracCtrl' */
void TracCtrl_Init(void)
{
  TracCtrl_initialize();               /* Init statechart */
  TracCtrl_Reset();
}

void TracCtrl_NoSync(void)
{
  TracCtrl_Reset();
}

#else
#include "trqdriv_mgm.h"

int16_T CmiTracP;
int16_T CmiTracI;
uint8_T FlgTracCtrl;
void TracCtrl_stub(void);
void TracCtrl_PreTDC(void)
{
}

void TracCtrl_Init(void)
{
  TracCtrl_stub();
}

void TracCtrl_NoSync(void)
{
  TracCtrl_stub();
}

void TracCtrl_T10ms(void)
{
  TracCtrl_stub();
}

void TracCtrl_stub(void)
{
  CmiTracP = CmiDriverP;
  CmiTracI = CmiDriverI;
  FlgTracCtrl = 0;
}

#endif

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
