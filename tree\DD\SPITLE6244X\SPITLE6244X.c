/*****************************************************************************************************************/
/* $HeadURL:: https://************/svn/Rep_Bo/EM/NISO/MV8/trunk/tree/DD/SPITLE6244X/SPITLE6244X.c    $   */
/* $ Description:                                                                                                */
/* $Revision:: 16655  $                                                                                          */
/* $Date:: 2025-05-12 11:08:24 +0300 (Mon, 12 May 2025)   $                                                      */
/* $Author:: durano                  $                                                                       */
/*****************************************************************************************************************/

#include "SPITLE6244X.h"

#undef RECOVERY_MESSAGE
#undef EXTEND_MESSAGE
#undef LOG_DIAGNOSIS_TLE
#ifdef LOG_DIAGNOSIS_TLE
#define LOG_DIAGNOSIS_1
#undef  LOG_DIAGNOSIS_2
#undef  LOG_DIAGNOSIS_3
#undef  LOG_DIAGNOSIS_4
#undef  LOG_DIAGNOSIS_5
#undef  LOG_DIAGNOSIS_STCON
#define Maxdeb 25
uint16_T debugSPIMsg[Maxdeb];
uint8_T bdSPIMsgIdx;
#endif

#ifdef _BUILD_SPITLE6244X_

#define TLE_LINE_DIAG_1 0
#define TLE_LINE_DIAG_2 1
#define TLE_LINE_DIAG_3 2
#define TLE_LINE_DIAG_4 3
#define TLE_LINE_DIAG_5 4

#define TLE_OVERVOLTAGE   0x01
#define TLE_UNDERVOLTAGE  0x02
#define TLE_ABE_STATUS    0x04
#define TLE_GENERAL_ERROR 0x08
#define TLE_OVERTEMP      0x10

#define MASKTLE_SINGLE_DIAG 0x03 /* Maschera per estrarre la singola diagnosi. */
#define TLE_NUM_DIAG 4 /* Numero di diagnosi per gruppo. */
#define TLE_NUM_LINE 5 /* Numero di linee diagnosi per gli ingressi. */
#define TLE_SHORT_CIRCUIT_GND 0x00 /* Valore riportato per cortocircuito a GND. */
#define TLE_CIRCUIT_OPEN 0x01 /* Valore riportato per circuito aperto. */
#define TLE_SHORT_CIRCUIT_VBAT 0x02 /* Valore riportato per cortocircuito a VBatt. */
#define TLE_CIRCUIT_LOAD 0x03 /* Valore riportato per circuito caricato. */

#define OUT_TLE_INJ_1B OUT_InjDrv_1
#define OUT_TLE_INJ_2B OUT_InjDrv_2
#define OUT_TLE_AIRCUT OTE_Air_Cut
#define OUT_TLE_CANISTER OTE_Canister
#define OUT_TLE_INJ_3B OUT_InjDrv_3
#define OUT_TLE_INJ_4B OUT_InjDrv_4
#define OUT_TLE_PUMPREL ODE_Pump_Relay
#define OUT_TLE_HORN ODE_HORN
#define OUT_TLE_LAMB_1 ODE_H_Lambda_1
#define OUT_TLE_LAMB_2 ODE_H_Lambda_2
#define OUT_TLE_INJ_1A OUT_InjDrv_5
#define OUT_TLE_INJ_2A OUT_InjDrv_6
#define OUT_TLE_INJ_3A OUT_InjDrv_7
#define OUT_TLE_INJ_4A OUT_InjDrv_8
#define OUT_TLE_TSS ODE_TSS
#define OUT_TLE_VVT OTE_VVT_Valve
#define OUT_TLE_DUMMY OUT_TLE_INJ_1B
#define OUT_TLE_FAN_1 OUT_TLE_DUMMY
#define OUT_TLE_FAN_2 OUT_TLE_DUMMY

/* Messaggi inviati in configurazione. */
#define CFGTLE6244PATTERNSIZE 12
const uint16_T vConfigTLE6244Pattern[CFGTLE6244PATTERNSIZE] = 
{
SPI_TLE6244X_MESSAGE_01,   
SPI_TLE6244X_MESSAGE_02,
SPI_TLE6244X_MESSAGE_03,
SPI_TLE6244X_MESSAGE_04,
SPI_TLE6244X_MESSAGE_05,
SPI_TLE6244X_MESSAGE_06,
SPI_TLE6244X_MESSAGE_07,
SPI_TLE6244X_MESSAGE_08,
SPI_TLE6244X_MESSAGE_09,
SPI_TLE6244X_MESSAGE_10,
SPI_TLE6244X_MESSAGE_11,
SPI_TLE6244X_MESSAGE_12,
};

/* Messaggi inviati in diagnosi. */
#ifdef TEST_OUT_TLE6244X_MODE
#define COMTLE6244PATTERNSIZE 10
#else
#define COMTLE6244PATTERNSIZE 8
#endif
const uint16_T vComTLE6244Pattern[COMTLE6244PATTERNSIZE] = 
{
/* Controllo tipo di guida per uscite: Diretta I/O oppure SPI. */
#ifdef TEST_OUT_TLE6244X_MODE
SPI_TLE6244X_RD_MUX1,
SPI_TLE6244X_RD_MUX2,
#endif
/* Invio comandi di diagnosi. */
SPI_TLE6244X_RD_STATCON,
SPI_TLE6244X_RD_DIA1,
SPI_TLE6244X_RD_DIA2,
SPI_TLE6244X_RD_DIA3,
SPI_TLE6244X_RD_DIA4,
SPI_TLE6244X_RD_DIA5,
SPI_TLE6244X_DEL_DIA,
SPI_TLE6244X_DUMMY,
};

/* GPIO riferito al pin TLE per diagnosi. */
const uint8_T vMapTLE6244Pin[TLE_NUM_DIAG*TLE_NUM_LINE] = 
{
OUT_TLE_INJ_1B,    
OUT_TLE_INJ_2B,    
OUT_TLE_AIRCUT,
OUT_TLE_CANISTER,
OUT_TLE_INJ_3B,    
OUT_TLE_INJ_4B,    
OUT_TLE_PUMPREL,
OUT_TLE_HORN,
OUT_TLE_LAMB_1,
OUT_TLE_LAMB_2,
OUT_TLE_INJ_1A,    
OUT_TLE_INJ_2A,    
OUT_TLE_INJ_3A,    
OUT_TLE_INJ_4A,    
OUT_TLE_TSS,    
OUT_TLE_VVT,    
OUT_TLE_FAN_1,    
OUT_TLE_FAN_2,    
OUT_TLE_DUMMY,
OUT_TLE_DUMMY,
};

/* Indice di diagnosi. */
#define TLE_NODIAG 255
const uint8_T vtTLE6244Diag[TLE_NUM_DIAG*TLE_NUM_LINE] = 
{
#ifdef IDN_INJECTOR_0
/* 0 */ DIAG_INJECTOR_0,
#else
/* 0 */ TLE_NODIAG, /* Diag Null */
#endif
#ifdef IDN_INJECTOR_1
/* 1 */ DIAG_INJECTOR_1,
#else
/* 1 */ TLE_NODIAG, /* Diag Null */
#endif
#ifdef IDN_DRL_LAMP
/* 2 */ DIAG_DRL_LAMP,
#else
/* 2 */ TLE_NODIAG, /* Diag Null */
#endif
#ifdef OUT_HEATEDGRIP
/* 3 */ DIAG_HEATEDGRIP,
#else
/* 3 */ TLE_NODIAG, /* Diag Null */
#endif
#ifdef IDN_INJECTOR_2
/* 4 */ DIAG_INJECTOR_2,
#else
/* 4 */ TLE_NODIAG, /* Diag Null */
#endif
#ifdef IDN_INJECTOR_3
/* 5 */ DIAG_INJECTOR_3,
#else
/* 5 */ TLE_NODIAG, /* Diag Null */
#endif
/* 6 */ TLE_NODIAG, /* Diag Null */
#ifdef IDN_HIGH_LAMP
/* 7 */ DIAG_LOW_BEAM,
#else
/* 7 */ TLE_NODIAG, /* Diag Null */
#endif
/* 8 */ DIAG_LSD_HLAMBDA,
/* 9 */ DIAG_LSD_HLAMBDA_2,
#ifdef IDN_INJECTOR_4
/*10 */ DIAG_INJECTOR_4,
#else
/*10 */ TLE_NODIAG, /* Diag Null */
#endif    
#ifdef IDN_INJECTOR_5
/*11 */ DIAG_INJECTOR_5,
#else
/*11 */ TLE_NODIAG, /* Diag Null */
#endif    
#ifdef IDN_INJECTOR_6
/*12 */ DIAG_INJECTOR_6,
#else
/*12 */ TLE_NODIAG, /* Diag Null */
#endif
#ifdef IDN_INJECTOR_7
/*13 */ DIAG_INJECTOR_7,
#else
/*13 */ TLE_NODIAG, /* Diag Null */
#endif
#ifdef OUT_TSS
/*14 */ DIAG_TSS,
#else
/*14 */ TLE_NODIAG, /* Diag Null */
#endif
/*15 */ TLE_NODIAG, /* Diag Null */
#ifdef  USE_TLE_PIN_17
/*16 */ DIAG_LSD_FANCOIL,
#else
/*16 */ TLE_NODIAG, /* Diag Null */
#endif
#ifdef  USE_TLE_PIN_18
/*17 */ DIAG_LSD_MAIN_REL,
#else
/*17 */ TLE_NODIAG, /* Diag Null */
#endif
/*18 */ TLE_NODIAG, /* Diag Null */
/*19 */ TLE_NODIAG, /* Diag Null */
};

/* Bit 0 Caso con Relay Load, Bit 1 Caso che non usa Realy Load. */
/* Es. 0x00000022, Canali 2 e 6 non usano il Realy Load. Range da 1 a 18 */
const uint32_T vtTLE6244Load = 0x000300C4; /* I canali 2, 6, 7, 17 e 18 non usano il Realy Load. */
#define VTTLE6244_LOAD_MASK 0x00000001

#ifdef EXTEND_MESSAGE
uint8_T spiTLE6244XID;
uint8_T spiTLE6244XRel;
uint8_T spiTLE6244XScon1;
uint8_T spiTLE6244XScon2;
uint8_T spiTLE6244XInp1;
uint8_T spiTLE6244XInp2;
#endif
uint8_T spiTLE6244XMux1;
uint8_T spiTLE6244XMux2;
uint8_T spiTLE6244XScon3;
uint8_T spiTLE6244XDia1;
uint8_T spiTLE6244XDia2;
uint8_T spiTLE6244XDia3;
uint8_T spiTLE6244XDia4;
uint8_T spiTLE6244XDia5;
uint8_T spiTLE6244XStacon;
uint8_T spiTLE6244XConf;
uint8_T spiTLE6244XOverTemp;
uint8_T spiTLE6244XOverVolt;
uint8_T spiTLE6244XUnderVolt;
uint8_T spiTLE6244XGenErr;
uint8_T spiTLE6244XOut17;
uint8_T spiTLE6244XOut18;
uint8_T spiTLE6244XHandShake; /* Conta ricezioni SPI. */

/* Var interne. */
static uint8_T spi_TLE6244X_EngageCOM;
static uint16_T spi_TLE6244X_OldMsgTx;
static uint8_T spi_TLE6244X_OldMsgRx;
static uint16_T spi_TLE6244X_Init;
static uint8_T spi_TLE6244X_StepCfg;
static uint8_T spi_TLE6244X_NumSample;
static uint16_T spi_TLE6244X_DataBuff_TX[SPI_TLE6244X_MAX_PATTERN];
static uint16_T spi_TLE6244X_DataBuff_RX[SPI_TLE6244X_MAX_PATTERN];
static uint8_T spi_TLE6244X_EvCmdON[TLE_NUM_DIAG*TLE_NUM_LINE];
static uint8_T spi_TLE6244X_EvCmdOFF[TLE_NUM_DIAG*TLE_NUM_LINE];
static uint8_T spi_TLE6244X_EvLastCmdON[TLE_NUM_DIAG*TLE_NUM_LINE];
static uint8_T spi_TLE6244X_EvLastCmdOFF[TLE_NUM_DIAG*TLE_NUM_LINE];
static uint8_T spi_TLE6244X_EvLastLev[TLE_NUM_DIAG*TLE_NUM_LINE];
#ifdef LOG_DIAGNOSIS_TLE
static uint16_T spi_TLE6244X_Flag;
#endif

/* Var esterne. */
uint8_T SpiTLE6244XABE;
uint8_T SpiTLE6244XReady;
#ifdef LOG_DIAGNOSIS_TLE
uint16_T RetTLE6244XError;
#endif

extern uint8_T PtFault[DIAG_NUMBER];

/* Calibrazioni. */
extern uint16_T SPITLE6244XDEBFRAME;
extern uint8_T  SPITLE6244XFAN1;
extern uint8_T  SPITLE6244XFAN2;
extern uint8_T  ENSPITLE6244X18OUT;

/* Prototipi. */
static int16_T manageSPI_TLE6244X_Data (int16_T ret);
static int16_T setSPI_TLE6244X_Frame (uint16_T cmd);
static int16_T sPI_TLE6244X_Diag (uint8_T drv17, uint8_T drv18);
static void spiTLE6244X_diagStatus (uint8_T groupDiag, uint8_T line);
static void spiTLE6244X_GeneralStatus (uint8_T groupDiag);
static int16_T setSPI_TLE6244X_FrameBuffered (uint16_T *cmd, uint8_T size);

/**********************************************************************************************************
// SPI_TLE6244X_Init (void).
// Inizializza le var del TLE6244X. */
int16_T SPI_TLE6244X_Init (void) 
{
    uint8_T i;
    int16_T ret;
    if ((spi_TLE6244X_Init != TLE6244X_RQCFG) && (spi_TLE6244X_Init != TLE6244X_ACK_TRX))
    {
        /* Init variabili. */
        #ifdef EXTEND_MESSAGE
        spiTLE6244XID = 0;
        spiTLE6244XRel = 0;
        spiTLE6244XInp1 = 0;
        spiTLE6244XInp2 = 0;
        spiTLE6244XScon1 = 0;
        spiTLE6244XScon2 = 0;
        spi_TLE6244X_Flag = 0;
        #endif
        spiTLE6244XMux1 = 0;
        spiTLE6244XMux2 = 0;
        spiTLE6244XScon3 = 0;
        spiTLE6244XStacon = 0;
        spiTLE6244XDia1 = 0;
        spiTLE6244XDia2 = 0;
        spiTLE6244XDia3 = 0;
        spiTLE6244XDia4 = 0;
        spiTLE6244XDia5 = 0;
        spiTLE6244XConf = 0;
        spiTLE6244XOverTemp = 0;
        spiTLE6244XOverVolt = 0;
        spiTLE6244XUnderVolt = 0;
        spiTLE6244XGenErr = 0;
        spi_TLE6244X_OldMsgTx = SPI_TLE6244X_RESERVED;
        spi_TLE6244X_OldMsgRx = 0;
        spi_TLE6244X_EngageCOM = TLE6244X_ACK_TRX;
        spi_TLE6244X_NumSample = 0;
        spi_TLE6244X_StepCfg = 0;
        i = 0;
        do
        {
            spi_TLE6244X_DataBuff_TX[i] = SPI_TLE6244X_RESERVED;
            spi_TLE6244X_DataBuff_RX[i] = 0;
            i++;
        } while (i < SPI_TLE6244X_MAX_PATTERN);
        i = 0;
        do
        {
            spi_TLE6244X_EvCmdON[i] = 0;
            spi_TLE6244X_EvCmdOFF[i] = 0;
            spi_TLE6244X_EvLastCmdON[i] = 0;
            spi_TLE6244X_EvLastCmdOFF[i] = 0;
            spi_TLE6244X_EvLastLev[i] = 0;
            i++;
        } while (i < (TLE_NUM_DIAG*TLE_NUM_LINE));
        /* Flag di config terminata del TLE6244X. */
        spi_TLE6244X_Init = TLE6244X_RQCFG;
        SpiTLE6244XReady = 0;
        SpiTLE6244XABE = SPI_TLE6244X_LOAD_NOTFBK;
        ret = NO_ERROR;
    }
    else
    {
        ret = PERIPHERAL_NOT_INITIALIZED;
    }
    return ret;
}

/* ConfigSPI_TLE6244X (void).
// Configura i pin per pilotare in TLE6244X Spi ed invia al TLE6244X il primo pattern di configurazione. */
int16_T ConfigSPI_TLE6244X (void) 
{
    int16_T ret;
    if (spi_TLE6244X_Init == TLE6244X_RQCFG)
    {
        /* Init variabili. */
        spi_TLE6244X_OldMsgTx = SPI_TLE6244X_RESERVED;
        spi_TLE6244X_OldMsgRx = 0;
        spi_TLE6244X_NumSample = 0;
        spi_TLE6244X_StepCfg = 0;
        #ifdef LOG_DIAGNOSIS_TLE
        spi_TLE6244X_Flag = 0;
        #endif
        spi_TLE6244X_EngageCOM = TLE6244X_ACK_TRX;
        /* Init pattern */
        setSPI_TLE6244X_FrameBuffered ((uint16_T *)&vConfigTLE6244Pattern[0], CFGTLE6244PATTERNSIZE);
        /* Flag di config terminata del TLE6244X. */
        spi_TLE6244X_Init = TLE6244X_ACK_TRX;
        /* Invio dati. */
        ret = SendSPI_TLE6244X_FrameBuffer();
        #ifdef LOG_DIAGNOSIS_TLE
        if (ret != NO_ERROR)
        {
            RetTLE6244XError = RetTLE6244XError | 0x0001; /* Send error. */
        }
        else
        {
            /* Non fare niente. */
        }
        #endif
    }
    else
    {
        ret = PERIPHERAL_NOT_INITIALIZED;
    }
    return ret;
}

/* int16_T SendSPI_TLE6244X_FrameBuffer (void).
// Setta un buffer per l'invio dati verso il TLE (MultiFrame). */
int16_T SendSPI_TLE6244X_FrameBuffer (void) 
{
    int16_T ret;
    uint8_t status;
    timeoutHandler_t configTimeout;
    
    /* Se Configurato */
    if ((spi_TLE6244X_Init != TLE6244X_NOTCFG) && (spi_TLE6244X_EngageCOM == TLE6244X_ACK_TRX))
    {
        GetResource (RES_SPI_CHC); /* Lock Risorsa SPI_C */  
        spi_TLE6244X_EngageCOM = TLE6244X_LOCKED_TRX; /* Blocco la scrittura del vettore d'invio dati, in caso di uso di setSPI_TLE6244X_Frame non corretto. */
        /* Se i parametri sono in range del vettore. */
        if ((spi_TLE6244X_NumSample > 0) && (spi_TLE6244X_NumSample < SPI_TLE6244X_MAX_PATTERN))
        {        
            if (GetSPIResFlag(SPI_TLE6244X_CH) == SPI_FREE)
            {
                uint8_T wExit = 0;
                SetSPIResFlag(SPI_TLE6244X_CH); /* Lock Flag per ricezione ad interrupt. */
                ret = SPI_RxTxBuffer (SPI_TLE6244X_CH, &spi_TLE6244X_DataBuff_TX[0], spi_TLE6244X_NumSample, SPI_TLE6244X_PCS); /* Invio dei dati SPI. */
                TIMING_SetTimeout(SPI_TIMEOUT, &configTimeout); /* Setto un timeout in caso di errore di periferica. */
                status = TIMEOUT_PENDING;
                /* Attesa passiva che il canale abbia TX e RX, rilasciato dall'interrupt. */
                while ((GetSPIResFlag(SPI_TLE6244X_CH) == SPI_BUSY) && (status == TIMEOUT_PENDING) && (wExit == 0))
                {
                    if (ret == NO_ERROR)
                    {
                        TIMING_GetTimeoutStatus(configTimeout, &status);
                    }
                    else
                    {
                        wExit = 1; /* Esci dal while, DMA BUSY. */
                    }
                }
                /* Se timeout rilascio il Flag che sarebbe dovuto essere svalidato in interrupt. */
                if ((status == TIMEOUT_EXPIRED) || (ret != NO_ERROR))
                {
                    SPI_Disable(SPI_TLE6244X_CH);
                    ResetSPIResFlag(SPI_TLE6244X_CH); /* Forza la liberazione del Flag di risorsa. */
                    ReleaseResource (RES_SPI_CHC); /* UnLock Risorsa SPI_C */
                    dbCntMF_TLE++; /* Contatore debug, Numero di timeout. */
                    ret = COMUNICATION_TX_ERROR;
                }
                /* Analisi dei dati ricevuti. */
                else
                {
                    ret = SPI_GetRxData (SPI_TLE6244X_CH, &spi_TLE6244X_DataBuff_RX[0], spi_TLE6244X_NumSample);
                    ReleaseResource (RES_SPI_CHC); /* UnLock Risorsa SPI_C */
                    ret = manageSPI_TLE6244X_Data (ret); /* Gestione messaggi di ricezione per TLE6244X */
                }
            }
            else
            {
                ReleaseResource (RES_SPI_CHC); /* UnLock Risorsa SPI_C */
                dbCntMF_TLE++; /* Contatore debug, Numero di timeout. */
                ret = COMMAND_SKIPPED;
            }   
        }
        /* Errore dei parametri, reset variabili. */
        else
        {
            ReleaseResource (RES_SPI_CHC); /* UnLock Risorsa SPI_C */
            #ifdef LOG_DIAGNOSIS_TLE
            RetTLE6244XError = RetTLE6244XError | 0x0080; /* Comando non inviato. */
            #endif
            ret = FLUX_PARAMETER_ERROR; /* Comando non inviato. */
        }
    }
    /* Periferica non configurata. */
    else
    {
        ret = PERIPHERAL_NOT_CONFIGURED;
    }
    spi_TLE6244X_NumSample = 0; /* Reset numero di mess da inviare. */
    spi_TLE6244X_EngageCOM = TLE6244X_ACK_TRX; /* Riabilitazione scrittura messaggi */
    return ret;
}

/* int16_T setSPI_TLE6244X_Frame (uint16_T cmd).
// Setta un comando.
// cmd: comando. */
static int16_T setSPI_TLE6244X_Frame (uint16_T cmd) 
{
    int16_T ret;
    if ((spi_TLE6244X_EngageCOM == TLE6244X_ACK_TRX) && (spi_TLE6244X_NumSample < SPI_TLE6244X_MAX_PATTERN))
    {
        spi_TLE6244X_DataBuff_TX[spi_TLE6244X_NumSample] = cmd;
        spi_TLE6244X_NumSample++;
        ret = NO_ERROR;
    }
    else
    {
        ret = FLUX_PARAMETER_ERROR;
    }
    return ret;
}

/* int16_T setSPI_TLE6244X_FrameBuffered (uint16_T *cmd, uint8_T size).
// Setta un comando.
// *cmd: puntatore ad array di comandi. 
// size: dimensione dell'array */
static int16_T setSPI_TLE6244X_FrameBuffered (uint16_T *cmd, uint8_T size) 
{
    uint8_T i;
    int16_T ret;
    if ((spi_TLE6244X_EngageCOM == TLE6244X_ACK_TRX) && ((size + spi_TLE6244X_NumSample) <= SPI_TLE6244X_MAX_PATTERN))
    {
        i = 0;
        while (i < size)
        {
            spi_TLE6244X_DataBuff_TX[spi_TLE6244X_NumSample] = cmd[i];
            spi_TLE6244X_NumSample++;
            i++;
        }
        ret = NO_ERROR;
    }
    else
    {
        ret = FLUX_PARAMETER_ERROR;
    }
    return ret;
}

/* int16_T sPI_TLE6244X_Diag (uint8_T drv17, uint8_T drv18).
// drv17: Pilotaggio dell'uscita17 via SPI.
// drv18: Pilotaggio dell'uscita18 via SPI.
// Controlla le diagnosi TLE ed attua i carichi. */
static int16_T sPI_TLE6244X_Diag (uint8_T drv17, uint8_T drv18)
{
    int16_T ret;
    if ((spi_TLE6244X_EngageCOM == TLE6244X_ACK_TRX) && (spi_TLE6244X_NumSample < SPI_TLE6244X_MAX_PATTERN))
    {
        drv17 = drv17 & 0x01;
        drv18 = drv18 & 0x01;
        /* Messaggio Attuazione Carichi. */
        #ifdef  USE_TLE_PIN_17
            spiTLE6244XOut17 = drv17;
        #else
            spiTLE6244XOut17 = (~SPITLE6244XFAN1) & 0x01;
        #endif
        #ifdef  USE_TLE_PIN_18
            spiTLE6244XOut18 = drv18;
        #else
            spiTLE6244XOut18 = (~SPITLE6244XFAN2) & 0x01;
        #endif
        setSPI_TLE6244X_Frame((uint16_T)SPI_TLE6244X_WR_SCON3 | spiTLE6244XOut17 | (spiTLE6244XOut18 << 1));
        /* Messaggi Diagnosi Pin. */
        setSPI_TLE6244X_FrameBuffered ((uint16_T *)&vComTLE6244Pattern[0], COMTLE6244PATTERNSIZE);
        ret = NO_ERROR;
    }
    else
    {
        ret = FLUX_PARAMETER_ERROR;
    }
    return ret;
} 

/* int16_T manageSPI_TLE6244X_Data (int16_T ret).
// Legge i dati inviati via SPI. */
static int16_T manageSPI_TLE6244X_Data (int16_T ret)
{
    if (ret == NO_ERROR) 
    {
        int8_T i;
        uint8_T tmpNOTACK;
        i = 0;
        tmpNOTACK = 0;
        spiTLE6244XHandShake++;
        do 
        {
            /* Verifico se il dato spedito il frame precedente � stato ricevuto correttamente. */
            if ((spi_TLE6244X_DataBuff_RX[i] & SPI_TLE6244X_MASK_RX) == SPI_TLE6244X_MATCH_RX) 
            {
                switch (spi_TLE6244X_OldMsgTx)
                {
                    #ifdef EXTEND_MESSAGE
                    case SPI_TLE6244X_RD_IDENT1:
                    {
                        spiTLE6244XID = spi_TLE6244X_OldMsgRx;
                        #ifdef LOG_DIAGNOSIS_TLE
                        spi_TLE6244X_Flag = spi_TLE6244X_Flag | RD_IDENT1_TLE6244X;
                        #endif
                    }
                    break;
                    case SPI_TLE6244X_RD_IDENT2:
                    {
                        spiTLE6244XRel = spi_TLE6244X_OldMsgRx;
                        #ifdef LOG_DIAGNOSIS_TLE
                        spi_TLE6244X_Flag = spi_TLE6244X_Flag | RD_IDENT2_TLE6244X;
                        #endif
                    }
                    break;
                    #endif
                    case SPI_TLE6244X_RD_MUX1:
                    {
                        spiTLE6244XMux1 = spi_TLE6244X_OldMsgRx;
                        #ifdef LOG_DIAGNOSIS_TLE
                        spi_TLE6244X_Flag = spi_TLE6244X_Flag | RD_MUX1_TLE6244X;
                        #endif
                        if (spi_TLE6244X_StepCfg == NORMALMODE)
                        {
                            #ifdef TEST_OUT_TLE6244X_MODE
                            if (spiTLE6244XMux1 != SPI_TLE6244X_MUX1DATA_WR)
                            {
                                tmpNOTACK = 2;
                                spi_TLE6244X_StepCfg = CFGCTRL;
                                #ifdef LOG_DIAGNOSIS_TLE
                                RetTLE6244XError = RetTLE6244XError | 0x0100; /* Command not assigned. */
                                #endif
                            }
                            else
                            {
                                /* Non fare niente -> Ok. */
                            }
                            #endif
                        }
                        else /* CFGCTRL */
                        {
                            if (spiTLE6244XMux1 != SPI_TLE6244X_MUX1DATA_WR)
                            {
                                tmpNOTACK = 2;
                                #ifdef LOG_DIAGNOSIS_TLE
                                RetTLE6244XError = RetTLE6244XError | 0x0100; /* Command not assigned. */
                                #endif
                            }
                            else
                            {
                                /* Non fare niente -> Ok. */
                            }
                        }
                    }
                    break;
                    case SPI_TLE6244X_RD_MUX2:
                    {
                        spiTLE6244XMux2 = spi_TLE6244X_OldMsgRx;
                        #ifdef LOG_DIAGNOSIS_TLE
                        spi_TLE6244X_Flag = spi_TLE6244X_Flag | RD_MUX2_TLE6244X;
                        #endif
                        if (spi_TLE6244X_StepCfg == NORMALMODE)
                        {
                            #ifdef TEST_OUT_TLE6244X_MODE
                            if (spiTLE6244XMux2 != SPI_TLE6244X_MUX2DATA_WR)
                            {
                                tmpNOTACK = 2;
                                spi_TLE6244X_StepCfg = CFGCTRL;
                                #ifdef LOG_DIAGNOSIS_TLE
                                RetTLE6244XError = RetTLE6244XError | 0x0100; /* Command not assigned. */
                                #endif
                            }
                            else
                            {
                                /* Non fare niente -> Ok. */
                            }
                            #endif
                        }
                        else /* CFGCTRL */
                        {
                            if (spiTLE6244XMux2 != SPI_TLE6244X_MUX2DATA_WR)
                            {
                                tmpNOTACK = 2;
                                #ifdef LOG_DIAGNOSIS_TLE
                                RetTLE6244XError = RetTLE6244XError | 0x0100; /* Command not assigned. */
                                #endif
                            }
                            else
                            {
                                /* Non fare niente -> Ok. */
                            }
                        }
                    }
                    break;
                    #ifdef EXTEND_MESSAGE
                    case SPI_TLE6244X_RD_SCON1:
                    {
                        spiTLE6244XScon1 = spi_TLE6244X_OldMsgRx;
                        #ifdef LOG_DIAGNOSIS_TLE
                        spi_TLE6244X_Flag = spi_TLE6244X_Flag | RD_SCON1_TLE6244X;
                        #endif
                    }
                    break;
                    case SPI_TLE6244X_RD_SCON2:
                    {
                        spiTLE6244XScon2 = spi_TLE6244X_OldMsgRx;
                        #ifdef LOG_DIAGNOSIS_TLE
                        spi_TLE6244X_Flag = spi_TLE6244X_Flag | RD_SCON2_TLE6244X;
                        #endif
                    }
                    break;
                    #endif
                    case SPI_TLE6244X_RD_SCON3:
                    {
                        spiTLE6244XScon3 = spi_TLE6244X_OldMsgRx;
                        #ifdef LOG_DIAGNOSIS_TLE
                        spi_TLE6244X_Flag = spi_TLE6244X_Flag | RD_SCON3_TLE6244X;
                        #endif
                        if (spi_TLE6244X_StepCfg == NORMALMODE)
                        {
                            /* Non fare niente -> Ok. */
                        }
                        else /* CFGCTRL */
                        {
                            if (spiTLE6244XScon3 != SPI_TLE6244X_SCON3DATA_WR)
                            {
                                tmpNOTACK = 2;
                                #ifdef LOG_DIAGNOSIS_TLE
                                RetTLE6244XError = RetTLE6244XError | 0x0100; /* Command not assigned. */
                                #endif
                            }
                            else
                            {
                                /* Non fare niente -> Ok. */
                            }
                        }
                    }
                    break;
                    case SPI_TLE6244X_RD_STATCON:
                    {
                        spiTLE6244XStacon = spi_TLE6244X_OldMsgRx;
                        #ifdef LOG_DIAGNOSIS_TLE
                        #ifdef LOG_DIAGNOSIS_STCON
                            debugSPIMsg[bdSPIMsgIdx] = spiTLE6244XStacon;
                            if(bdSPIMsgIdx < (Maxdeb-1)) bdSPIMsgIdx++;
                            else bdSPIMsgIdx = 0;
                        #endif
                        spi_TLE6244X_Flag = spi_TLE6244X_Flag | RD_STATCON_TLE6244X;
                        #endif
                        if (spi_TLE6244X_StepCfg == NORMALMODE)
                        {
                            spiTLE6244X_GeneralStatus (spiTLE6244XStacon);
                            #ifdef TEST_OUT_TLE6244X_MODE
                            if ((spiTLE6244XStacon & 0xE0) != SPI_TLE6244X_SETSTAT)
                            {
                                tmpNOTACK = 2;
                                spi_TLE6244X_StepCfg = CFGCTRL;
                                #ifdef LOG_DIAGNOSIS_TLE
                                RetTLE6244XError = RetTLE6244XError | 0x0100; /* Command not assigned. */
                                #endif
                            }
                            else
                            {
                                /* Non fare niente -> Ok. */
                            }
                            #endif
                        }
                        else /* CFGCTRL */
                        {
                            if ((spiTLE6244XStacon & 0xE0) != SPI_TLE6244X_SETSTAT)
                            {
                                tmpNOTACK = 2;
                                #ifdef LOG_DIAGNOSIS_TLE
                                RetTLE6244XError = RetTLE6244XError | 0x0100; /* Command not assigned. */
                                #endif
                            }
                            else
                            {
                                /* Non fare niente -> Ok. */
                            }
                        }
                    }
                    break;
                    case SPI_TLE6244X_RD_DIA1:
                    {
                        spiTLE6244XDia1 = spi_TLE6244X_OldMsgRx;
                        #ifdef LOG_DIAGNOSIS_TLE
                        #ifdef LOG_DIAGNOSIS_1
                            debugSPIMsg[bdSPIMsgIdx] = spiTLE6244XDia1;
                            if(bdSPIMsgIdx < (Maxdeb-1)) bdSPIMsgIdx++;
                            else bdSPIMsgIdx = 0;
                        #endif
                        spi_TLE6244X_Flag = spi_TLE6244X_Flag | RD_DIA1_TLE6244X;
                        #endif
                        spiTLE6244X_diagStatus (spiTLE6244XDia1, TLE_LINE_DIAG_1);
                    }
                    break;
                    case SPI_TLE6244X_RD_DIA2:
                    { 
                        spiTLE6244XDia2 = spi_TLE6244X_OldMsgRx;
                        #ifdef LOG_DIAGNOSIS_TLE
                        #ifdef LOG_DIAGNOSIS_2
                            debugSPIMsg[bdSPIMsgIdx] = spiTLE6244XDia2;
                            if(bdSPIMsgIdx < (Maxdeb-1)) bdSPIMsgIdx++;
                            else bdSPIMsgIdx = 0;
                        #endif
                        spi_TLE6244X_Flag = spi_TLE6244X_Flag | RD_DIA2_TLE6244X;
                        #endif
                        spiTLE6244X_diagStatus (spiTLE6244XDia2, TLE_LINE_DIAG_2);
                    }
                    break;
                    case SPI_TLE6244X_RD_DIA3:
                    {
                        spiTLE6244XDia3 = spi_TLE6244X_OldMsgRx;
                        #ifdef LOG_DIAGNOSIS_TLE
                        #ifdef LOG_DIAGNOSIS_3
                            debugSPIMsg[bdSPIMsgIdx] = spiTLE6244XDia3;
                            if(bdSPIMsgIdx < (Maxdeb-1)) bdSPIMsgIdx++;
                            else bdSPIMsgIdx = 0;
                        #endif
                        spi_TLE6244X_Flag = spi_TLE6244X_Flag | RD_DIA3_TLE6244X;
                        #endif
                        spiTLE6244X_diagStatus (spiTLE6244XDia3, TLE_LINE_DIAG_3);
                    }
                    break;
                    case SPI_TLE6244X_RD_DIA4:
                    {
                        spiTLE6244XDia4 = spi_TLE6244X_OldMsgRx;
                        #ifdef LOG_DIAGNOSIS_TLE
                        #ifdef LOG_DIAGNOSIS_4
                            debugSPIMsg[bdSPIMsgIdx] = spiTLE6244XDia4;
                            if(bdSPIMsgIdx < (Maxdeb-1)) bdSPIMsgIdx++;
                            else bdSPIMsgIdx = 0;
                        #endif
                        spi_TLE6244X_Flag = spi_TLE6244X_Flag | RD_DIA4_TLE6244X;
                        #endif
                        spiTLE6244X_diagStatus (spiTLE6244XDia4, TLE_LINE_DIAG_4);
                    }
                    break;
                    case SPI_TLE6244X_RD_DIA5:
                    {
                        spiTLE6244XDia5 = spi_TLE6244X_OldMsgRx;
                        #ifdef LOG_DIAGNOSIS_TLE
                        #ifdef LOG_DIAGNOSIS_5
                            debugSPIMsg[bdSPIMsgIdx] = spiTLE6244XDia5;
                            if(bdSPIMsgIdx < (Maxdeb-1)) bdSPIMsgIdx++;
                            else bdSPIMsgIdx = 0;
                        #endif
                        spi_TLE6244X_Flag = spi_TLE6244X_Flag | RD_DIA5_TLE6244X;
                        #endif
                        spiTLE6244X_diagStatus (spiTLE6244XDia5, TLE_LINE_DIAG_5);
                    }
                    break;
                    case SPI_TLE6244X_RD_CONFIG:
                    {
                        spiTLE6244XConf = spi_TLE6244X_OldMsgRx;
                        #ifdef LOG_DIAGNOSIS_TLE
                        spi_TLE6244X_Flag = spi_TLE6244X_Flag | RD_CONFIG_TLE6244X;
                        #endif
                        if (spi_TLE6244X_StepCfg == NORMALMODE)
                        {
                            /* Non fare niente -> Ok. */
                        }
                        else /* CFGCTRL */
                        {
                            if (spiTLE6244XConf != SPI_TLE6244X_SETCFG)
                            {
                                tmpNOTACK = 2;
                                #ifdef LOG_DIAGNOSIS_TLE
                                RetTLE6244XError = RetTLE6244XError | 0x0100; /* Command not assigned. */
                                #endif
                            }
                            else
                            {
                                /* Non fare niente -> Ok. */
                            }
                        }
                    }
                    break;
                    #ifdef EXTEND_MESSAGE
                    case SPI_TLE6244X_RD_INP1:
                    {
                        spiTLE6244XInp1 = spi_TLE6244X_OldMsgRx;
                        #ifdef LOG_DIAGNOSIS_TLE
                        spi_TLE6244X_Flag = spi_TLE6244X_Flag | RD_INP1_TLE6244X;
                        #endif
                    }
                    break;
                    case SPI_TLE6244X_RD_INP2:
                    {
                        spiTLE6244XInp2 = spi_TLE6244X_OldMsgRx;
                        #ifdef LOG_DIAGNOSIS_TLE
                        spi_TLE6244X_Flag = spi_TLE6244X_Flag | RD_INP2_TLE6244X;
                        #endif
                    }
                    break;
                    #endif
                    default:
                    {
                        /* Non fare niernte, comando non implementato. */
                    }
                    break;
                }
            }
            else
            {
                if ((spi_TLE6244X_DataBuff_RX[i] & 0x0100) != 0)
                {
                    ret = PARITY_ERROR; /* Parit� errata. */
                    #ifdef LOG_DIAGNOSIS_TLE
                    RetTLE6244XError = RetTLE6244XError | 0x0008; /* Parit� error. */
                    #endif
                }
                else
                {
                    spi_TLE6244X_OldMsgTx = spi_TLE6244X_DataBuff_TX[i] & 0xFF00; /* In questo caso segnala l'errore istantaneamente e non un giro dopo. */
                    ret = COMUNICATION_ERROR; /* Comunicazione errata. */
                    #ifdef LOG_DIAGNOSIS_TLE
                    RetTLE6244XError = RetTLE6244XError | 0x0010; /* Command error. */
                    #endif
                }
                #ifdef RECOVERY_MESSAGE /* Determina quale messaggio non � stato spedito. */
                switch (spi_TLE6244X_OldMsgTx)
                {
                    #ifdef EXTEND_MESSAGE
                    case SPI_TLE6244X_RD_IDENT1:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    case SPI_TLE6244X_RD_IDENT2:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    #endif
                    case SPI_TLE6244X_RD_MUX1:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    case SPI_TLE6244X_RD_MUX2:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    #ifdef EXTEND_MESSAGE
                    case SPI_TLE6244X_RD_SCON1:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    case SPI_TLE6244X_RD_SCON2:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    #endif
                    case SPI_TLE6244X_RD_SCON3:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    case SPI_TLE6244X_RD_STATCON:
                    {
                        SpiTLE6244XABE = SPI_TLE6244X_LOAD_NOTFBK;
                        tmpNOTACK = 1;
                    }
                    break;
                    case SPI_TLE6244X_RD_DIA1:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    case SPI_TLE6244X_RD_DIA2:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    case SPI_TLE6244X_RD_DIA3:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    case SPI_TLE6244X_RD_DIA4:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    case SPI_TLE6244X_RD_DIA5:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    case SPI_TLE6244X_RD_CONFIG:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    #ifdef EXTEND_MESSAGE
                    case SPI_TLE6244X_RD_INP1:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    case SPI_TLE6244X_RD_INP2:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    #endif
                    case SPI_TLE6244X_WR_STATCON:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    case SPI_TLE6244X_WR_MUX1:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    case SPI_TLE6244X_WR_MUX2:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    #ifdef EXTEND_MESSAGE
                    case SPI_TLE6244X_WR_SCON1:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    case SPI_TLE6244X_WR_SCON2:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    #endif
                    case SPI_TLE6244X_WR_SCON3:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    case SPI_TLE6244X_WR_CONFIG:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    case SPI_TLE6244X_DEL_DIA:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    case SPI_TLE6244X_RESERVED:
                    {
                        tmpNOTACK = 1;
                    }
                    break;
                    default:
                    {
                        tmpNOTACK = 1;
                        ret = INSTRUCTION_ERROR; /* Comando trasmesso errato !CONTROLLARE DEFINE se entra (Non deve mai entrare). */
                        #ifdef LOG_DIAGNOSIS_TLE
                        RetTLE6244XError = RetTLE6244XError | 0x0020; /* Istruzione error. */
                        #endif
                    }
                    break;
                }
                #else
                tmpNOTACK = 1;
                #endif
            }
            spi_TLE6244X_OldMsgTx = spi_TLE6244X_DataBuff_TX[i] & 0xFF00;
            spi_TLE6244X_OldMsgRx = (uint8_T)(spi_TLE6244X_DataBuff_RX[i] & 0xFF);
            i++;
        } while (i < spi_TLE6244X_NumSample);
        if (tmpNOTACK != 0) /* Errore riscontrato. */
        {
            if (spi_TLE6244X_StepCfg == NORMALMODE)
            {
                /* Errore SPI. */
            }
            else /* CFGCTRL */
            {
                spi_TLE6244X_Init = TLE6244X_RQCFG;
                ret = PERIPHERAL_NOT_CONFIGURED; /* Configurazione errata. */
                #ifdef LOG_DIAGNOSIS_TLE
                RetTLE6244XError = RetTLE6244XError | 0x0040; /* Config error. */
                #endif
                /* Errore SPI. */
            }
            SpiTLE6244XReady = 0; /* Segnalato SPI non Ready. */
        }
        else /* Com Corretta. */
        {
            SpiTLE6244XReady = 1; /* Segnalato SPI Ready. */
            spi_TLE6244X_StepCfg = NORMALMODE;
        }
    }
    else
    {
        /* Non fare niente, errore di ricezione nell'assegnamento parametri. */
    }
    return ret;
}

/* spiTLE6244X_GeneralStatus (uint8_T groupDiag).
// groupDiag: parametro che ragruppa varie diagnosi.
// Analisi delle diagnosi globali per il TLE. */
static void spiTLE6244X_GeneralStatus (uint8_T groupDiag)
{
    uint8_T stDiagRes;
    /* Caso TLE_OVERVOLTAGE */
    if ((groupDiag & TLE_OVERVOLTAGE) == 0)
    {
        spiTLE6244XOverVolt = 1; /* Riscontrato una sovratensione su VCC. */
    }
    else
    {
        spiTLE6244XOverVolt = 0; /* Nessuna sovratensione su Vcc. -> Ok. */
    }
    /* Caso TLE_UNDERVOLTAGE */
    if ((groupDiag & TLE_UNDERVOLTAGE) == 0)
    {
        spiTLE6244XUnderVolt = 1; /* Riscontrato una sottotensione su VCC. */
    }
    else
    {
        spiTLE6244XUnderVolt = 0; /* Nessuna sottotensione su Vcc. -> Ok. */
    }
    /* Caso TLE_ABE_STATUS */
    if ((groupDiag & TLE_ABE_STATUS) == 0)
    {
        SpiTLE6244XABE = SPI_TLE6244X_LOAD_OFF; /* Load TLE Disable. */
    }
    else
    {
        SpiTLE6244XABE = SPI_TLE6244X_LOAD_ON; /* Load TLE Enable. */
    }
    /* Caso TLE_GENERAL_ERROR */
    if ((groupDiag & TLE_GENERAL_ERROR) == 0)
    {
        spiTLE6244XGenErr = 1; /* Trovato un errore generico. */
    }
    else
    {
        spiTLE6244XGenErr = 0; /* Nessun errore riscontrato. */
    }
    /* Caso TLE_OVERTEMP */
    if ((groupDiag & TLE_OVERTEMP) == 0)
    {
        spiTLE6244XOverTemp = 1; /* Diagnosi di errore. */
        DiagMgm_SetDiagState(DIAG_TSD_TLE6244, OVERTEMPERATURE, &stDiagRes);
    }
    else
    {
        spiTLE6244XOverTemp = 0; /* Diagnosi Ok. */
        DiagMgm_SetDiagState(DIAG_TSD_TLE6244, NO_PT_FAULT, &stDiagRes);
    }
}

/* spiTLE6244X_diagStatus (uint8_T groupDiag, uint8_T line).
// groupDiag: parametro che ragruppa 4 diagnosi.
// line: identifica i vari gruppi diagnosi.
// Analisi delle diagnosi per i pin del TLE. */
static void spiTLE6244X_diagStatus (uint8_T groupDiag, uint8_T line) 
{
    uint8_T diagIdx;
    uint8_T tmpDiag;
    uint8_T vecDiagIdx;
    uint8_T genDiagIdx;
    uint8_T stDiagRes;
    uint8_T fpin;
    uint8_T fpinPR;
    uint8_T fault;
    uint8_T ptFault;
    uint8_T flgSetDiag; /* Indica se la diagnosi effettuata � significativa */

    vecDiagIdx = 0;
    diagIdx = 0; /* Contatore indice diagnosi. */
    /* Esegui fino che le diagnisi su questa linea sono finite. */
    do 
    {
        vecDiagIdx = diagIdx + (line * 4);
        genDiagIdx = vtTLE6244Diag[vecDiagIdx];
        if ((genDiagIdx != TLE_NODIAG) && (SpiTLE6244XABE == SPI_TLE6244X_LOAD_ON))
        {
            fault = NO_PT_FAULT;
            flgSetDiag = 1;
            ptFault = PtFault[genDiagIdx];
            tmpDiag = (groupDiag >> (diagIdx * 2)) & MASKTLE_SINGLE_DIAG; /* Estrazione singola diagnosi. */
            DIGIO_InGet(OUT_TLE_PUMPREL, &fpinPR); /* Leggo lo stato della pompa relay */ 
            /* Controllo ed adattamento della funzione in caso di logiche funzionali particolari. */
            switch (vecDiagIdx)
            {
                case PIN_WITH_REVERSE_LOGIC:
                {
                    DIGIO_InGet(vMapTLE6244Pin[vecDiagIdx], &fpin); /* Leggo lo stato del pin */
                    fpin = (~fpin) & 0x01; /* Inverto la logica dei pin a logica rovesciata. */
                }
                break;
                case PIN_WITH_ONLY_SPI_17:
                {
                    fpin = spiTLE6244XOut17; /* pin virtuale, � una variabile di stato. */
                }
                break;
                case PIN_WITH_ONLY_SPI_18:
                {
                    fpin = spiTLE6244XOut18; /* pin virtuale, � una variabile di stato. */
                }
                break;
                default:
                {
                    DIGIO_InGet(vMapTLE6244Pin[vecDiagIdx], &fpin); /* Leggo lo stato del pin */ 
                }
                break;
            }
            /* Controllo il tipo di diagnosi segnalata. */
            switch (tmpDiag)
            {
                case TLE_SHORT_CIRCUIT_GND:
                {
                    ResetEv_CmdOFF_spiTLE6244X(vecDiagIdx);
                    /* Se il Relay pompa � attivo, qualsiasi circuito a GND � errore. */
                    if ((GetFilterPRPinStatus() >= LOAD_ON_CNT) && (fpinPR == TLE6244X_CMD_ON))
                    {
                        fault = CC_TO_GND;
                    }
                    /* Se il Relay pompa � disattivo, i circuiti non gestiti dal relay sono in errore. */
                    else
                    {
                        if (((vtTLE6244Load >> vecDiagIdx) & VTTLE6244_LOAD_MASK) != 0)
                        {
                            fault = CC_TO_GND;
                        }
                        else /* Non si pu� fare diagnosi perch� Relay disabilitato che apre i suoi carichi.*/
                        {
                            flgSetDiag = 0;
                        }
                    } 
                }
                break;
                case TLE_CIRCUIT_OPEN:
                {
                    ResetEv_CmdOFF_spiTLE6244X(vecDiagIdx);
                    /* Se il Relay pompa � attivo, qualsiasi circuito aperto � errore. */
                    if ((GetFilterPRPinStatus() >= LOAD_ON_CNT) && (fpinPR == TLE6244X_CMD_ON))
                    {
                        fault = OPEN_CIRCUIT;
                    }
                    /* Se il Relay pompa � disattivo, i circuiti non gestiti dal relay sono in errore. */
                    else
                    {
                        if (((vtTLE6244Load >> vecDiagIdx) & VTTLE6244_LOAD_MASK) != 0)
                        {
                            fault = OPEN_CIRCUIT;
                        }
                        else /* Non si pu� fare diagnosi perch� Relay disabilitato che apre i suoi carichi.*/
                        {
                            flgSetDiag = 0;
                        }
                    }    
                }
                break;
                case TLE_SHORT_CIRCUIT_VBAT:
                {
                    ResetEv_CmdON_spiTLE6244X(vecDiagIdx);
                    /* Se il Relay pompa � attivo, qualsiasi circuito a VBAT � errore. */
                    if ((GetFilterPRPinStatus() >= LOAD_ON_CNT) && (fpinPR == TLE6244X_CMD_ON))
                    {
                        fault = CC_TO_VBAT;
                    }
                    /* Se il Relay pompa � disattivo, i circuiti non gestiti dal relay sono in errore. */
                    else
                    {
                        if (((vtTLE6244Load >> vecDiagIdx) & VTTLE6244_LOAD_MASK) != 0)
                        {
                            fault = CC_TO_VBAT;
                        }
                        else /* Non si pu� fare diagnosi perch� Relay disabilitato che apre i suoi carichi.*/
                        {
                            flgSetDiag = 0;
                        }
                    }
                }
                break;
                case TLE_CIRCUIT_LOAD: /* Nessun errore. */
                {
                    flgSetDiag = 0;
                    /* Relay carichi attivo e stabile. */
                    if ((GetFilterPRPinStatus() >= LOAD_ON_CNT) && (fpinPR == TLE6244X_CMD_ON))
                    {
                        if ((fpin == TLE6244X_CMD_ON) || (GetEv_CmdON_spiTLE6244X(vecDiagIdx) > 0))
                        { 
                            ResetEv_CmdON_spiTLE6244X(vecDiagIdx);
                            if ((ptFault == CC_TO_VBAT) || (ptFault == NO_PT_FAULT))
                            {
                                flgSetDiag = 1;
                                fault = NO_PT_FAULT;
                            }
                            else
                            {
                                /* Non fare niente. */
                            }
                        }
                        else
                        {
                            /* Non fare niente */
                        }
                        if((fpin == TLE6244X_CMD_OFF) || (GetEv_CmdOFF_spiTLE6244X(vecDiagIdx) > 0))
                        {
                            ResetEv_CmdOFF_spiTLE6244X(vecDiagIdx);
                            if ((ptFault == CC_TO_GND) || (ptFault == OPEN_CIRCUIT) || (ptFault == NO_PT_FAULT))
                            {
                                flgSetDiag = 1;
                                fault = NO_PT_FAULT;
                            }
                            else
                            {
                                /* Non fare niente. */
                            }
                        }
                        else
                        {
                            /* Non fare niente */
                        }
                    }
                    /* Relay carichi non attivo. */
                    else
                    {
                        /* Caso circuiti non sotto Relay Carichi. */
                        if (((vtTLE6244Load >> vecDiagIdx) & VTTLE6244_LOAD_MASK) != 0)
                        {
                            if ((fpin == TLE6244X_CMD_ON) || (GetEv_CmdON_spiTLE6244X(vecDiagIdx) > 0))
                            { 
                                ResetEv_CmdON_spiTLE6244X(vecDiagIdx);
                                if ((ptFault == CC_TO_VBAT) || (ptFault == NO_PT_FAULT))
                                {
                                    flgSetDiag = 1;
                                    fault = NO_PT_FAULT;
                                }
                                else
                                {
                                    /* Non fare niente. */
                                }
                            }
                            else
                            {
                                /* Non fare niente */
                            }
                            if((fpin == TLE6244X_CMD_OFF) || (GetEv_CmdOFF_spiTLE6244X(vecDiagIdx) > 0))
                            {
                                ResetEv_CmdOFF_spiTLE6244X(vecDiagIdx);
                                if ((ptFault == CC_TO_GND) || (ptFault == OPEN_CIRCUIT) || (ptFault == NO_PT_FAULT))
                                {
                                    flgSetDiag = 1;
                                    fault = NO_PT_FAULT;
                                }
                                else
                                {
                                     /* Non fare niente. */
                                }
                            }
                            else
                            {
                                /* Non fare niente */
                            }
                        }
                        /* Caso Circuiti sotto Relay Carichi. */
                        else
                        {                       
                            /* Non fare niente. */
                        }
                    }
                }
                break;
                default:
                {
                    flgSetDiag = 0; /* Non deve entrare mai. */
                }
                break;
            }
            if (flgSetDiag == 1)
            {
                DiagMgm_SetDiagState(genDiagIdx, fault, &stDiagRes);
            }
            else
            {
                /* Non fare niente. */
            }
        }
        else
        {
            /* Non fare niente. */
        }
        diagIdx++;
    } while (diagIdx < TLE_NUM_DIAG);
}

/* SetEv_FrontCnt_spiTLE6244X (uint8_T numECnt, uint8_T isOn).
// numECnt: Indice del pin del TLE che sta attuando.
// isOn: Stato On/Off del pin.
// Setta i fronti di On/Off State del TLE per il pin in esame. */
void SetEv_FrontCnt_spiTLE6244X (uint8_T numECnt, uint8_T isOn)
{
    /* Se il comando � ON. */
    if (isOn == 1)
    {
        spi_TLE6244X_EvCmdON[numECnt]++; /* Incrementa il conta Comandi ON. */
        spi_TLE6244X_EvLastLev[numECnt] = isOn; /* Memorizza il nuovo livello elettrico. */
    }
    /* Se il comando � OFF. */
    else
    {
        spi_TLE6244X_EvCmdOFF[numECnt]++; /* Incrementa il conta Comandi OFF. */
        spi_TLE6244X_EvLastLev[numECnt] = isOn; /* Memorizza il nuovo livello elettrico. */
    }
}

/* SetEv_LevelCnt_spiTLE6244X (uint8_T numECnt, uint8_T isOn).
// numECnt: Indice del pin del TLE che sta attuando.
// isOn: Stato On/Off del pin.
// Setta il livello di On/Off State del TLE per il pin in esame. */
void SetEv_LevelCnt_spiTLE6244X (uint8_T numECnt, uint8_T isOn)
{
    /* Se il livello elettrico visto il giro prima e costante entra. */
    if (isOn == spi_TLE6244X_EvLastLev[numECnt])
    {
        /* Non fare niente */
    }
    /* Memorizza il nuovo livello elettrico. */
    else
    {
        spi_TLE6244X_EvLastLev[numECnt] = isOn;
        if (isOn == 1)
        {
            spi_TLE6244X_EvCmdON[numECnt]++; /* Incrementa il conta Comandi ON. */
        }
        else
        {
            spi_TLE6244X_EvCmdOFF[numECnt]++; /* Incrementa il conta Comandi OFF. */
        }
    }
}

/* ResetEv_CmdON_spiTLE6244X (uint8_T numECnt).
// numECnt: Indice del pin del TLE che sta attuando.
// Resetta l'evento di On State del TLE per il pin in esame. */
void ResetEv_CmdON_spiTLE6244X (uint8_T numECnt)
{
    spi_TLE6244X_EvLastCmdON[numECnt] = spi_TLE6244X_EvCmdON[numECnt];
}

/* GetEv_CmdON_spiTLE6244X (uint8_T numECnt).
// numECnt: Indice del pin del TLE che sta attuando.
// Ottiene l'evento di On State del TLE per il pin in esame. */
uint8_T GetEv_CmdON_spiTLE6244X (uint8_T numECnt)
{
    uint8_T ret;
    if (spi_TLE6244X_EvCmdON[numECnt] != spi_TLE6244X_EvLastCmdON[numECnt])
    {
        ret = 1; /* Evento catturato. */
    }
    else
    {
        ret = 0; /* Nessuna transizione. */
    }
    return ret;
}

/* ResetEv_CmdOFF_spiTLE6244X (uint8_T numECnt).
// numECnt: Indice del pin del TLE che sta attuando.
// Resetta l'evento di Off State del TLE per il pin in esame. */
void ResetEv_CmdOFF_spiTLE6244X (uint8_T numECnt)
{
    spi_TLE6244X_EvLastCmdOFF[numECnt] = spi_TLE6244X_EvCmdOFF[numECnt];
}

/* GetEv_CmdOFF_spiTLE6244X (uint8_T numECnt).
// numECnt: Indice del pin del TLE che sta attuando.
// Ottiene l'evento di Off State del TLE per il pin in esame. */
uint8_T GetEv_CmdOFF_spiTLE6244X (uint8_T numECnt)
{
    uint8_T ret;
    if (spi_TLE6244X_EvCmdOFF[numECnt] != spi_TLE6244X_EvLastCmdOFF[numECnt])
    {
        ret = 1; /* Evento catturato. */
    }
    else
    {
        ret = 0; /* Nessuna transizione. */
    }
    return ret;
}

/* ProgramSpiDAC_T10ms (void).
// Debug da gestire a task 10ms per verifica del funzionamento. */
void ProgramSpi_TLE6244X_T10ms (void) 
{
    uint8_T drv1;
    uint8_T drv2;
    
    drv1 = ~FanCoilCmd;
    if (ENSPITLE6244X18OUT == 1u)
    {
        drv2 = ~FanCoilCmd;
    }
    else if (ENSPITLE6244X18OUT == 2u)
    {
       drv2 = MainRelCmd; // Niso!!!! This is coming from MV6 ~MainRelCmd;
    }
    else
    {
        drv2 = ~0u;
    }
    #ifdef TEST_MSG_TLE6244X
    if (SPITLE6244XDEBFRAME != SPI_TLE6244X_RESERVED)
    {
        setSPI_TLE6244X_Frame(SPITLE6244XDEBFRAME);
        sPI_TLE6244X_Diag (drv1, drv2); /* Formattazione ed invio comandi di diagnosi. */
        SendSPI_TLE6244X_FrameBuffer();
    }
    else
    {
    #endif
        if (spi_TLE6244X_Init == TLE6244X_RQCFG)
        {
            ConfigSPI_TLE6244X (); /* Se la configurazione non ha avuto buon esito, riconfiguro. */
        }
        else
        {
            sPI_TLE6244X_Diag (drv1, drv2); /* Formattazione ed invio comandi di diagnosi. */
            SendSPI_TLE6244X_FrameBuffer();
        }
    #ifdef TEST_MSG_TLE6244X
    }
    #endif
}

int16_T Read_SPI_ABETLE6244X(void)
{
    int16_T ret;
    if (spi_TLE6244X_Init == TLE6244X_RQCFG)
    {
        ret = ConfigSPI_TLE6244X (); /* Se la configurazione non ha avuto buon esito, riconfiguro. */
    }
    else
    {
        setSPI_TLE6244X_Frame(SPI_TLE6244X_RD_STATCON);
        ret = SendSPI_TLE6244X_FrameBuffer();
    }
    return ret;
}

#else

int16_T SPI_TLE6244X_Init (void) 
{
    return RET_FUNCTION_STB;
}

int16_T ConfigSPI_TLE6244X (void) 
{
    return RET_FUNCTION_STB;
}

int16_T SendSPI_TLE6244X_FrameBuffer (void)
{
    return RET_FUNCTION_STB;
}


void ProgramSpi_TLE6244X_T10ms (void)
{
    /* Non fare niente. */
}

int16_T Read_SPI_ABETLE6244X(void)
{
    return 0;
}

uint8_T GetEv_CmdON_spiTLE6244X (uint8_T numECnt)
{
    return 0;
}

void ResetEv_CmdON_spiTLE6244X (uint8_T numECnt)
{
    /* Non fare niente. */
}

void SetEv_FrontCnt_spiTLE6244X (uint8_T numECnt, uint8_T isOn)
{
    /* Non fare niente. */
}

uint8_T GetEv_CmdOFF_spiTLE6244X (uint8_T numECnt)
{
    return 0;
}

void ResetEv_CmdOFF_spiTLE6244X (uint8_T numECnt)
{
    /* Non fare niente. */
}

void SetEv_LevelCnt_spiTLE6244X (uint8_T numECnt, uint8_T isOn)
{
    /* Non fare niente. */
}

#endif

