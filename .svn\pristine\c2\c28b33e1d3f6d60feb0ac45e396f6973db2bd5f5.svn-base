/*****************************************************************************************************************/
/* $HeadURL::                                                                                                $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                           */
/*****************************************************************************************************************/

#ifndef _ENGFLAG_H_
#define _ENGFLAG_H_

/** include files **/
#include "relaymgm.h"
#include "typedefs.h"

#ifdef _BUILD_IDLEMGM_
#define USE_TORQUE_MGM
#else
#undef USE_TORQUE_MGM
#endif

#define RM_RAIN 0
#define RM_URBAN 1
#define RM_SPORT 2
#define RM_RACE 3
#define RM_CUSTOM 4
#define RM_SIZE 5

#define DRVC_OFF   (0)
#define DRVC_START (1)
#define DRVC_CONF  (2)
#define DRVC_ENDED (3)
#define WUC_NOT_ENDED   (0)
#define WUC_ENDED       (1)

/** public data **/
extern uint8_T  EndStartFlg;
extern uint8_T  FlgEconMode;
extern uint8_T  RidingModeCAN;
extern uint8_T  RidingMode;
extern uint8_T  RidingModeTOut;
extern uint8_T  AccSens;
extern uint8_T  MaxTrq;
extern uint8_T  EngResp;
extern uint8_T  RpmLim;
extern uint8_T  EngBrake;
extern uint8_T  DrivingCycle;
extern uint8_T  WarmUpCycle;
extern uint8_T  CntDrivingCycle;
extern uint8_T  CntWarmUpCycle;
extern uint8_T  FlgUDSPwrLSlow;

/** public functions **/
void EngFlag_Init(void);
void EngFlag_T10ms(void);

#endif
