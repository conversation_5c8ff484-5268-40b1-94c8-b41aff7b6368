/*****************************************************************************************************************/
/* $HeadURL:: https://************/svn/Rep_Bo/EM/NISO/MV8/trunk/tree/DD/COMMON/SPITLE6244X.h         $   */
/* $ Description:                                                                                                */
/* $Revision:: 16655  $                                                                                          */
/* $Date:: 2025-05-12 11:08:24 +0300 (Mon, 12 May 2025)   $                                                      */
/* $Author:: durano                  $                                                                       */
/*****************************************************************************************************************/

#ifndef __SPI_TLE6244X__
#define __SPI_TLE6244X__

#include "diagmgm_out.h"
#include "diagmgm_def.h"
#include "digio.h"
#include "injcmd.h"
#include "OS_api.h"
#include "OS_resources.h"
#include "Relaymgm.h"
#include "spi.h"
#include "Timing.h"
#include "typedefs.h"

/* Var Esterne. */
extern uint8_T SpiTLE6244XABE;
extern uint8_T SpiTLE6244XReady;
extern uint16_T RetTLE6244XError;

#define NUMPINOUT_TLE_INJ_1B 0
#define NUMPINOUT_TLE_INJ_2B 1
#define NUMPINOUT_TLE_AIRCUT 2
#define NUMPINOUT_TLE_CANISTER 3
#define NUMPINOUT_TLE_INJ_3B 4
#define NUMPINOUT_TLE_INJ_4B 5
#define NUMPINOUT_TLE_PUMPREL 6
#define NUMPINOUT_TLE_HORN 7
#define NUMPINOUT_TLE_LAMB_1 8
#define NUMPINOUT_TLE_LAMB_2 9
#define NUMPINOUT_TLE_INJ_1A 10
#define NUMPINOUT_TLE_INJ_2A 11
#define NUMPINOUT_TLE_INJ_3A 12
#define NUMPINOUT_TLE_INJ_4A 13
#define NUMPINOUT_TLE_TSS 14
#define NUMPINOUT_TLE_VVT 15
#define NUMPINOUT_TLE_FAN_1 16
#define NUMPINOUT_TLE_FAN_2 17

#undef TEST_MSG_TLE6244X

#ifdef _BUILD_SPITLE6244X_ /* Diriver programmabile via SPI. */

#define FlgFdbDisL3 !SpiTLE6244XABE

/*************************************************************/
/* Comandi. */
#define SPI_TLE6244X_DUMMY 0x0000 /* Legge l'identificativo del Device e anche DUMMY. */
#define SPI_TLE6244X_RD_IDENT1 0x0000 /* Legge l'identificativo del Device. */
#define SPI_TLE6244X_RD_IDENT2 0x0300 /* Legge l'identificativo Sowtware relase del Device. */
#define SPI_TLE6244X_RD_MUX1 0x0500 /* Lettura Modalit� di pilotaggio Driver 1-8. */
#define SPI_TLE6244X_RD_MUX2 0x0600 /* Lettura Modalit� di pilotaggio Driver 9-16. */
#define SPI_TLE6244X_RD_SCON1 0x0900 /* Lettura Stato uscita (SPI Mode) 1-8. */
#define SPI_TLE6244X_RD_SCON2 0x0A00 /* Lettura Stato uscita (SPI Mode) 9-16. */
#define SPI_TLE6244X_RD_SCON3 0x0C00 /* Lettura Stato uscita (SPI Mode) 17-18. */
#define SPI_TLE6244X_RD_STATCON 0x0F00 /* Lettura status del Device. */
#define SPI_TLE6244X_RD_DIA1 0x1100 /* Lettura Diagnosi 1-4. */
#define SPI_TLE6244X_RD_DIA2 0x1200 /* Lettura Diagnosi 5-8. */
#define SPI_TLE6244X_RD_DIA3 0x1400 /* Lettura Diagnosi 9-12. */
#define SPI_TLE6244X_RD_DIA4 0x1700 /* Lettura Diagnosi 13-16. */
#define SPI_TLE6244X_RD_DIA5 0x1800 /* Lettura Diagnosi 17-18, VBatt. */
#define SPI_TLE6244X_RD_CONFIG 0x1B00 /* Lettura Modo di interfaccia comando (uSec-BUS). */
#define SPI_TLE6244X_RD_INP1 0x1D00 /* Lettura stato ingressi 1-5 e 8 (input port expander). */
#define SPI_TLE6244X_RD_INP2 0x1E00 /* Lettura stato ingressi 9-15 (input port expander). */
#define SPI_TLE6244X_WR_STATCON 0x2200 /* Scrittura STATCON_REG per tipologia di diagnosi. */
#define SPI_TLE6244X_WR_MUX1 0x2400 /* Scrittura Modalit� di pilotaggio Driver 1-8 (SPI o Diretta). */
#define SPI_TLE6244X_WR_MUX2 0x2700 /* Scrittura Modalit� di pilotaggio Driver 9-16 (SPI o Diretta). */
#define SPI_TLE6244X_WR_SCON1 0x2800 /* Scrittura Stato uscita (SPI Mode) 1-8. */
#define SPI_TLE6244X_WR_SCON2 0x2B00 /* Scrittura Stato uscita (SPI Mode) 9-16. */
#define SPI_TLE6244X_WR_SCON3 0x2D00 /* Scrittura Stato uscita (SPI Mode) 17-18. */
#define SPI_TLE6244X_WR_CONFIG 0x2E00 /* Scrittura Modo di interfaccia comando (uSec-BUS). */
#define SPI_TLE6244X_DEL_DIA 0x3000 /* Reset Diagnosi. */
#define SPI_TLE6244X_PARERR 0x3E00 /* Parit� errata. */
#define SPI_TLE6244X_CMDERR 0x3F00 /* Comando errato. */
#define SPI_TLE6244X_RESERVED 0xFF00 /* RESERVED. */
/*************************************************************/
/*************************************************************/
/* DIAG Table. */
#define SPI_TLE6244X_OK 0x03 /* Uscita con diagnosi di funzionamento corretto. */
#define SPI_TLE6244X_SHORT_CIRCUIT_VDD 0x02 /* Diagnosi di cortocircuito a VBatt. */
#define SPI_TLE6244X_OPEN_LOAD 0x01 /* Diagnosi di circuito aperto. */
#define SPI_TLE6244X_SHORT_CIRCUIT_GND 0x00 /* Diagnosi di cortocircuito a GND. */

#define SPI_TLE6244X_VDD_NOOVERVOLT 0x01 /* Driver in Overvolt. */
#define SPI_TLE6244X_VDD_NOUNDERVOLT 0x02 /* Driver in Undervolt. */
#define SPI_TLE6244X_VDD_ABE_OK 0x04 /* ABE non in reset. */
#define SPI_TLE6244X_VDD_DIAGERR 0x08 /* Riscontrata una diagnosi errata. */
#define SPI_TLE6244X_VDD_NOOVERTEMP 0x10 /* Driver in Sovratemperatura. */
#define SPI_TLE6244X_VDD_OVERVOLT 0x00 /* Uscita con diagnosi di funzionamento corretto. */
/*************************************************************/
/*************************************************************/
/* Control Setup. */
#define SPI_TLE6244X_MUX1DATA_WR 0xFF /* Config uscite SPI/Paral/uSec. */
#define SPI_TLE6244X_MUX2DATA_WR 0xFF /* Config uscite SPI/Paral/uSec. */
#define SPI_TLE6244X_SCON3DATA_WR 0xFF /* Config pin 17 e 18. */
#define SPI_TLE6244X_CONFIG_RD 0x01 /* Lettura Config Data. */
#define SPI_TLE6244X_BMUX_USBUS 0x00 /* Uso uSec-BUS. */
#define SPI_TLE6244X_BMUX_PARALLEL 0x02 /* Uso Parallelo. */
#define SPI_TLE6244X_OSCB 0x00 /* Nessun Switch off SCB. */
#define SPI_TLE6244X_OSCB_SWITCHOFF 0xFC /* Switch off in caso di SCB. */
#define SPI_TLE6244X_SETCFG (SPI_TLE6244X_OSCB_SWITCHOFF | SPI_TLE6244X_BMUX_PARALLEL | SPI_TLE6244X_CONFIG_RD) /* Set Config. */
#define SPI_TLE6244X_VDD_NOLATCH 0x00 /* Out Latch off Vdd. */
#define SPI_TLE6244X_VDD_LATCH 0x20 /* Out Latch Vdd. */
#define SPI_TLE6244X_VDD_UPT 0x00 /* Vdd monitor a Upper threshold. */
#define SPI_TLE6244X_VDD_LWT 0x40 /* Vdd monitor a Lower threshold. */
#define SPI_TLE6244X_VDD_MON 0x00 /* Vdd monitor On. */
#define SPI_TLE6244X_VDD_MOFF 0x80 /* Vdd monitor Off. */
#define SPI_TLE6244X_SETSTAT (SPI_TLE6244X_VDD_MOFF | SPI_TLE6244X_VDD_LWT | SPI_TLE6244X_VDD_NOLATCH) /* Set Stat Config. */

#define SPI_TLE6244X_PIN_LOW 0
#define SPI_TLE6244X_PIN_HIGH 1
#define SPI_TLE6244X_LOAD_ON 1
#define SPI_TLE6244X_LOAD_OFF 0
#define SPI_TLE6244X_LOAD_NOTFBK 2

#define CFGCTRL 0
#define NORMALMODE 1

#define TLE6244X_NOTCFG 0x0
#define TLE6244X_RQCFG 0xC3
#define TLE6244X_LOCKED_TRX 0x3C
#define TLE6244X_ACK_TRX 0xA5
/*************************************************************/
/*************************************************************/
/* Mask Control. */
#define SPI_TLE6244X_MATCH_RX 0x2A00 /* Rx Match. */
#define SPI_TLE6244X_MASK_RX 0x3F00 /* Err Rx Mask. */
#define SPI_TLE6244X_RX_OK 0x2B00 /* Rx Ok, senza errori nel messaggio precedente. */
/*************************************************************/
/*************************************************************/
/* Patter INIT. */
#define SPI_TLE6244X_MESSAGE_01 SPI_TLE6244X_WR_CONFIG | SPI_TLE6244X_SETCFG /* Invio Comando pilotaggio uscite da ingressi INx. */
#define SPI_TLE6244X_MESSAGE_02 SPI_TLE6244X_WR_STATCON | SPI_TLE6244X_SETSTAT /* Invio Comando config diagnosi Vdd. */
#define SPI_TLE6244X_MESSAGE_03 SPI_TLE6244X_WR_SCON3 | SPI_TLE6244X_SCON3DATA_WR /* Stato Uscite 17 e 18. */
#define SPI_TLE6244X_MESSAGE_04 SPI_TLE6244X_WR_MUX1 | SPI_TLE6244X_MUX1DATA_WR /* Uscite pilotate direttamente e non SPI. */
#define SPI_TLE6244X_MESSAGE_05 SPI_TLE6244X_WR_MUX2 | SPI_TLE6244X_MUX2DATA_WR /* Uscite pilotate direttamente e non SPI. */
#define SPI_TLE6244X_MESSAGE_06 SPI_TLE6244X_RD_CONFIG /* Leggo lo stato del Config. */
#define SPI_TLE6244X_MESSAGE_07 SPI_TLE6244X_RD_STATCON /* Leggo lo stato del SCON. */
#define SPI_TLE6244X_MESSAGE_08 SPI_TLE6244X_RD_SCON3 /* Leggo lo stato del out SPI. */
#define SPI_TLE6244X_MESSAGE_09 SPI_TLE6244X_RD_MUX1 /* Leggo lo stato del controllo uscita. */
#define SPI_TLE6244X_MESSAGE_10 SPI_TLE6244X_RD_MUX2 /* Leggo lo stato del controllo uscita. */
#define SPI_TLE6244X_MESSAGE_11 SPI_TLE6244X_DEL_DIA /* Reset Diagnosi. */
#define SPI_TLE6244X_MESSAGE_12 SPI_TLE6244X_RD_IDENT2 /* Leggo la Rel del device. */
#define SPI_TLE6244X_START_PATTERN 12 /* Numero di messaggi da inviare. */
#define SPI_TLE6244X_MAX_PATTERN SPIC_TXRX_BUFFER_SIZE /* Numero massimo di messaggi da inviare !ATTENZIONE: SPI_TLE6244X_MAX_PATTERN < MAX((TX_BUFFER_SIZE/2),(RX_BUFFER_SIZE/2)). */
#define SPI_TLE6244X_VERIFY_ALL_INIT_RD 0x280C /* Controlla se la lettura dell'init � stata eseguita completamente. */
/*************************************************************/
/* Read Flag. */
#define RD_IDENT1_TLE6244X 0x0001
#define RD_IDENT2_TLE6244X 0x0002
#define RD_MUX1_TLE6244X 0x0004
#define RD_MUX2_TLE6244X 0x0008
#define RD_SCON1_TLE6244X 0x0010
#define RD_SCON2_TLE6244X 0x0020
#define RD_SCON3_TLE6244X 0x0040
#define RD_STATCON_TLE6244X 0x0080
#define RD_DIA1_TLE6244X 0x0100
#define RD_DIA2_TLE6244X 0x0200
#define RD_DIA3_TLE6244X 0x0400
#define RD_DIA4_TLE6244X 0x0800
#define RD_DIA5_TLE6244X 0x1000
#define RD_CONFIG_TLE6244X 0x2000
#define RD_INP1_TLE6244X 0x4000
#define RD_INP2_TLE6244X 0x8000
/*************************************************************/
#define TEST_OUT_TLE6244X_MODE

/**************** PINOUT REMAP ******************/
/* Canale e ChipSelect SPI per il TLE6244X. */
#define SPI_TLE6244X_CH SPI_CH_C 
#define SPI_TLE6244X_PCS PCS0 
/**************** PINOUT REMAP ******************/

#define PIN_WITH_REVERSE_LOGIC 7
#define PIN_WITH_ONLY_SPI_17 16
#define PIN_WITH_ONLY_SPI_18 17

#define TLE6244X_CMD_OFF 1
#define TLE6244X_CMD_ON 0

/** PIN per gestire Attuazioni SPI **/
#define USE_TLE_PIN_17
#define USE_TLE_PIN_18
/************************************/

/* Interfaccie esterne. */
int16_T ConfigSPI_TLE6244X (void);
int16_T SendSPI_TLE6244X_FrameBuffer (void);
int16_T SPI_TLE6244X_Init (void);
void ProgramSpi_TLE6244X_T10ms (void);
int16_T Read_SPI_ABETLE6244X(void);
uint8_T GetEv_CmdON_spiTLE6244X (uint8_T numECnt);
void ResetEv_CmdON_spiTLE6244X (uint8_T numECnt);
uint8_T GetEv_CmdOFF_spiTLE6244X (uint8_T numECnt);
void ResetEv_CmdOFF_spiTLE6244X (uint8_T numECnt);
void SetEv_FrontCnt_spiTLE6244X (uint8_T numECnt, uint8_T isOn);
void SetEv_LevelCnt_spiTLE6244X (uint8_T numECnt, uint8_T isOn);

#else

#define FlgFdbDisL3 1

/* Interf. */
int16_T ConfigSPI_TLE6244X (void);
int16_T SendSPI_TLE6244X_FrameBuffer (void);
void ProgramSpi_TLE6244X_T10ms (void);
int16_T Read_SPI_ABETLE6244X(void);
int16_T SPI_TLE6244X_Init (void);
uint8_T GetEv_CmdON_spiTLE6244X (uint8_T numECnt);
void ResetEv_CmdON_spiTLE6244X (uint8_T numECnt);
uint8_T GetEv_CmdOFF_spiTLE6244X (uint8_T numECnt);
void ResetEv_CmdOFF_spiTLE6244X (uint8_T numECnt);
void SetEv_FrontCnt_spiTLE6244X (uint8_T numECnt, uint8_T isOn);
void SetEv_LevelCnt_spiTLE6244X (uint8_T numECnt, uint8_T isOn);

#endif

#endif /* END__SPI_TLE6244X */

