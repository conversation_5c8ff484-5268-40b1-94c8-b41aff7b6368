/*****************************************************************************************************************/
/* $HeadURL:: https://************/svn/Rep_Bo/EM/NISO/MV8/trunk/tree/APPLICATION/ENGFLAG/engflag_calib.c     $   */
/* $ Description:                                                                                                */
/* $Revision:: 16719  $                                                                                          */
/* $Date:: 2025-06-24 11:07:28 +0300 (<PERSON><PERSON>, 24 Jun 2025)   $                                                      */
/* $Author:: yeldano                 $                                                                           */
/*****************************************************************************************************************/

#ifndef __RTWTYPES_H__
#include "typedefs.h"
#endif

#include "engflag.h"

#ifdef _BUILD_ENGFLAG_

#ifdef __MWERKS__ 

#pragma force_active on 

#pragma section RW ".calib" ".calib" 

#else 

#pragma ghs section rodata=".calib" 

#endif 
//Force EndStartFlg
__declspec(section ".calib") int8_T FOENDSTARTFLG = -1;
//Force driving cycle
__declspec(section ".calib") uint8_T FODRVC = 0; 
//Idle Entry Gas Position Threshold [%]
__declspec(section ".calib") uint16_T POSGASINIDLE = 6;   //(  0.3750*16)
//Idle Exit Gas Position Threshold [%]
__declspec(section ".calib") uint16_T POSGASOUTIDLE = 9;   //(  0.5625*16)
//Rpm threshold for engine start flag [rpm]
__declspec(section ".calib") uint16_T RPMENGSTART =    800;   //   800

/* Enable Select Map */
__declspec(section ".calib") uint8_T ENRIDINGMODE[RM_SIZE] = {1, 1, 1, 1, 1};

/* Time to validate button pressure [x*10ms] */
__declspec(section ".calib") uint8_T TIMCHRIDINGMODEFST = 2; // 20 ms
/* Time to validate button pressure [x*10ms] */
__declspec(section ".calib") uint8_T TIMCHRIDINGMODE = 25; // 250 ms

/* Time to confirm new Riding Mode [x*10ms] */
__declspec(section ".calib") uint16_T TIMACKRIDINGMODE = 300; // 3000 ms

/* Speed min to refresh custom map. [km/h] */
__declspec(section ".calib") uint8_T VSPDRIDINGCUSTMIN = 5*16; // 5 Km/h

/* Configuration Map. */
__declspec(section ".calib") uint8_T  VTACCSENS[RM_SIZE-1] = {0, 0, 0, 0};
__declspec(section ".calib") uint8_T  VTMAXTRQ[RM_SIZE-1] = {0, 0, 0, 0};
__declspec(section ".calib") uint8_T  VTENGRESP[RM_SIZE-1] = {0, 0, 0, 0};
__declspec(section ".calib") uint8_T  VTRPMLIM[RM_SIZE-1] = {0, 0, 0, 0};

/* WarmUp */
__declspec(section ".calib") int16_T  DELTTWATERWUC = 22*16; // 22 �C
__declspec(section ".calib") int16_T  TWATERWUC = 70*16; // 70 �C

/* Econ Mode */
__declspec(section ".calib") uint8_T  ENECONTNKEMPTY = 0;
__declspec(section ".calib") uint16_T THRFRONTVEHSPEEDECONMODE = (0 * 16);
__declspec(section ".calib") uint16_T THRVSPEEDECONMODEON = (110 * 16);
__declspec(section ".calib") uint16_T THRVSPEEDECONMODEOFF = (100 * 16);
__declspec(section ".calib") uint8_T  THRGEARECONMODE = 5;

#ifdef __MWERKS__ 
#pragma force_active off 
#endif 

#endif // _BUILD_ENGFLAG_
