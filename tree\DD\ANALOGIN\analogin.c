/*****************************************************************************************************************/
/* $HeadURL:: https://************/svn/Rep_Bo/EM/NISO/MV8/trunk/tree/DD/ANALOGIN/analogin.c                $   */
/* $ Description:                                                                                                */
/* $Revision:: 16424  $                                                                                          */
/* $Date:: 2024-07-25 18:29:45 +0300 (Thu, 25 Jul 2024)   $                                                      */
/* $Author:: LanaL                   $                                                                       */
/*****************************************************************************************************************/

#include "typedefs.h"
#ifdef  MATLAB_MEX_FILE
#define _BUILD_ANALOGIN_
#endif
#include "ETPU_EngineDefs.h"
#include "ETPU_VrsDefs.h"
#include "analogin.h"
#include "adc.h"
#include "sys.h"
#include "timing.h"
#include "mathlib.h"
#include "diagmgm_out.h"
#include "digio.h"
#include "sync.h"
#include "syncmgm.h"
#include "diagcanmgm.h"
#include "utils.h"
#include "canmgm.h"
#include "AirCorr_out.h"

/* DA UTILIZZARE SOLO PER DEBUG */
#undef _TEST_EXANGLE_ // IN_IgnCmd_8 oppure OUT_D2

#define BOARD_TEMP_BUFF         16
#define T_SENSE_T_HIGH          TPS.TCCR0.B.THIGH
#define T_SENSE_T_LOW           TPS.TCCR0.B.TLOW
#define T_SENSE_CODE_THIGH      TPS.TCCR0.B.TSCV2
#define T_SENSE_CODE_TLOW       TPS.TCCR0.B.TSCV1
#define V_BG_CODE_TLOW          TPS.TCCR1.B.TSCV3

uint8_T StDiagGearPosIn = NO_PT_FAULT;

#ifdef _BUILD_ANALOGIN_

//UNSIGNED OUTPUTS
uint16_T FuelTankLevel;
uint16_T HfmSignal;
uint16_T MapRatio;
uint16_T MapSignal;
uint16_T MapSignalPAtm;
uint16_T PresInj;
uint16_T QAirCylHfm;
uint16_T VBattery;
uint8_T  KeySignal2;
uint8_T  MapPhaseFlg;
uint8_T  MapPhaseValid;
uint16_T VBattery1;
uint16_T VBattery2;
uint16_T VSens1;
uint16_T VSens2;
uint16_T VAnalogDx1;
uint16_T VAnalogDx2;
uint16_T VAnalogDx3;
uint16_T VAnalogSx1;
uint16_T VAnalogSx2;
uint16_T VAnalogSx3;
uint16_T VAnalogSx4;
uint16_T VAnalogSx5;
uint16_T VAnalogSx6;
uint16_T VRefH;
uint16_T VRefL;
uint16_T VRefHalf;
uint16_T VRef75;
uint16_T VRef25;
uint8_T  NFiltMap;
uint16_T MapSignalSOI;
uint16_T MapSignalEOI;
uint16_T VCmdStarter;
uint16_T VDiagStarter;
int16_T  TBoardTemperature;


//TUNING PARAMETERS
extern uint8_T  USEA2RSIGNAL;
extern uint16_T VBATGAIN;
extern uint16_T VBATMIN;
extern uint16_T VBATMAX;
extern uint16_T VBATOFFSET;
extern int16_T  FUELTLGAIN;
extern int16_T  FUELTLOFFSET;
extern uint32_T VLAMREADY;
extern uint16_T MAPGAIN;
extern int16_T  MAPOFFSET;
extern uint16_T MAPMIN;
extern uint16_T MAPMAX;
extern uint16_T VMAPMIN;
extern uint16_T VMAPMAX;
extern uint16_T VEXHVMIN;
extern uint16_T VEXHVMAX;
extern uint16_T VEXHOVERLOW;
extern uint16_T VEXHOVERHIGH;
extern uint16_T BKHFMVOLT[VTHFMKGH_dim];
extern uint16_T VTHFMKGH[VTHFMKGH_dim];
extern uint16_T BKVAFOBJ[VTAFOBJ_dim];
extern uint16_T VTAFOBJ[VTAFOBJ_dim];
extern uint16_T BKTECU[VTTECU_dim];
extern int16_T  VTTECU[VTTECU_dim];
extern uint16_T VBATINMIN;
extern uint16_T VBATINMAX;
extern uint16_T THRKEYON;
extern uint16_T KFILTVBAT;
extern uint16_T VSENSMIN;
extern uint16_T VSENSMAX;
extern uint16_T BKRPMFMAP[BKRPMFMAP_dim];
extern uint8_T  VTNFILTMAP[BKRPMFMAP_dim];
extern uint16_T KFILTVLAMOBJ;
extern uint16_T MAPPHASEOFFSET;
extern uint16_T VTMAPACQANGLE[];
extern uint16_T BKRPMMAPACQ[];
extern uint16_T MAPACQPATMANGLE;
extern uint16_T THMAPRIPPLE;
extern uint16_T MAPEOIWEIGHT;
extern int16_T  DELTAMAPPHASE;
extern uint16_T THRPMDIAGBAT;
extern uint8_T  IDXCYLPRESINJ;
extern uint16_T VGEARPOSMIN;
extern uint16_T VGEARPOSMAX;
extern uint16_T VGEAROVERLOW;
extern uint16_T VGEAROVERHIGH;
extern int16_T  KFILTVGEARPOS;
extern uint16_T TGEARSHIFT;
extern uint16_T THTDCDIAGVREG;
extern uint16_T THMAPHYSTPATM;
extern int16_T  MINPROGLATENCY;
extern int16_T  MINANGPROGDIFF;
extern uint16_T THRPMGEARDIAG;
extern int16_T  BKANGWEIGHT[];
extern uint8_T  VTMAPWEIGHT[];
extern uint16_T MAPNORMGAIN;
extern uint8_T  USEGASIDLESW;
extern int16_T  MAPACQAFTERTOOTH;
extern uint16_T VTANGLEMAPACQTOOTH[];

//LOCAL VARAIBLES
uint16_T VAngThrottle1;
uint16_T VAngThrottle2;
uint16_T VAngThrottle3;
uint16_T VAngThrottle4;
uint16_T VAngExhVal;    /* angle feedback of exhaust gas valve */
uint16_T VGasPos1;
uint16_T VGasPos2;
uint16_T VGasPos3;
uint16_T VGasPos4;
uint16_T VGasIDLSw;
uint16_T VTAir;
uint16_T VTWater;
uint16_T VTWater2;
uint16_T VKam;
uint16_T VPresAtm;
uint16_T VBatteryIn;
uint16_T VBatteryIn2;
uint16_T VKeySignal;
uint16_T VFuelTankLevel;
uint16_T VMapSignal;
uint16_T VHfmSignal;
uint16_T VTEcu1;
uint16_T VTEcu2;
uint16_T MapSignal0;
uint16_T MapSignal1;
uint16_T MapSignalAngle;
uint16_T HfmSignal0;
uint16_T MapBuffer[MAP_BUFFER];
uint16_T HfmBuffer[HFM_BUFFER];
uint8_T  MapIndex;
uint8_T  HfmIndex;
int32_T  mapfilter;
int32_T  HfmSignal_hr;
int16_T  TEcu1;
int16_T  TEcu2;
uint16_T VEcuGnd;
uint16_T AFObjIn;
uint16_T VAFObj;
int32_T  VAFObj_hr;
int32_T  VBattery_HR;
int16_T  GainCC;                            // 2^-14
int16_T  OffsetCC;                        // 2^-14
uint8_T  FlgDiagLambda;
uint16_T VLamP;
uint16_T VLamM;
uint16_T VLam2P;
uint16_T VLam2M;
uint16_T MapSignalMin;
uint16_T MapSignalMax;
uint16_T MapSignalRipple;
uint16_T VGearPos;
uint16_T VGearPos0;
uint16_T VGearShift;
uint16_T VLB3Raw;
uint16_T VLC4Raw;

uint16_T OldMAPGAIN;
int16_T  OldMAPOFFSET;
uint16_T OldMAPMIN;
uint16_T OldMAPMAX;

extern  uint8_T   stDISABLEINT;
extern  uint32_T  EE_BiosInit;
extern  uint32_T  EE_BiosErr;
extern  uint8_t   FlgMAPSOADone;
extern  uint8_T   CntTaskEOAMap;
extern  uint32_T  TaskTimerEOAMapms;

#if (ENGINE_TYPE == MV_AGUSTA_4C_TDC_0_9)
const uint32_T ANGLE_MAP_ACQ_INIT[MAX_TRIGGER_MAP_ACQ_INIT] = {7*TOOTH_DEG_SCALING, 16*TOOTH_DEG_SCALING};
#elif (ENGINE_TYPE == MV_AGUSTA_4C)
const uint32_T ANGLE_MAP_ACQ_INIT[MAX_TRIGGER_MAP_ACQ_INIT] = {5*TOOTH_DEG_SCALING, 13*TOOTH_DEG_SCALING};
#elif (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_20)
const uint32_T ANGLE_MAP_ACQ_INIT[MAX_TRIGGER_MAP_ACQ_INIT] = {1*TOOTH_DEG_SCALING, 17*TOOTH_DEG_SCALING};
#elif (ENGINE_TYPE == MV_AGUSTA_3C_TDC_0_30)
const uint32_T ANGLE_MAP_ACQ_INIT[MAX_TRIGGER_MAP_ACQ_INIT] = {3*TOOTH_DEG_SCALING, 25*TOOTH_DEG_SCALING};
#endif

uint32_T Angle4MapSignal[MAX_TRIGGER_NUM];
#ifdef DEBUG_ANGLE_CALC
uint32_T Angle4MapSignalDebug1[MAX_TRIGGER_NUM];
uint32_T Angle4MapSignalDebug2[MAX_TRIGGER_NUM];
#endif
uint16_T LastMapAcqAngle;
uint32_T Time4MapSignal = 500;
uint16_T VMapSignalBuff[MAX_TRIGGER_NUM];
uint16_T MapAcqEOIAng;
uint16_T MapAcqSOIAng;
uint8_T  IdnMapPAtm = 0;
uint8_T  IdnMapSOI = 1;
uint8_T  IdnMapEOI = 2;
uint8_T  IdnMapAir = 3;
uint8_T  CntEOAMap;
uint16_T MapAcqAngle;

uint8_T  debugADC_Error;
uint8_T  debugADC_ErrorCnt;
int16_T  debugADC_ErrorType;
uint8_T  debugADC_Error_2;
uint8_T  debugADC_ErrorCnt_2;
int16_T  debugADC_ErrorType_2;
uint8_T  FlgMapAcqStarted;
uint8_T  CntTryPhase;
uint8_T  FlgEngCycleCompleted;
uint8_T  StMapAcq;
uint16_T debugADC_CrankAngle0;
uint16_T debugADC_CrankAngle1;
uint16_T debugADC_CrankAngle2;
uint16_T debugADC_CrankAngle3;
uint16_T debugADC_CrankAngle4;
uint16_T debugADC_CrankAngle5;
uint8_T  NumOfTrig;
uint8_T  ForceInitMapAcqAngle;
uint8_T  FlgVMapCalc;

uint16_T IndexMapEOI;
uint16_T IndexMapPAtm;
uint16_T IndexMapSOI;
uint16_T RatioMapEOI;
uint16_T RatioMapPAtm;
uint16_T RatioMapSOI;

// Internal Functions
void AnalogIn_AngThrottle(void);
void AnalogIn_GasPos(void);
void AnalogIn_TAir(void);
void AnalogIn_TAir_Init(void);
void AnalogIn_TWater(void);
void AnalogIn_TWater_2(void);
void AnalogIn_TWater_Init(void);
void AnalogIn_TWater_2_Init(void);
void AnalogIn_VKam(void);
uint8_T GetGearPos(uint16_T vgpos);

//void AnalogIn_TWater_NoSync(void);
void MapSignal_PowerOn_5ms(void);
void MapSignal_TDC(void);
void AnalogIn_PresAtm(void);
void AnalogIn_VBattery(void);
void AnalogIn_VBattery_Init(void);
void AnalogIn_VKeySignal(void);
void AnalogIn_VFuelTankLevel(void);
void HfmSignal_PowerOn_5ms(void);
void HfmSignal_Angle(void);
void AnalogIn_VLambda_Init(void);
void AnalogIn_VLambda_T5ms(void);
void AnalogIn_VLambdaObj_TDC_HTDC(void);
void AnalogIn_VLambdaObj_PowerOn_T5ms(void);
void AnalogIn_EcuTemp(void);
void GetMapSignal(uint16_T * MapSignal0, uint16_T VMapSignal);
void AnalogIn_VSens(void);
void AnalogIn_ADC_Calibration(void);
void AnalogIn_MapSignal_SOI(void);
void AnalogIn_MapSignal_EOI(void);
void RpmSens_Diagnosis(void);
void AnalogIn_VGearPos(void);
void AnalogIn_VCmdStarter(void);
void AnalogIn_VDiagStarter(void);
void AnalogIn_MapAngAcq_SOA(void);
void AnalogIn_Map_StartOfAcq(void);
void AnalogIn_Set_FlgMapAcqStarted(uint8_T value);
void FuncMapAngAcqEndOfAcq(void);

//Funzioni chiamate dai Task
void AnalogIn_Init(void)
{
    int16_T errtmp = 0;
    uint8_T i;

    //Inizializzo i PWM usati come trigger per i canali AD
#ifdef EN_TRIGGER_450_US
    SYS_ADC_SourceConfig(_450_MICROSEC  , TIME_BASE);
    SYS_ADC_SetPeriod(_450_MICROSEC,450);
#endif
#ifdef EN_TRIGGER_2_MS
    SYS_ADC_SourceConfig(_2_MILLISEC    , TIME_BASE);
    SYS_ADC_SetPeriod(_2_MILLISEC,2000);
#endif
#ifdef EN_TRIGGER_10_MS
    SYS_ADC_SourceConfig(_10_MILLISEC   , TIME_BASE);
    SYS_ADC_SetPeriod(_10_MILLISEC,10000);
#endif

    //Configurazione dei canali ADC utilizzati
    for (i=0; i < N_ANCH; i++)
    {
        if (USEGASIDLESW != 0)
        {
            ADC_Init(ANTAB_CFG0[i].anch, NULL, NULL, 0, ANTAB_CFG0[i].samprate);
        }
        else
        {
            ADC_Init(ANTAB_CFG1[i].anch, NULL, NULL, 0, ANTAB_CFG1[i].samprate);
        }
    }
    /* Enable dei canali ADC utilizzati */
    /* Abilito i PWM usati come trigger per i canali AD */
#ifdef EN_TRIGGER_2_MS
    errtmp = ADC_Enable(_2_MILLISEC);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
    }
    else /* no error */
    {
        SYS_ADC_SetStatus(_2_MILLISEC, TRIGGER_ON);
    }
#endif
#ifdef EN_TRIGGER_10_MS
    errtmp = ADC_Enable(_10_MILLISEC);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
    }
    else /* no error */
    {
        SYS_ADC_SetStatus(_10_MILLISEC, TRIGGER_ON);
    }
#endif
#ifdef EN_TRIGGER_450_US
    errtmp = ADC_Enable(_450_MICROSEC);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
    }
    else /* no error */
    {
        SYS_ADC_SetStatus(_450_MICROSEC, TRIGGER_ON);
    }
#endif
#ifdef EN_SOFTWARE_TRIGGERED
    errtmp = ADC_Enable(SOFTWARE_TRIGGERED);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
    }
#endif

#ifdef _TEST_EXANGLE_
//    DIGIO_OutCfg(_TEST_EXANGLE_, 0);
    DIGIO_OutCfgExt(_TEST_EXANGLE_, 0, OUT_PUSHPULL, DRVSTR_10PF, MIN_SLEWRATE);
#endif

#ifdef MAP_ANGLE_BASED
    FlgVMapCalc = 0;
    AnalogIn_Set_FlgMapAcqStarted(0);
    NumOfTrig = MAX_TRIGGER_MAP_ACQ_INIT;
    SYS_ADC_SourceConfig(ANGULAR_SAMPLING, TIME_BASE);
    AnalogIn_MapAngAcq_SOA();
#endif

    AnalogIn_ADC_Calibration();
    AnalogIn_AngThrottle();
    AnalogIn_GasPos();
    AnalogIn_TAir_Init();
    AnalogIn_TWater_Init();
    AnalogIn_TWater_2_Init();
    AnalogIn_PresAtm();

    OldMAPGAIN = MAPGAIN ;
    OldMAPOFFSET = MAPOFFSET ;
    OldMAPMIN = MAPMIN ;
    OldMAPMAX = MAPMAX ;
    MapSignal_PowerOn_5ms();
    /* INIZIALIZZO I VALORI MIN E MAX PER IL CALCOLO DEL RIPPLE NELLA DIAGNOSI GIRI */
    MapSignalMin = MapSignalAngle;
    MapSignalMax = MapSignalAngle;
    MapSignalRipple = 0;

    HfmSignal_PowerOn_5ms();
    AnalogIn_VBattery_Init();
    AnalogIn_VKeySignal();
    AnalogIn_VFuelTankLevel();
    AnalogIn_VLambda_Init();
    AnalogIn_VLambdaObj_PowerOn_T5ms();
    AnalogIn_EcuTemp();
    AnalogIn_VSens();
    AnalogIn_VGearPos();
    AnalogIn_VCmdStarter();
    AnalogIn_VDiagStarter();
}

/* FUNZIONI PER L'ACQUISIZIONE DELLA PRESSIONE SU BASE ANGOLARE */
#ifdef MAP_ANGLE_BASED
void AnalogIn_ValidateAngAcq(int16_T *angle_sup, uint8_T *flgangmod, int16_T *angle_inf, int16_T mindiff)
{
    int16_T diff;
    int16_T maxdiff;
    
    maxdiff = CYCLE_ANGLE - mindiff;
    diff = *angle_sup - *angle_inf;

    if((diff < mindiff) || (diff > maxdiff))
    {
        /* RICALCOLO DELL'ANGOLO DI TRIGGER */
        *angle_sup = *angle_inf + mindiff;
        if (*angle_sup >= CYCLE_ANGLE)
        {
            *angle_sup = *angle_sup - CYCLE_ANGLE;
            *flgangmod = 2;
        }
        else
        {
            *flgangmod = 1;
        }
    }
    else
    {
        *flgangmod = 0;
    }
}

void AnalogIn_MapAngAcq_SOA(void)
{
    int16_T errtmp;
    int16_T diff;
    uint16_T tmp_angle;

    /* INTERROMPO L'ACQUISIZIONE IN WAIT_SYNCH */
    if((((StSync != SYNCH) && (ENGAPPHASE != 0u)) || ((StSync == NO_SYNCH) && (ENGAPPHASE == 0u))) && (FlgMapAcqStarted == 0))
    {
        NumOfTrig = 2;
        Time4MapSignal = 500;
        
        /* IN CASO DI SISTEMA NON SINCRONIZZATO RESET DEGLI ANGOLI */
        Angle4MapSignal[0] = ANGLE_MAP_ACQ_INIT[0];
        Angle4MapSignal[1] = ANGLE_MAP_ACQ_INIT[1];
        MapAcqEOIAng = 0;
        MapAcqSOIAng = 0;
        
        StMapAcq = MAPACQ_TIME_CONFIG;
    }
    //mi:
    else if (ForceInitMapAcqAngle == 1)
    {
        if(FlgSyncPhased == 0)
        {
            NumOfTrig = 2;
            Time4MapSignal = 0;
            
            /* IN CASO DI SISTEMA NON SINCRONIZZATO RESET DEGLI ANGOLI */
            Angle4MapSignal[0] = ANGLE_MAP_ACQ_INIT[0];
            Angle4MapSignal[1] = ANGLE_MAP_ACQ_INIT[1];
        }
        else if (NumOfTrig != MAX_TRIGGER_NUM)
        {
            NumOfTrig = 2;
            Time4MapSignal = 0;
            
            /* IN CASO DI SISTEMA SINCRONIZZATO */
            Angle4MapSignal[0] = MapAcqAngle;
            tmp_angle = MapAcqAngle + VTANGLEMAPACQTOOTH[1];
            if(tmp_angle >= CYCLE_ANGLE)
            {
                Angle4MapSignal[1] = tmp_angle - CYCLE_ANGLE;
            }
            else
            {
                Angle4MapSignal[1] = tmp_angle;
            }
        }
        else
        {
            /* Angle4MapSignal already config */
            NumOfTrig = 2;
            Time4MapSignal = 0;
        }
        /* IN CASO DI RIAVVIO AD ANGOLO */
        StMapAcq = MAPACQ_ANG_CONFIG;
         
        debugADC_Error = 15;
        ForceInitMapAcqAngle = 0;
    }
    else if((StSync == SYNCH) && (FlgMapAcqStarted == 0))
    {
        Time4MapSignal = 0;
        StMapAcq = MAPACQ_ANG_CONFIG;
    }
    else if(FlgMapAcqStarted != 0)
    {
        Time4MapSignal = 0;
        StMapAcq = MAPACQ_ANG_CONFIG;
    }
    else
    {
        StMapAcq = MAPACQ_NO_CONFIG;
    }

    if ((StMapAcq != MAPACQ_NO_CONFIG) && (FlgMapAcqStarted == 0))
    {
        AnalogIn_Map_StartOfAcq();
    }
}

void AnalogIn_MapAngAcq_EOA(void)
{
    uint16_T    tmpVMapSignal;
    int16_T     tmpDeltaMap;
    uint16_T    maxmap2phase;
    int32_T  tmp_angle;
    uint8_T i;
    
    /* MEMORIZZAZIONE ULTIMO ANGOLO ACQUISITO */
    LastMapAcqAngle = (uint16_T)Angle4MapSignal[NumOfTrig-1];
    
    tmpVMapSignal = (VMapSignalBuff[1] >> 4);
    GetMapSignal(&MapSignalAngle, tmpVMapSignal);
    
    tmpVMapSignal = (VMapSignalBuff[0] >> 4);
    GetMapSignal(&MapSignal1, tmpVMapSignal);
    
    /* CALCOLO DI MapPhaseValid e di MapPhaseFlg solo nel caso 
       in cui i valori di pressione siano acquisiti ad angolo*/
    if(StMapAcq == MAPACQ_ANG_CONFIG)
    {
        if ((MapPhaseValid==0) && (!FlgEOL))
        {
            /* Nel caso in cui la fase non sia ancora valida la calcolo */
            if(DELTAMAPPHASE==0)
            {
                /* Nel caso in cui la calibrazione sia 0 il sistema si fasa sempre */
                MapPhaseValid = 1;
            }
            else
            {
                maxmap2phase  = PresAtm + THMAPHYSTPATM;
                if ((MapSignalAngle<=maxmap2phase) && (MapSignal1<=maxmap2phase))
                {
                    /* Calcolo vero e proprio di MapPhaseValid */
                    tmpDeltaMap   = (int16_T)MapSignalAngle - (int16_T)MapSignal1;
                    tmpDeltaMap   = (int16_T)(GenAbs((int32_T)tmpDeltaMap,INT16_TYPE));
                    MapPhaseValid = (tmpDeltaMap > DELTAMAPPHASE);
                }
            }
        }
        else
        {
            /* Ho gi� dichiarato valida la fase, per cui non devo fare altri calcoli */
            MapPhaseValid = 1;
        }

        if(MapPhaseValid == 0)
        {
            /* Toggle per inibire calcolo MapSignalAngle */
            FlgVMapCalc = 0;
            
            /* INCREMENTO CONTATORE TENTATIVI FASATURA */
            CntTryPhase++;
            NumOfTrig = 2;

            /* MODIFICARE ANGOLI PER ACQUISIZIONE PRESSIONE */
            if (TSTBIT(CntTryPhase,0))
            {
                Angle4MapSignal[0] = ANGLE_MAP_ACQ_INIT[0] + 360*DEGREE_PRECISION;
                Angle4MapSignal[1] = ANGLE_MAP_ACQ_INIT[1] + 360*DEGREE_PRECISION;
            }
            else
            {
                Angle4MapSignal[0] = ANGLE_MAP_ACQ_INIT[0];
                Angle4MapSignal[1] = ANGLE_MAP_ACQ_INIT[1];
                /* Flag per indicare che un ciclo motore � stato completato senza riconoscere la fase */
                FlgEngCycleCompleted = 1;
            }
        }
        else if (NumOfTrig != MAX_TRIGGER_NUM)
        {
            /* Toggle per inibire calcolo MapSignalAngle */
            FlgVMapCalc = 0;
            
            /* Riprogrammazione angoli e diagnosi */
            NumOfTrig = MAX_TRIGGER_NUM;
            
            /* Calcolo angoli per acquisizione */
            for(i=0; i<MAX_TRIGGER_NUM; i++)
            {
                tmp_angle = TDC_ANGLE_MAP + VTANGLEMAPACQTOOTH[i];
                tmp_angle = tmp_angle + MAPACQAFTERTOOTH;
                if (tmp_angle >= CYCLE_ANGLE)
                {
                    Angle4MapSignal[i] = tmp_angle - CYCLE_ANGLE;
                }
                else if (tmp_angle < 0)
                {
                    Angle4MapSignal[i] = tmp_angle + CYCLE_ANGLE;
                }
                else
                {
                    Angle4MapSignal[i] = tmp_angle;
                }
            }
        }
        else
        {
            /* Angle4MapSignal already config */
            /* Toggle per inibire calcolo MapSignalAngle */
            FlgVMapCalc = 1;
        }
        /* CALCOLO MapPhaseFlg*/
        MapPhaseFlg = ((MapSignalAngle-MAPPHASEOFFSET)>MapSignal1);
    }
    else
    {
        /* MISRA */
    }
}

void AnalogIn_VMapSignal_Calc(void)
{
    uint8_T  i;
    uint32_T tmpPresInj;
    uint16_T tmpMAPSOIWEIGHT;
    uint16_T idxSOI, ratSOI, idxEOI, ratEOI, idxOFF, ratOFF;
    uint16_T tmp_vmap, tmp_angle_u16;
    int16_T  tmp_angle_s16;
    uint8_T  PtFaultVMapSignal = NO_PT_FAULT;
    uint8_T  StDiagVMapSignal = NO_FAULT;

    /* Calcolo Index/Ratio associato all'angolo del TDC */
    tmp_angle_u16 = (CYCLE_ANGLE + TDC_ANGLE_MAP);
    tmp_angle_u16 = tmp_angle_u16 + MAPACQAFTERTOOTH;
    if(tmp_angle_u16 >= CYCLE_ANGLE)
    {
        tmp_angle_u16 = tmp_angle_u16 - CYCLE_ANGLE;
    }
    PreLookUpIdSearch_U16(&idxOFF, &ratOFF, tmp_angle_u16, &(VTANGLEMAPACQTOOTH[0]), (MAX_TRIGGER_NUM-1));
    
    /* Calcolo MapSignalAngle */
    LookUp_U16_U16(&(MapAcqAngle), &VTMAPACQANGLE[0], Rpm, &BKRPMMAPACQ[0], (BKRPMMAPACQ_dim-1));
    
    tmpPresInj = 0;
    for (i = 0; i < BKANGWEIGHT_dim; i++)
    {
        tmp_angle_s16 = BKANGWEIGHT[i] + MapAcqAngle;
        if (tmp_angle_s16 < 0)
        {
            tmp_angle_u16 = tmp_angle_s16 + CYCLE_ANGLE;
        }
        else if (tmp_angle_s16 >= CYCLE_ANGLE)
        {
            tmp_angle_u16 = tmp_angle_s16 - CYCLE_ANGLE;
        }
        else
        {
             tmp_angle_u16 = tmp_angle_s16;
        }
        
        PreLookUpIdSearch_U16(&idxSOI, &ratSOI, tmp_angle_u16, &(VTANGLEMAPACQTOOTH[0]), (MAX_TRIGGER_NUM-1));
        if (idxSOI >= idxOFF)
        {
            idxSOI = idxSOI - idxOFF;
        }
        else
        {
            idxSOI = MAX_TRIGGER_NUM + idxSOI - idxOFF;
        }
        /* In caso di fine buffer interpolare con campione 0 */
        if(idxSOI == (MAX_TRIGGER_NUM-1))
        {
            INTERP_IR_U16(&tmp_vmap, VMapSignalBuff[idxSOI], VMapSignalBuff[0], ratSOI);
        }
        else
        {
            LookUp_IR_U16(&tmp_vmap, &VMapSignalBuff[0], idxSOI, ratSOI, (MAX_TRIGGER_NUM-1));
        }
        tmp_vmap = tmp_vmap >> 4;
        tmpPresInj = tmpPresInj + tmp_vmap*VTMAPWEIGHT[i];
    }
    
    tmpPresInj = tmpPresInj >> 7;
    tmpPresInj = (tmpPresInj * MAPNORMGAIN) >> 16;
    
    /* Calcolo VMapSignal per diagnosi */
    if ((StMapAcq == MAPACQ_ANG_CONFIG) && (NumOfTrig == MAX_TRIGGER_NUM) && (FlgVMapCalc != 0))
    {
        if(tmpPresInj > VMAPMAX)
        {
            VMapSignal = VMAPMAX;
        }
        else
        {
            VMapSignal = (uint16_T)tmpPresInj;
        }
        
        GetMapSignal(&MapSignalAngle, VMapSignal);
    }
    else
    {
        VMapSignal = (VMapSignalBuff[0] >> 4);
    }
    
    if (TSTBIT(EE_BiosErr,ADC_IDX) == 0)
    {
        /* check for punctual fault */
        DiagMgm_RangeCheck_U16(&PtFaultVMapSignal, VMapSignal, VMAPMIN, VMAPMAX, CC_TO_GND, CC_TO_VCC);
        DiagMgm_SetDiagState(DIAG_V_MAPSIGNAL, PtFaultVMapSignal, &StDiagVMapSignal);
    }
    
    if ((StSync == SYNCH) && (MapPhaseValid == 1))
    {
        /* Calcolo MapSignalPAtm */
        PreLookUpIdSearch_U16(&idxSOI, &RatioMapPAtm, MAPACQPATMANGLE, &(VTANGLEMAPACQTOOTH[0]), (MAX_TRIGGER_NUM-1));
        if (idxSOI >= idxOFF)
        {
            IndexMapPAtm = idxSOI - idxOFF;
        }
        else
        {
            IndexMapPAtm = MAX_TRIGGER_NUM + idxSOI - idxOFF;
        }
        /* In caso di fine buffer interpolare con campione 0 */
        if(IndexMapPAtm == (MAX_TRIGGER_NUM-1))
        {
            INTERP_IR_U16(&tmp_vmap, VMapSignalBuff[IndexMapPAtm], VMapSignalBuff[0], RatioMapPAtm);
        }
        else
        {
            LookUp_IR_U16(&tmp_vmap, &VMapSignalBuff[0], IndexMapPAtm, RatioMapPAtm, (MAX_TRIGGER_NUM-1));
        }
        tmp_vmap = tmp_vmap>>4;
        GetMapSignal(&MapSignalPAtm, tmp_vmap);
        
        /* Calcolo MapSignalSOI */
        PreLookUpIdSearch_U16(&idxSOI, &RatioMapSOI, MapAcqSOIAng, &(VTANGLEMAPACQTOOTH[0]), (MAX_TRIGGER_NUM-1));
        if (idxSOI >= idxOFF)
        {
            IndexMapSOI = idxSOI - idxOFF;
        }
        else
        {
            IndexMapSOI = MAX_TRIGGER_NUM + idxSOI - idxOFF;
        }
        /* In caso di fine buffer interpolare con campione 0 */
        if(IndexMapSOI == (MAX_TRIGGER_NUM-1))
        {
            INTERP_IR_U16(&tmp_vmap, VMapSignalBuff[IndexMapSOI], VMapSignalBuff[0], RatioMapSOI);
        }
        else
        {
            LookUp_IR_U16(&tmp_vmap, &VMapSignalBuff[0], IndexMapSOI, RatioMapSOI, (MAX_TRIGGER_NUM-1));
        }
        tmp_vmap = tmp_vmap>>4;
        GetMapSignal(&MapSignalSOI, tmp_vmap);
        
        /* Calcolo MapSignalEOI */
        PreLookUpIdSearch_U16(&idxEOI, &RatioMapEOI, MapAcqEOIAng, &(VTANGLEMAPACQTOOTH[0]), (MAX_TRIGGER_NUM-1));
        if (idxEOI >= idxOFF)
        {
            IndexMapEOI = idxEOI - idxOFF;
        }
        else
        {
            IndexMapEOI = MAX_TRIGGER_NUM + idxEOI - idxOFF;
        }
        /* In caso di fine buffer interpolare con campione 0 */
        if(IndexMapEOI == (MAX_TRIGGER_NUM-1))
        {
            INTERP_IR_U16(&tmp_vmap, VMapSignalBuff[IndexMapEOI], VMapSignalBuff[0], RatioMapEOI);
        }
        else
        {
            LookUp_IR_U16(&tmp_vmap, &VMapSignalBuff[0], IndexMapEOI, RatioMapEOI, (MAX_TRIGGER_NUM-1));
        }
        tmp_vmap = tmp_vmap>>4;
        GetMapSignal(&MapSignalEOI, tmp_vmap);
        
        /* Calcolo PresInj */
        tmpMAPSOIWEIGHT = 1024 - MAPEOIWEIGHT;
        if(tmpMAPSOIWEIGHT < 0)
        {
            tmpMAPSOIWEIGHT = 0;
        }
        
        tmpPresInj =  (uint32_T)(((uint16_T)tmpMAPSOIWEIGHT) * MapSignalSOI);  /* 2^-10 */
        tmpPresInj += (uint32_T)(MAPEOIWEIGHT * MapSignalEOI);  /* 2^-10 */
        
        PresInj =  (uint16_T)(tmpPresInj>>10);  /* 2^0 */
        
            /* INSERIRE DIAGNOSI ELETTRICA SENSORE */
    }
    else
    {
        MapSignalSOI = MapSignalAngle;
        MapSignalEOI = MapSignalAngle;
        MapSignalPAtm = MapSignalAngle;
    }
}

void FuncMapAngAcq_EndOfAcq(void)
{
    FuncMapAngAcqEndOfAcq();
    TerminateTask();
}

void FuncMapAngAcqEndOfAcq(void)
{
#ifdef _TEST_TIMING_
    uint64_T starttimer_old;
    uint64_T starttimer;
    
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old);
    }
    else { /* MISRA */ }
#endif

    CntTaskEOAMap++;

    CntEOAMap++;
    
    //SYNC_GetCrankAngle(&debugADC_CrankAngle1);
    AnalogIn_MapAngAcq_EOA();
    
    //SYNC_GetCrankAngle(&debugADC_CrankAngle2);
    if (stDISABLEINT == 0)
    {
        AnalogIn_MapAngAcq_SOA();
    }
    
    //SYNC_GetCrankAngle(&debugADC_CrankAngle3);
    AnalogIn_VMapSignal_Calc();
    
    //SYNC_GetCrankAngle(&debugADC_CrankAngle4);

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer);
        starttimer = (starttimer - starttimer_old);
        TIMING_TicksToMicroSeconds(starttimer, &starttimer);
        if ((uint32_T)(starttimer) > TaskTimerEOAMapms)
        {
            TaskTimerEOAMapms = (uint32_T)(starttimer);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void AnalogIn_Sync(void)
{
    /* DA RIEMPIRE QUANDO CI SARA' DISTINZIONE TRA TASK SYNC E PHASED */
}

void AnalogIn_NoSync(void)
{
    FlgVMapCalc = 0;
    SYS_ADC_SetStatus(ANGULAR_SAMPLING, TRIGGER_OFF);
    AnalogIn_Set_FlgMapAcqStarted(0);
    AnalogIn_MapAngAcq_SOA();
}

void AnalogIn_Map_StartOfAcq(void)
{
    int16_T errtmp;
    
    errtmp = SYS_ADC_SetAngTrigger(Angle4MapSignal, Time4MapSignal, NumOfTrig);
    
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error = 1;
        debugADC_ErrorCnt++;
        debugADC_ErrorType = errtmp;
    }
    else /* no error */
    {
        //Inizializzo ed abilito canale AD
        errtmp = ADC_Init(IDN_MAP_SIGNAL_ANG, VMapSignalBuff, NULL, NumOfTrig, ANGULAR_SAMPLING);
        if (errtmp < 0)
        {
            SETBIT(EE_BiosErr,ADC_IDX);
            debugADC_Error = 2;
            debugADC_ErrorCnt++;
            debugADC_ErrorType = errtmp;
        }
        else /* no error */
        {
            errtmp = ADC_Enable(ANGULAR_SAMPLING);
            
            if (errtmp < 0)
            {
                SETBIT(EE_BiosErr,ADC_IDX);
                debugADC_Error = 3;
                debugADC_ErrorCnt++;
                debugADC_ErrorType = errtmp;
            }
            else /* no error */
            {
                //Inizializzo ed abilito trigger del canale AD
                errtmp = SYS_ADC_SetStatus(ANGULAR_SAMPLING, TRIGGER_ON);
                if (errtmp < 0)
                {
                    SETBIT(EE_BiosErr,ADC_IDX);
                    debugADC_Error = 4;
                    debugADC_ErrorCnt++;
                    debugADC_ErrorType = errtmp;
                }
            }
        }
    }
    
    /* VIENE SETTATO IL FLAG CHE INDICA ACQUISIZIONE PROGRAMMATA */
    AnalogIn_Set_FlgMapAcqStarted(1);
}

void AnalogIn_Set_FlgMapAcqStarted(uint8_T value)
{
    FlgMapAcqStarted = value;
}

#else
void AnalogIn_MapAngAcq_SOA(void)
{
    
}

void FuncMapAngAcq_EndOfAcq(void)
{
    
}

#endif

void AnalogIn_T5ms(void)
{
    AnalogIn_AngThrottle();
    if (Rpm==0)
    {
        MapSignal_PowerOn_5ms();
        HfmSignal_PowerOn_5ms();
        AnalogIn_VLambdaObj_PowerOn_T5ms();
    }
    RpmSens_Diagnosis();
    AnalogIn_VGearPos();
    AnalogIn_VLambda_T5ms();
    AnalogIn_VSens();

    if (ForceInitMapAcqAngle)
    {
        uint8_T id;
        /* Turn off Trigger */

        SYS_ADC_SetStatus(ANGULAR_SAMPLING, TRIGGER_OFF);

        AnalogIn_Set_FlgMapAcqStarted(0);
        AnalogIn_MapAngAcq_SOA();

        CntEOAMap = CntEOAMap - 2; /* Per evitare di riforzare al prossimo ciclo */
        ForceInitMapAcqAngle = 0;
    }
}

void AnalogIn_T10ms(void)
{
    AnalogIn_GasPos();

    AnalogIn_VKeySignal();
    AnalogIn_VKam();

    // Leggo la tensione di batteria solo a chiave inserita
    // altrimenti mantengo l'ultimo valore letto
    if(KeySignal2)
    {
        AnalogIn_VBattery();
    }
//    AnalogIn_VGearPos();
    AnalogIn_VCmdStarter();
    AnalogIn_VDiagStarter();
}

void AnalogIn_T100ms(void)
{
    AnalogIn_TAir();
    AnalogIn_TWater();
    AnalogIn_TWater_2();
    AnalogIn_PresAtm();
    AnalogIn_VFuelTankLevel();
    AnalogIn_EcuTemp();
    AnalogIn_TempSense();
}

void AnalogIn_TDC(void)
{
    AnalogIn_VLambdaObj_TDC_HTDC();
    if(AbsTdc == ABSTDCCALCAIR)
    {
        MapSignal_TDC();
    }
}

void AnalogIn_HTDC(void)
{
    AnalogIn_VLambdaObj_TDC_HTDC();
}

void AnalogIn_Angle(void)
{
    HfmSignal_Angle();

#ifdef _TEST_EXANGLE_
  {
    uint8_t pin;
    DIGIO_OutGet(_TEST_EXANGLE_, (&pin));
    DIGIO_OutSet(_TEST_EXANGLE_, !pin);
  }
#endif
}

void AnalogIn_EOI(void)
{
    AnalogIn_MapSignal_EOI();
}

void AnalogIn_SOI(void)
{
    AnalogIn_MapSignal_SOI();
}

//Funzioni separate per ciascun tipo di sensore trattato
void AnalogIn_AngThrottle(void)
{
    int16_T errtmp;
    uint8_T PtFaultVAngExhVal;
    uint8_T StPtFaultState;

#if defined(IDN_ANG_THROTTLE)
    errtmp = ADC_GetSampleRes(IDN_ANG_THROTTLE, &(VAngThrottle1), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 1;
        debugADC_ErrorCnt_2++;
    }
#endif

#if defined(IDN_ANG_THROTTLE2)
    errtmp = ADC_GetSampleRes(IDN_ANG_THROTTLE2, &(VAngThrottle2), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 2;
        debugADC_ErrorCnt_2++;
    }
#endif
#if defined(IA_ANG_THROTTLE3)
    errtmp = ADC_GetSampleRes(IA_ANG_THROTTLE3, &(VAngThrottle3), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 3;
        debugADC_ErrorCnt_2++;
    }
#endif
#if defined(IA_ANG_THROTTLE4)
    errtmp = ADC_GetSampleRes(IA_ANG_THROTTLE4, &(VAngThrottle4), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 4;
        debugADC_ErrorCnt_2++;
    }
#endif
#if defined(IA_EXH_FBK)
    errtmp = ADC_GetSampleRes(IA_EXH_FBK, &VAngExhVal, 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 67;
        debugADC_ErrorCnt_2++;
    }
    else    /* no error */
    {
        if (USEA2RSIGNAL != 2)
        {
            if (VAngExhVal < VEXHVMIN)
            {
                PtFaultVAngExhVal = CC_TO_GND;
            }
            else if (VAngExhVal < VEXHOVERLOW)
            {
                PtFaultVAngExhVal = SIGNAL_OVERRANGE;
            }
            else if (VAngExhVal > VEXHVMAX)
            {
                PtFaultVAngExhVal = CC_TO_VCC;
            }
            else if (VAngExhVal > VEXHOVERHIGH)
            {
                PtFaultVAngExhVal = SIGNAL_OVERRANGE;
            }
            else
            {
                PtFaultVAngExhVal = NO_PT_FAULT;
            }
            DiagMgm_SetDiagState(DIAG_EXHVALVEFDBK, PtFaultVAngExhVal, &StPtFaultState);
        }
        else { /* MISRA */ }
    }
#endif
}

void AnalogIn_GasPos(void)
{
    int16_T errtmp;

#if defined(IDN_GAS_POSITION)
    errtmp = ADC_GetSampleRes(IDN_GAS_POSITION, &(VGasPos1), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 5;
        debugADC_ErrorCnt_2++;
    }
#endif

#if defined(IDN_GAS_POSITION2)
    errtmp = ADC_GetSampleRes(IDN_GAS_POSITION2, &(VGasPos2), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 6;
        debugADC_ErrorCnt_2++;
    }
#endif

#if defined(IDN_GAS_POSITION3)
    errtmp = ADC_GetSampleRes(IDN_GAS_POSITION3, &(VGasPos3), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 7;
        debugADC_ErrorCnt_2++;
    }
#endif

#if defined(IDN_GAS_POSITION4)
    errtmp = ADC_GetSampleRes(IDN_GAS_POSITION4, &(VGasPos4), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 8;
        debugADC_ErrorCnt_2++;
    }
#endif

#if defined(IDN_GAS_IDLE_SW)
    errtmp = ADC_GetSampleRes(IDN_GAS_IDLE_SW, &(VGasIDLSw), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 31;
        debugADC_ErrorCnt_2++;
    }
#endif
}

void AnalogIn_TAir_Init(void)
{
    int16_T errtmp;

#ifdef IDN_T_AIR
    errtmp = ADC_GetSampleResSoftTrig(IDN_T_AIR, &(VTAir), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 9;
        debugADC_ErrorCnt_2++;
    }
#else    // IDN_T_AIR not defined
    VTAir = 0;
#endif
}

void AnalogIn_TAir(void)
{
    int16_T errtmp;

#ifdef IDN_T_AIR
    /* Lettura TAir da sensore */
    errtmp = ADC_GetSampleRes(IDN_T_AIR, &(VTAir), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 10;
        debugADC_ErrorCnt_2++;
    }
#else    // IDN_T_AIR not defined
    VTAir = 0;
#endif

}

void AnalogIn_VLambdaObj_PowerOn_T5ms(void)
{
#ifdef IDN_LAMBDA_OBJ
    uint16_T tmpAFObjIn;
    int16_T errtmp;

    errtmp = ADC_GetSampleRes(IDN_LAMBDA_OBJ, &(VAFObj), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 11;
        debugADC_ErrorCnt_2++;
    }

    LookUp_U16_U16( &(tmpAFObjIn), VTAFOBJ, VAFObj, BKVAFOBJ, (VTAFOBJ_dim-1));

    FOF_Reset_S16_FXP((int16_T *)&AFObjIn, &VAFObj_hr, tmpAFObjIn, KFILTVLAMOBJ, tmpAFObjIn, TRUE, VAFObj_hr);
#endif
}

void AnalogIn_VLambdaObj_TDC_HTDC(void)
{
#ifdef IDN_LAMBDA_OBJ
    uint16_T tmpAFObjIn;
    int16_T  errtmp;

    errtmp = ADC_GetSampleRes(IDN_LAMBDA_OBJ, &(VAFObj), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 12;
        debugADC_ErrorCnt_2++;
    }

    LookUp_U16_U16( &(tmpAFObjIn), VTAFOBJ, VAFObj, BKVAFOBJ, (VTAFOBJ_dim-1));

    FOF_Reset_S16_FXP((int16_T *)&AFObjIn, &VAFObj_hr, tmpAFObjIn, KFILTVLAMOBJ, tmpAFObjIn, !EndStartFlg, VAFObj_hr);
#endif
}

void AnalogIn_VKam(void)
{
#ifdef IDN_VKAM_SMP
    int16_T errtmp;
    // Lettura VKam
    errtmp = ADC_GetSampleRes(IDN_VKAM_SMP, &(VKam), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 13;
        debugADC_ErrorCnt_2++;
    }
#else
    VKam = V_SMP_ADC_SUPPLY;
#endif
}

void AnalogIn_TWater_Init(void)
{
    int16_T errtmp;

#ifdef IDN_T_WATER
    errtmp = ADC_GetSampleResSoftTrig(IDN_T_WATER, &(VTWater), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 14;
        debugADC_ErrorCnt_2++;
    }
#else   //  IDN_T_WATER not defined
    VTWater = 0;
#endif
}

void AnalogIn_TWater(void)
{
#ifdef IDN_T_WATER
    int16_T errtmp;
    /* Lettura TWater da sensore */
    errtmp = ADC_GetSampleRes(IDN_T_WATER, &(VTWater), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 15;
        debugADC_ErrorCnt_2++;
    }
#else   //  IDN_T_WATER not defined
    VTWater = 0;
#endif

}

void AnalogIn_TWater_2_Init(void)
{
#ifdef IDN_T_WATER_2
    int16_T errtmp;
    errtmp = ADC_GetSampleResSoftTrig(IDN_T_WATER_2, &(VTWater2), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 68;
        debugADC_ErrorCnt_2++;
    }
#else   //  IDN_T_WATER_2 not defined
    VTWater2 = 0;
#endif
}

void AnalogIn_TWater_2(void)
{
#ifdef IDN_T_WATER_2
    int16_T errtmp;
    /* Lettura TWater da sensore */
    errtmp = ADC_GetSampleRes(IDN_T_WATER_2, &(VTWater2), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 69;
        debugADC_ErrorCnt_2++;
    }
    else    /* no error */
    {
        /* Non fare niente. */
    }
#else   //  IDN_T_WATER not defined
    VTWater2 = 0;
#endif
}

uint8_T MapParamChangedFlg(void)
{
    /* returns '1' whenever any of the parameters related to MAP acquisition changes */
    /* returns '0' when all the parameters related to MAP acquisition don't change */
    uint8_T retval;

    retval = ((MAPGAIN != OldMAPGAIN) || (MAPOFFSET != OldMAPOFFSET) || (MAPMIN != OldMAPMIN) || (MAPMAX != OldMAPMAX));

    OldMAPGAIN = MAPGAIN ;
    OldMAPOFFSET = MAPOFFSET;
    OldMAPMAX = MAPMAX;
    OldMAPMIN = MAPMIN;

    return retval;
}


void RpmSens_Diagnosis(void)
{
/* DIAGNOSI FUNZIONALE GIRI */
#if (defined(IDN_MAP_SIGNAL_ANG)) && defined(EN_DIAG_RPM_SENS)
    uint8_T PtFaultRpmSens = NO_PT_FAULT;
    uint8_T StDiagRpmSens = NO_FAULT;
    int32_T    tmpMapSignalRipple;

    if (FlgSyncReady && (Rpm==0) && InjEnable && !MapParamChangedFlg())
    {
        MapSignalMin = min(MapSignalMin, MapSignalAngle);
        MapSignalMax = max(MapSignalMax, MapSignalAngle);
        tmpMapSignalRipple = (int32_T)MapSignalMax - (int32_T)MapSignalMin;
        MapSignalRipple = (uint16_T)(GenAbs(tmpMapSignalRipple, INT16_TYPE));

        if (MapSignalRipple > THMAPRIPPLE)
        {
            PtFaultRpmSens = SIG_NOT_PLAUSIBLE;
        }
        else
        {
            PtFaultRpmSens = NO_PT_FAULT;
        }
    }
    else
    {
        PtFaultRpmSens = NO_PT_FAULT;
        MapSignalMin = MapSignalAngle;
        MapSignalMax = MapSignalAngle;
        MapSignalRipple = 0;
    }
    DiagMgm_SetDiagState(DIAG_RPM_SENS, PtFaultRpmSens, &StDiagRpmSens);
#endif // EN_DIAG_RPM_SENS
}

void MapSignal_PowerOn_5ms(void)
{
    uint8_T PtFaultVMapSignal = NO_PT_FAULT;
    uint8_T  StDiagVMapSignal = NO_FAULT;
    int16_T     errtmp;

    /* INIT - RESET contatore di tentativi di riconoscimento fase */
    CntTryPhase = 0;
    FlgEngCycleCompleted = 0;
    
    if (TSTBIT(EE_BiosErr,ADC_IDX) == 0)
    {
        /* check for Punctual Fault */
        DiagMgm_RangeCheck_U16(&PtFaultVMapSignal, VMapSignal, VMAPMIN, VMAPMAX, CC_TO_GND, CC_TO_VCC);
        DiagMgm_SetDiagState(DIAG_V_MAPSIGNAL, PtFaultVMapSignal, &StDiagVMapSignal);

        GetMapSignal(&MapSignalAngle, VMapSignal);

        MapPhaseFlg = 1;
        MapPhaseValid = 0;

        MapSignal0 = MapSignalAngle;
        MapSignal1 = MapSignalAngle;
        PresInj = MapSignalAngle;
        MapSignalSOI  = MapSignalAngle;
        MapSignalEOI  = MapSignalAngle;
        MapSignalPAtm = MapSignalAngle;

        NFiltMap = MAP_BUFFER;
        MovAvgFilter_S16((int16_T *)&MapSignal, &mapfilter, (int16_T *)MapBuffer, &MapIndex, (int16_T)MapSignal0, NFiltMap, MAP_BUFFER, 1, mapfilter, (int16_T *)MapBuffer, MapIndex);
        MapIndex = 0;

        RatioCalcU16(&MapRatio, MapSignal, PresAtm, MAX_MAPRATIO);
    }
}

void MapSignal_TDC(void)
{
    if((MapIndex == MAP_BUFFER) && (FlgSyncPhased))
    {
        // Solo nel primo evento angolare proveniendo da non fasato
        // Mentre Stsync == WAIT_SYNC devo aggiornare comunque MapSignal0
        MapSignal0 = MapSignalAngle;
        
        NFiltMap = MAP_BUFFER;
        MovAvgFilter_S16((int16_T *)&MapSignal, &mapfilter, (int16_T *)MapBuffer, &MapIndex, (int16_T)MapSignal0, NFiltMap, MAP_BUFFER, 1, mapfilter, (int16_T *)MapBuffer, MapIndex);
    }
    else
    {
        MapSignal0 = MapSignalAngle;
        // Filtraggio MapSignal0
#if (MAP_BUFFER > 1)
        {
            uint8_T tmpNFiltMap;
            unsigned int iRowIndex;
            
            BINARYSEARCH_U16_Near_iL(&(iRowIndex), RpmF, BKRPMFMAP, (BKRPMFMAP_dim-1));
            tmpNFiltMap = VTNFILTMAP[iRowIndex];
            //LookUp_U8_U16( &(tmpNFiltMap), VTNFILTMAP, RpmF, BKRPMFMAP, (BKRPMFMAP_dim-1));
            
            if(tmpNFiltMap > MAP_BUFFER)
            {
                NFiltMap = MAP_BUFFER;
            }
            else
            {
                NFiltMap = tmpNFiltMap;
            }
            MovAvgFilter_S16((int16_T *)&MapSignal, &mapfilter, (int16_T *)MapBuffer, &MapIndex, (int16_T)MapSignal0, NFiltMap, MAP_BUFFER, 0, mapfilter, (int16_T *)MapBuffer, MapIndex);
        }
#else
        MapSignal = MapSignal0;
#endif /*#if (MAP_BUFFER > 1) */
    }

    // Calcolo MapRatio = MapSignal / PresAtm * 100
    RatioCalcU16(&MapRatio, MapSignal, PresAtm, MAX_MAPRATIO);
 }

/* Evento di Start Of Injection proveniente da INJCMD */
void AnalogIn_MapSignal_SOI(void)
{
#if defined(IDN_MAP_SIGNAL_ANG) && (USE_MAP_ACQANGLE == 1)
    if (LastSOICyl == IDXCYLPRESINJ)
    {
        MapAcqSOIAng = CrankAngSOI;
    }
#endif
}

/* Evento di End Of Injection proveniente da INJCMD */
void AnalogIn_MapSignal_EOI(void)
{
#if defined(IDN_MAP_SIGNAL_ANG) && (USE_MAP_ACQANGLE == 1)
    if (LastEOICyl == IDXCYLPRESINJ)
    {
        MapAcqEOIAng = CrankAngEOI;
    }
#endif
}

void GetMapSignal(uint16_T *MapSignal0, uint16_T VMapSignal)
{
    int16_T tmpMapSignal0;

    tmpMapSignal0 = (int16_T)((uint32_T)((((int32_T)VMapSignal*5000) >> 10) * MAPGAIN)>>10) + MAPOFFSET;
    if (tmpMapSignal0 > MAPMAX)
    {
        *MapSignal0 = MAPMAX;
    }
      
    else if(tmpMapSignal0 < MAPMIN)
    {
        *MapSignal0 = MAPMIN;
    }
    else
    { 
        *MapSignal0 = (uint16_T)tmpMapSignal0;
    }
   
}

void AnalogIn_PresAtm(void)
{
#ifdef IDN_ATM_PRESSURE
    int16_T errtmp;
    errtmp = ADC_GetSampleRes(IDN_ATM_PRESSURE, &(VPresAtm), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 20;
        debugADC_ErrorCnt_2++;
    }
    else
    {
    }
#endif    // IDN_ATM_PRESSURE
}

void AnalogIn_VKeySignal(void)
{
#ifdef IDN_KEYSIGNAL
    int16_T errtmp;

    errtmp = ADC_GetSampleRes(IDN_KEYSIGNAL, &(VKeySignal), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 21;
        debugADC_ErrorCnt_2++;
    }
    else /* no error */
    {
        if(VKeySignal > THRKEYON)
        {
            KeySignal2 = 1;
        }
        else
        {
            KeySignal2 = 0;
        }
    }
#else
    KeySignal2 = 1;
#endif
}

void AnalogIn_VBattery_Init(void)
{
    int16_T tmpVBattery;
    int16_T tmpVBatteryF;
    int16_T errtmp;

#ifdef IDN_BATTERY
    errtmp = ADC_GetSampleResSoftTrig(IDN_BATTERY, &(VBatteryIn),10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 22;
        debugADC_ErrorCnt_2++;
    }
    else /* no error */
    {
        tmpVBattery = (int16_T)(((uint32_T)(((uint32_T)VBatteryIn*5000) >> 6) * VBATGAIN) >> 16) + VBATOFFSET;
        VBattery1 = tmpVBattery;
        FOF_Reset_S16_FXP(&tmpVBatteryF, &VBattery_HR, VBattery1, KFILTVBAT, tmpVBattery, TRUE, VBattery_HR);
        VBattery = tmpVBatteryF;
    }   
#endif  // IDN_BATTERY

#ifdef IDN_BATTERY2
    errtmp = ADC_GetSampleResSoftTrig(IDN_BATTERY2, &(VBatteryIn2), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 23;
        debugADC_ErrorCnt_2++;
    }
    else /* no error */
    {
        tmpVBattery = (int16_T)(((uint32_T)(((uint32_T)VBatteryIn2*5000) >> 6) * VBATGAIN) >> 16) + VBATOFFSET;        VBattery2 = tmpVBattery; 
        VBattery2 = tmpVBattery; 
    }  
#endif  // IDN_BATTERY2
}

void AnalogIn_VBattery(void)
{
    uint8_T PtFaultVBatteryIn = NO_PT_FAULT;
    uint8_T StDiagVBatteryIn = NO_FAULT;
    int16_T tmpVBattery;
    int16_T tmpVBatteryF;
    int16_T errtmp;

#ifdef IDN_BATTERY
    errtmp = ADC_GetSampleRes(IDN_BATTERY, &(VBatteryIn), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 24;
        debugADC_ErrorCnt_2++;
    }
    else /* no error */
    {
        tmpVBattery = (int16_T)(((uint32_T)(((uint32_T)VBatteryIn*5000) >> 6) * VBATGAIN) >> 16) + VBATOFFSET;
        PtFaultVBatteryIn = NO_PT_FAULT;
        if(tmpVBattery > VBATINMAX)
        {
            tmpVBattery = (int16_T)VBATINMAX;
        }
        else if(tmpVBattery < VBATINMIN)
        {
            tmpVBattery = (int16_T) (VBAT_RECOVERY*16);
            PtFaultVBatteryIn = SIG_NOT_PLAUSIBLE;
        }
        else
        {
            /**/
        }
        DiagMgm_SetDiagState(DIAG_SENS_VBAT, PtFaultVBatteryIn, &StDiagVBatteryIn);

        // Fault Puntuale sensore
        if (StDiag[DIAG_SENS_VBAT] == FAULT_FILTERING)
        {
            tmpVBattery = VBattery;
        }
        VBattery1 = tmpVBattery;    /* Per visualizzazione */
        /* Filtraggio della tensione di batteria */
        FOF_Reset_S16_FXP(&tmpVBatteryF, &VBattery_HR, VBattery1, KFILTVBAT, tmpVBattery, FALSE, VBattery_HR);
        VBattery = tmpVBatteryF;

        /* Diagnosi regolatore */
        if (Rpm > THRPMDIAGBAT && CntTdcCrk > THTDCDIAGVREG)
        {
            DiagMgm_RangeCheck_U16(&PtFaultVBatteryIn, VBattery, VBATMIN, VBATMAX, UNDER_VOLTAGE, CIRCUIT_MALFUNCTION);
            DiagMgm_SetDiagState(DIAG_V_REG, PtFaultVBatteryIn, &StDiagVBatteryIn);
        }
    }
#endif

#ifdef IDN_BATTERY2
    errtmp = ADC_GetSampleRes(IDN_BATTERY2, &(VBatteryIn2), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 25;
        debugADC_ErrorCnt_2++;
    }
    else /* no error */
    {
        tmpVBattery = (int16_T)(((uint32_T)(((uint32_T)VBatteryIn2*5000) >> 6) * VBATGAIN) >> 16) + VBATOFFSET;
        VBattery2 = tmpVBattery;
    }
#endif  // IDN_BATTERY2
}

void AnalogIn_VFuelTankLevel(void)
{
#ifdef IDN_FUEL_LEVEL
    int16_T FuelTankLevel_tmp;
    int16_T errtmp;

    errtmp = ADC_GetSampleRes(IDN_FUEL_LEVEL, &(VFuelTankLevel), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 26;
        debugADC_ErrorCnt_2++;
    }
    else /* no error */
    {
        FuelTankLevel_tmp = (int16_T)(((((int32_T)((int16_T)VFuelTankLevel - FUELTLOFFSET) * 5000) >> 10) * FUELTLGAIN) >> 7);
        if (FuelTankLevel_tmp < 0)
        {
            FuelTankLevel = 0;
        }
        else
        {
            if (FuelTankLevel_tmp > MAX_PERC)
                FuelTankLevel = MAX_PERC;
            else
                FuelTankLevel = (uint16_T)FuelTankLevel_tmp;
        }
    }
#endif
}

void HfmSignal_PowerOn_5ms(void)
{
#ifdef IDN_HFM_SIGNAL
    int16_T errtmp;

    errtmp = ADC_GetSampleRes(IDN_HFM_SIGNAL, &(VHfmSignal), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 27;
        debugADC_ErrorCnt_2++;
    }
    else /* no error */
    {
        LookUp_U16_U16( &(HfmSignal0), VTHFMKGH, VHfmSignal, BKHFMVOLT, (VTHFMKGH_dim-1));
        MovAvgFilter_S16((int16_T *)&HfmSignal, &HfmSignal_hr, (int16_T *)HfmBuffer, &HfmIndex, (int16_T)HfmSignal0, HFM_BUFFER, HFM_BUFFER, 1, HfmSignal_hr, (int16_T *)HfmBuffer, HfmIndex);
        if (Rpm > RPMHFMMIN)
        {
            QAirCylHfm = (uint16_T)(((uint32_T)HfmSignal * (uint32_T)K_KGH_TO_MGCC)/(uint32_T)Rpm);    // 0..1024 Kg/h 1/64
        }
        else
        {
            QAirCylHfm = 0;
        }
    }
#endif
}

void HfmSignal_Angle(void)
{
#ifdef IDN_HFM_SIGNAL
    int16_T errtmp;
    errtmp = ADC_GetSampleRes(IDN_HFM_SIGNAL, &(VHfmSignal), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 28;
        debugADC_ErrorCnt_2++;
    }
    else /* no error */
    {
        LookUp_U16_U16( &(HfmSignal0), VTHFMKGH, VHfmSignal, BKHFMVOLT, (VTHFMKGH_dim-1));

        MovAvgFilter_S16((int16_T *)&HfmSignal, &HfmSignal_hr, (int16_T *)HfmBuffer, &HfmIndex, (int16_T)HfmSignal0, HFM_BUFFER, HFM_BUFFER, 0, HfmSignal_hr, (int16_T *)HfmBuffer, HfmIndex);

        if (Rpm > RPMHFMMIN)
        {
            QAirCylHfm = (uint16_T)(((uint32_T)HfmSignal * (uint32_T)K_KGH_TO_MGCC)/(uint32_T)Rpm);    // 0..1024 Kg/h 1/64
        }
        else
        {
            QAirCylHfm = 0;
        }
    }
#endif
}

void AnalogIn_VLambda_Init(void)
{
    VLamM = 0;
    VLamP = 0;
    VLam2M = 0;
    VLam2P = 0;
#ifdef ODE_Lambda
    FlgDiagLambda = 1;  // 1 = DISABLE DIAGNOSIS !!
//    DIGIO_OutCfg(ODE_Lambda, FlgDiagLambda);
    DIGIO_OutCfgExt(ODE_Lambda, FlgDiagLambda, OUT_PUSHPULL, DRVSTR_10PF, MIN_SLEWRATE);
#endif
}

void AnalogIn_VLambda_T5ms(void)
{
    int16_T errtmp;
#ifdef IDN_LAM1_P
    errtmp = ADC_GetSampleRes(IDN_LAM1_P, &(VLamP), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 29;
        debugADC_ErrorCnt_2++;
    }
#endif
#ifdef IDN_LAM1_M
    errtmp = ADC_GetSampleRes(IDN_LAM1_M, &(VLamM), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 30;
        debugADC_ErrorCnt_2++;
    }
#endif
#ifdef IDN_LAM2_P
    errtmp = ADC_GetSampleRes(IDN_LAM2_P, &(VLam2P), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 31;
        debugADC_ErrorCnt_2++;
    }
#endif
#ifdef IDN_LAM2_M
    errtmp = ADC_GetSampleRes(IDN_LAM2_M, &(VLam2M), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 32;
        debugADC_ErrorCnt_2++;
    }
#endif
}

//Internal ECU Temperature & Voltage Measure
void AnalogIn_EcuTemp(void)
{
#ifdef IDN_T_ECU1
    int16_T errtmp;

    errtmp = ADC_GetSampleRes(IDN_T_ECU1, &(VTEcu1), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 33;
        debugADC_ErrorCnt_2++;
    }
    else /* no error */
    {
        LookUp_S16_U16( &(TEcu1), VTTECU, VTEcu1, BKTECU, (VTTECU_dim-1));
    }
#endif
#ifdef IDN_T_ECU2
    int16_T errtmp;

    errtmp = ADC_GetSampleRes(IDN_T_ECU2, &(VTEcu2), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 34;
        debugADC_ErrorCnt_2++;
    }
    else /* no error */
    {
        LookUp_S16_U16( &(TEcu2), VTTECU, VTEcu2, BKTECU, (VTTECU_dim-1));
    }
#endif
#ifdef IDN_V_ECU_GND
    int16_T errtmp;

    errtmp = ADC_GetSampleRes(IDN_V_ECU_GND, &(VEcuGnd), 10);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 35;
        debugADC_ErrorCnt_2++;
    }
#endif
}

void AnalogIn_VSens(void)
{
    uint8_T PtFaultVSens1 = NO_PT_FAULT, PtFaultVSens2 = NO_PT_FAULT;
    uint8_T  StPtFaultVSens1 = NO_FAULT, StPtFaultVSens2 = NO_FAULT;
    int16_T errtmp;
#ifdef IDN_V_SENS1
    errtmp = ADC_GetSampleRes(IDN_V_SENS1, &(VSens1), 11);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 36;
        debugADC_ErrorCnt_2++;
    }
    else    /* no error */
    {
        // Fault Puntuale
        DiagMgm_RangeCheck_U16(&PtFaultVSens1, VSens1, VSENSMIN, VSENSMAX, CC_TO_GND, CC_TO_VCC);
        DiagMgm_SetDiagState(DIAG_VSENS1, PtFaultVSens1, &StPtFaultVSens1);
    }
#endif // IDN_V_SENS1

#ifdef IDN_V_SENS2
    errtmp = ADC_GetSampleRes(IDN_V_SENS2, &(VSens2), 11);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 37;
        debugADC_ErrorCnt_2++;
    }
    else    /* no error */
    {
        /* Fault Puntuale */
        DiagMgm_RangeCheck_U16(&PtFaultVSens2, VSens2, VSENSMIN, VSENSMAX, CC_TO_GND, CC_TO_VCC);
        DiagMgm_SetDiagState(DIAG_VSENS2, PtFaultVSens2, &StPtFaultVSens2);
    }
#endif // IDN_V_SENS2

#ifdef IDN_VREF_H
    errtmp = ADC_GetSampleRes(IDN_VREF_H, &VRefH, 16);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 38;
        debugADC_ErrorCnt_2++;
    }
#endif
#ifdef IDN_VREF_L
    errtmp = ADC_GetSampleRes(IDN_VREF_L, &VRefL, 16);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 39;
        debugADC_ErrorCnt_2++;
   }
#endif
#ifdef IDN_VREF_HALF
    errtmp = ADC_GetSampleRes(IDN_VREF_HALF, &VRefHalf, 16);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 40;
        debugADC_ErrorCnt_2++;
    }
#endif
}

void AnalogIn_ADC_Calibration(void)
{
#if defined(IDN_VREF_25) && defined(IDN_VREF_75)
    uint8_T i;
    int32_T tmpVRef75 = 0;
    int32_T tmpVRef25 = 0;
    int16_T errtmp;

    /* Leggo N_ADC_SAMPLE consecutivi e ne faccio la media */
    for(i=0;i<N_ADC_SAMPLE;i++)
    {
        errtmp = ADC_GetSampleRes(IDN_VREF_75, &VRef75, 16);  /* 2^-16 */
        if (errtmp < 0)
        {
            SETBIT(EE_BiosErr,ADC_IDX);
            debugADC_Error_2 = 41;
            debugADC_ErrorCnt_2++;
        }
        else /* no error */
        {
            tmpVRef75 += VRef75;
        }
        UTILS_nop();
        UTILS_nop();

        errtmp = ADC_GetSampleRes(IDN_VREF_25, &VRef25, 16);  /* 2^-16 */
        if (errtmp < 0)
        {
            SETBIT(EE_BiosErr,ADC_IDX);
            debugADC_Error_2 = 42;
            debugADC_ErrorCnt_2++;
        }
        else /* no error */
        {
            tmpVRef25 += VRef25;
        }
        UTILS_nop();
        UTILS_nop();
    }
    tmpVRef75 /= N_ADC_SAMPLE;
    tmpVRef25 /= N_ADC_SAMPLE;
    VRef75 = (uint16_T)tmpVRef75;              // 2^-16
    VRef25 = (uint16_T)tmpVRef25;                // 2^-16
    tmpVRef75 >>= 2;                                      // 2^-14
    tmpVRef25 >>= 2;                                      // 2^-14

    // Calcolo gain GCC = (V_REF_75 - V_REF_25) / (VRef75 - VRef25)
    GainCC = (int16_T)(((int32_T)(V_REF_75 - V_REF_25) << 14) / (tmpVRef75 - tmpVRef25));     // 2^-14

    // Calcolo offset OCC = V_REF_75 - GCC * VRef75 - 2
    OffsetCC = (int16_T)((int32_T)V_REF_75 - ((int32_T)(GainCC * tmpVRef75) >> 14) - 2);     // 2^-14

#ifdef EN_ADC_CALIBRATION
    /* A questo punto i dati sono pronti per essere passati all'ADC */
    errtmp = ADC_CalibrationSet(GainCC, OffsetCC);
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 43;
        debugADC_ErrorCnt_2++;
    }
#endif
#endif // IDN_VREF_25 && IDN_VREF_75
}


void AnalogIn_VGearPos(void)
{
#ifdef IA_GEAR_POS
    int16_t errtmp;
    static uint8_T PtFaultGearPosIn = NO_PT_FAULT;
    static uint16_T cnt_vgpos = 0;
    static int32_T hr_VGearPos0 = 0;

    if (EECANNode4En == 0)
    {
        errtmp = ADC_GetSampleRes(IA_GEAR_POS, &VGearPos0, 10);
    }
    else
    {
        errtmp = 0;
        VGearPos0 = VGearPosCAN;
    }
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 44;
        debugADC_ErrorCnt_2++;
        StDiagGearPosIn = SIGNAL_OVERRANGE;
    }
    else
    {
        FOF_Reset_S16_FXP(&VGearPos, &hr_VGearPos0, VGearPos0, KFILTVGEARPOS, 0, FALSE, hr_VGearPos0);

        if (VGearPos < VGEARPOSMIN)
        {
            PtFaultGearPosIn = CC_TO_GND;
            cnt_vgpos = 0;
        }
        else if (VGearPos < VGEAROVERLOW)
        {
            PtFaultGearPosIn = SIGNAL_OVERRANGE;
            cnt_vgpos = 0;
        }
        else if (VGearPos > VGEARPOSMAX)
        {
            if (cnt_vgpos == 0)
            {
                cnt_vgpos = 1;
            }
            else if (cnt_vgpos > TGEARSHIFT)
            {
                PtFaultGearPosIn = CC_TO_VCC;
            }
            else
            {
                cnt_vgpos++;
            }
        }
        else if (VGearPos > VGEAROVERHIGH)
        {
            PtFaultGearPosIn = SIGNAL_OVERRANGE;
            cnt_vgpos = 0;
        }
        else if ((VDEGearAnalogCAN != 0) || (StDiag[DIAG_VEH_CAN_NODE_4] == FAULT))
        {
            PtFaultGearPosIn = SIGNAL_OVERRANGE;
            cnt_vgpos = 0;
        }
        else
        {
            PtFaultGearPosIn = NO_PT_FAULT;
            cnt_vgpos = 0;
        }
        if (Rpm >= THRPMGEARDIAG)
        {
            StDiagGearPosIn = PtFaultGearPosIn;
            //DiagMgm_SetDiagState(DIAG_GEAR_SENSOR, PtFaultGearPosIn, &StDiagGearPosIn);
        }
        else
        {
            if (PtFaultGearPosIn == NO_PT_FAULT)
            {
                StDiagGearPosIn = PtFaultGearPosIn;
                //DiagMgm_SetDiagState(DIAG_GEAR_SENSOR, PtFaultGearPosIn, &StDiagGearPosIn);
            }
            else 
            {
                cnt_vgpos = 0;
            }
        }
    }
#endif
}

void AnalogIn_VCmdStarter(void)
{
#ifdef IA_CMD_STARTER
    int16_t errtmp;
    uint16_t tmpval;

    errtmp = ADC_GetSampleRes(IA_CMD_STARTER, &tmpval, 10);  /* 2^-10 */
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 45;
        debugADC_ErrorCnt_2++;
    }
    else /* no error */
    {
        VCmdStarter = tmpval;
    }
#endif
}


void AnalogIn_VDiagStarter(void)
{
#ifdef IA_DIAG_STARTER
    int16_t errtmp;
    uint16_t tmpval;

    errtmp = ADC_GetSampleRes(IA_DIAG_STARTER, &tmpval, 10);  /* 2^-10 */
    if (errtmp < 0)
    {
        SETBIT(EE_BiosErr,ADC_IDX);
        debugADC_Error_2 = 46;
        debugADC_ErrorCnt_2++;
    }
    else /* no error */
    {
        VDiagStarter = tmpval;
    }
#endif
}

int16_T AnalogIn_DigIn(uint8_T anch, uint16_T loth, uint16_T hith, uint16_T diaglow, uint16_T diaghigh, uint8_T mode, uint8_T *pOut, uint8_T *ppfault, uint8_T *ppnotval, uint16_T *pAnlFdbk)
{
/* This function converts an analog input into a digital signal */
/* input parameters: */
/*      anch = analog input channel */
/*      loth = maximum threshold to get LOW value */
/*      hith = minimum threshold to get HIGH value */
/*      diaglow = value under which we detect CC_TO_GND */
/*      diaghigh = value over which we detect CC_TO_Vcc */
/*      mode = type of conversion */
/*                                             */
/* output parameters: */
/*      pOut = digital output value */
/*      ppfault = diagnosis result */
/*      ppnotval = value not plausible */
/*      exit value = error code */
    uint16_T    anval;  /* analog value sampled */
    int16_T     err = NO_ERROR;

    *ppfault = NO_PT_FAULT;
    *ppnotval = 1;
    if (mode == ADC_ON_DEMAND)
    {
        err = ADC_GetSampleResSoftTrig(anch, &anval, 10);
    }
    else
    {
        err = ADC_GetSampleRes(anch, &anval, 10);
    }
    *pAnlFdbk = anval;
    if (err == NO_ERROR)
    {
        if (anval < diaglow)
        {
            *ppfault = CC_TO_GND;
            *ppnotval = 1;
        }
        else if(anval <= loth)
        {
            /* low value */
            *pOut = 0;
            *ppnotval = 0;
        }
        else if(anval > diaghigh)
        {
            *ppfault = CC_TO_VCC;
            *ppnotval = 1;
        }
        else if(anval >= hith)
        {
            /* high value */
            *pOut = 1;
            *ppnotval = 0;
        }
        else /* transition or not allowed value */
        {
            *ppfault = OPEN_CIRCUIT;
            *ppnotval = 1;
        }
    }
    else
    {
        /* Non fare niente. */
    }
    return err;
}


int16_T AnalogIn_TempSense(void)
{
#if defined(BANDGAP) && defined(TEMP_SENSOR)

    static int32_T sumVBoardTemperature = (0x1FFFF * BOARD_TEMP_BUFF);
    static int16_T VBoardTemperatureBuf[BOARD_TEMP_BUFF];
    static uint8_T i = 0;

    static int32_t tCodeHighLowDiff;
    static int32_t tHighLowDiff;
    static int32_t tSenseCodeLowtHighLow;
    static uint8_t tHigh = 0;
    static uint8_t tLow = 0;
    static int32_t tLow_16;

    uint16_T bandgap;
    uint16_t adcBoardTemperatureVal = 0;
    int16_T errtmp = 0;

    /* Initialize temperature constants */
    if(tHigh == 0)
    {
        if(T_SENSE_T_HIGH == 2)
        {
            tHigh = 145;
        }
        else if(T_SENSE_T_HIGH == 3)
        {
            tHigh = 150;
        }
        else
        {
            errtmp = -1;
        }

        if(errtmp != -1)
        {
            if(T_SENSE_T_LOW == 0)
            {
                tLow = 25;
            }
            else if(T_SENSE_T_LOW == 1)
            {
                tLow = 40;
            }
            else
            {
                errtmp = -1;
            }
        }
        // tLow 2^4
        tLow_16 = tLow *16;
        // Calculate the difference between Thigh and Tlow 2^0
        tHighLowDiff = (tHigh - tLow);
        // Ttsens_code(Tlow) x (Thigh -Tlow) 2^4
        tSenseCodeLowtHighLow = T_SENSE_CODE_TLOW * tHighLowDiff;
        // Calculate the difference between the adc code read at Tlow and Thigh
        tCodeHighLowDiff = T_SENSE_CODE_THIGH -T_SENSE_CODE_TLOW;
    }

    if(errtmp == 0)
    {
        errtmp = ADC_GetSampleRes(BANDGAP, &bandgap, 14);  /* 2^14 */
        if (errtmp < 0)
        {
            SETBIT(EE_BiosErr,ADC_IDX);
            debugADC_Error_2 = 45;
            debugADC_ErrorCnt_2++;
        }
    }
    if(errtmp == 0)
    {
        errtmp = ADC_GetSampleRes(TEMP_SENSOR, &adcBoardTemperatureVal, 14);  /* 2^14 */
        if (errtmp < 0)
        {
            SETBIT(EE_BiosErr,ADC_IDX);
            debugADC_Error_2 = 128;
            debugADC_ErrorCnt_2++;
        }
    }

    if(errtmp == 0)
    {
        sumVBoardTemperature -= VBoardTemperatureBuf[i]; // Remove last value calculated
        VBoardTemperatureBuf[i] = tLow_16 + ((((int32_t)tHighLowDiff * ((int32_t)(adcBoardTemperatureVal * bandgap) / V_BG_CODE_TLOW) - tSenseCodeLowtHighLow)* 16) / tCodeHighLowDiff);
        sumVBoardTemperature += VBoardTemperatureBuf[i];
        i++;
        if(i >= BOARD_TEMP_BUFF)
        {
            i = 0;
        }
        TBoardTemperature = sumVBoardTemperature / BOARD_TEMP_BUFF; // Devide by BOARD_TEMP_BUFF to calculate the average value
    }
    return errtmp;
#endif // (BANDGAP) && defined(TEMP_SENSOR)
}


#endif // _BUILD_ANALOGIN_

