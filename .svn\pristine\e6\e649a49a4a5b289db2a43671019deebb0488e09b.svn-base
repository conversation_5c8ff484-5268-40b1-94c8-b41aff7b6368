/*****************************************************************************************************************/
/* $HeadURL:: http://***********:8080/svn/Rep_Bo/EM/appl_calib/branches/LL_MV6_32_DIAG/tree/DD/CANMGM/CANMGM_#$  */
/* $ Description:                                                                                                */
/* $Revision:: 13810  $                                                                                          */
/* $Date:: 2022-02-11 08:23:50 +0100 (ven, 11 feb 2022)   $                                                      */
/* $Author:: LanaL                   $                                                                           */
/*****************************************************************************************************************/

#ifdef _BUILD_CANMGM_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "CANMGM_MV_950.h"

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
#define BUS_OFF_QUICK_DELAY 12u  // 60ms delay for Quick BusOff recovery
#define BUS_OFF_SLOW_DELAY  40u  // 200ms delay for Slow BusOff recovery

#define DIV_ELEAN_CAN_MSG_10MS 3 /* Read every 10ms, send every 10ms */

#define DASH_EMPTY    2  // 20ms   -> 10ms  @30ms
#define DS_INFO_EMPTY 2  // 20ms   -> 10ms  @30ms
#define ABS_EMPTY     2  // 10ms   -> 5ms   @15ms
#define LHB_EMPTY     3  // 50ms   -> 20ms  @80ms
#define RHB_EMPTY     3  // 50ms   -> 20ms  @80ms
#define MCU1_EMPTY    3  // 50ms   -> 20ms  @80ms
#define KLS_RRS_EMPTY 10 // 100ms  -> 10ms  @110ms
#define GPS_02_EMPTY  2  // 100ms  -> 50ms  @150ms
#define GPS_04_EMPTY  2  // 100ms  -> 50ms  @150ms
#define EGEAR_EMPTY   2  // 5ms    -> 5ms   @15ms
#define TPMS_EMPTY    2  // 1000ms -> 500ms @1500ms

#define GP_SWITCH_1ST_NEUTRAL 0
#define GP_SWITCH_OTHER       1

#define FC_ELEAN_IDLE  0
#define FC_ELEAN_START 1
#define FC_ELEAN_WAIT  2
#define FC_ELEAN_FLT   3

#define CANSEND_STOP     0
#define CANSEND_WAITSTOP 1
#define CANSEND_START    2

#define ECU_RRS_ID 2

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
typedef struct StrBusOff_Tag
{
    uint8_T cntBusOffRec;
    uint8_T cntQuickBusOffRec;
    uint8_T cntSlowBusOffRec;
    uint8_T flgRecBusOffRan;
} StrBusOff;

/*-----------------------------------*
 * PRIVATE CONST
 *-----------------------------------*/
static const uint8_T VtCntWrong[16]=
{
    1, 0, 3, 2, 7, 4, 6, 5, 15, 11, 14, 10, 9, 12, 8, 13
};

/*-----------------------------------*
 * PUBLIC VARIABLES
 *-----------------------------------*/
/* SUBBED */
uint8_T  EnCutOffCAN;
int16_T  TWaterCAN;
uint8_T  VDTWaterCAN;
uint8_T  FlgTipIn;
uint8_T  QuickShiftTypeCAN;
uint8_T  FlgNoTrqCtrSACAN;
uint8_T  FlgEMDSoftStop;
uint16_T LoadCAN;
uint8_T  VDLoadCAN;
uint16_T RpmCAN;
uint8_T  VDRpmCAN;

/* ENG1 */
/* */

/* ENG2 */
/* */

/* ENG3 */
/* */

/* ENG4 */
/* */

/* ENG5 */
/* */

/* ENG7 */
/* */

/* ERRS */
/* */

/* EVIN */
uint8_T EcuVIN[17];

/* LAM */
uint16_T LamCAN[N_MSG_LAM];

/* MCU1 */
int16_T  TAirCAN;
uint8_T  VDTAirCAN;
#ifdef MCU1
uint8_T  MainRelCmd;
#endif
#ifdef MCU1
uint8_T  ParkBrakeSignal;
#endif
uint8_T  RiderPresenceCAN;
uint8_T  StarterCmdFdbkCAN;
uint8_T  TransportLock;
uint8_T  CntMCU1CAN;

/* KLS_RRS */
uint8_T  InjDisableIMMO;

/* ABS */
uint8_T  VDVehSpeedFrontCAN;
uint8_T  VDVehSpeedRearCAN;
uint16_T VehSpeedFrontCAN;
uint16_T VehSpeedFrontNcCAN;
uint16_T VehSpeedRearCAN;
uint16_T VehSpeedRearNcCAN;
uint8_T  FrontWheelPresCAN;
uint8_T  VDFrontWheelPresCAN;
uint16_T BufVehSpeedRearCAN[SIZE_VS_CAN];
uint16_T BufVehSpeedFrontCAN[SIZE_VS_CAN];

/* DASH */
uint8_T  LcToExitCAN;
uint8_T  LcLevelCAN;
uint8_T  AwLevelCAN;
uint8_T  EnVSpeedLimM;
uint8_T  QuickShiftEnCAN;
uint8_T  FlgLowFuel;
uint8_T  SetTracCtrlCAN;
uint8_T  VehSpeedCtrlCAN;
uint16_T VehSpeedSetUpCAN;
uint8_T  GripHeatLevelCAN; //niso added.

/* DINFO */
uint8_T  OTAUpdate;
uint8_T  TCSetupRequest;
uint8_T  TyreTypeCAN;
uint8_T  InitStarterEnCAN;

/* DVIN */
uint8_T  DashVIN[17];
uint8_T  FlgDashVIN_B0;
uint8_T  FlgDashVIN_B1;
uint8_T  FlgDashVIN_B2;
uint8_T  FlgDashVINEchoReply_B0;
uint8_T  FlgDashVINEchoReply_B1;
uint8_T  FlgDashVINEchoReply_B2;

/* E-GEAR */
uint8_T  GearShiftWaitCAN;
uint8_T  DownShiftReqCAN;
uint8_T  NeutralReqCAN;
uint8_T  UpShiftReqCAN;
uint16_T VGearPosCAN;
uint8_T  VDEGearAnalogCAN;

/* E-LEAN */
int16_T  AxCAN;
int16_T  WzCAN;
int16_T  PitchCAN;
int16_T  RollCAN;
uint16_T AbsRollCAN;

/* LHB */
uint8_T  ClutchSignal1CAN;
uint8_T  ClutchSignal2CAN;
uint8_T  HornSignalCAN;

/* RHB */
uint8_T  StopSignal;
uint8_T  StartSignalCAN;

/* GPS02 */
uint16_T AltitudeGPS;
uint16_T VehSpeedGPS;

/* GPS04 */
uint8_T  ErrorGPS;
uint8_T  FixGPS;

/* TPMS */
uint8_T  TPMSLoc;
uint8_T  TPMSPre;
uint8_T  TPMSWfc;
uint8_T  TPMSRst;
uint8_T  TPMSSda;
uint8_T  TPMSNode;

/* CANMGM */
uint8_T  VDAbsWL;
uint8_T  VehOptConfig;
uint8_T  BrakeSignalCAN;
uint8_T  COBStartActive;
//uint8_T  BrakeSignal;
uint8_T  FlgYawRec;
uint8_T  ATroutineFlagCAN;
uint8_T  StCanSendEn;
uint8_T  KeyStored;

uint8_T     VDVBattery; //niso added.

/*-----------------------------------*
 * PRIVATE VARIABLES AND TESTPOINTS
 *-----------------------------------*/
/* ENG1 */
CCPTEST uint8_T  FlgTracCtrlEn;
CCPTEST uint8_T  TracCtrlAct;
CCPTEST uint8_T  KitMapSel;
CCPTEST uint8_T  GearPosDashboard;

/* ENG2 */
CCPTEST uint8_T  VDQFuelLth;
CCPTEST uint8_T  FdbVehSpeedCtrlCAN;

/* ENG3 */
CCPTEST uint8_T  InjDisableIMMOCAN;
CCPTEST uint8_T  FlgDownload;

/* ENG4 */
CCPTEST uint8_T  IDMuxE4;

/* ENG5 */
CCPTEST uint8_T  IDMuxE5;

/* ENG7 */
/* */

/* ERRS */
CCPTEST uint16_T RndAnalogIn;
CCPTEST uint32_T KLSRnd;
CCPTEST uint8_T  CntKeyImmo;

/* LAM */
CCPTEST uint16_T NRxFrLAM[N_MSG_LAM];

/* KLS_RRS */
CCPTEST uint32_T KLSSign;
CCPTEST uint32_T OldECUUID;
CCPTEST uint8_T  McuKLSReq;
CCPTEST uint8_T  CntKLSResend;

/* MCU1 */
CCPTEST uint8_T  FlgNCMImmo;
CCPTEST uint16_T MCU1Timestamp;
CCPTEST uint8_T  VDBrakeSignal;
CCPTEST uint8_T  BrakeLampCAN;

/* ABS */
CCPTEST uint8_T  ABSPresent;
CCPTEST uint8_T  ABSWarningLamp;
CCPTEST uint16_T VehSpeedFrontRawCAN;
CCPTEST uint16_T VehSpeedRearRawCAN;
CCPTEST uint16_T CntCANNode3En;

/* DASH */
CCPTEST uint16_T DASHTimestamp;
CCPTEST uint8_T  BKLStatusCAN;
CCPTEST uint8_T  StSuspCAN;
CCPTEST uint8_T  StSuspReqCAN;

/* DINFO */
CCPTEST uint8_T  QSSetup;

/* DVIN */
CCPTEST int16_T  CanRxErrVinCodeDASH;

/* E-GEAR */
CCPTEST uint8_T  VDEGearDigCAN;
CCPTEST uint16_T CntCANNode4En;

/* E-LEAN */
CCPTEST uint8_T  VDAzCAN;
CCPTEST int16_T  AzCAN;
CCPTEST uint8_T  VDAyCAN;
CCPTEST int16_T  AyCAN;
CCPTEST uint8_T  VDAxCAN;
CCPTEST uint8_T  VDWzCAN;
CCPTEST uint8_T  VDWyCAN;
CCPTEST int16_T  WyCAN;
CCPTEST uint8_T  VDWxCAN;
CCPTEST int16_T  WxCAN;
CCPTEST uint16_T PitchRawCAN;
CCPTEST uint8_T  VDPitchCAN;
CCPTEST uint8_T  VDRollCAN;
CCPTEST uint8_T  VDRollCANDeb;
CCPTEST uint16_T RollRawCAN;
CCPTEST uint16_T CntCANNode2En;
CCPTEST int16_T  CanRxErrAxsPos1;
CCPTEST int16_T  CanRxErrAxsPos2;
CCPTEST int16_T  CanRxErrAxsPos3;

/* LHB */
CCPTEST uint8_T  LHsStError;

/* RHB */
CCPTEST uint8_T  RHsStError;

/* GPS_02 */
/* */

/* GPS_04 */
/* */

/* TPMS */
CCPTEST uint8_T  TPMSTem;
CCPTEST uint8_T  TPMSBal;
CCPTEST uint16_T TPMSTim;
CCPTEST uint8_T  TPMSCom;
CCPTEST uint8_T  TPMSTtd;
CCPTEST uint8_T  TPMSPdt;
CCPTEST uint16_T CntCANNode5En;

/* CANMGM */
CCPTEST StrBusOff VehBusOffRec;
CCPTEST StrBusOff PriBusOffRec;
CCPTEST uint8_T  Gen05[8];
CCPTEST uint8_T  Gen10[8];
CCPTEST uint8_T  InvS10[8];
CCPTEST uint8_T  Gen100[8];
CCPTEST uint8_T  InvS100[8];
CCPTEST uint8_T  CanRxErrDash;
CCPTEST uint8_T  CanRxErrGPS;
CCPTEST uint8_T  ValidCANDiagAx1;
CCPTEST uint8_T  ValidCANDiagAx2;
CCPTEST uint8_T  ValidCANDiagAx3;
CCPTEST uint16_T FrmLostDashboard;
CCPTEST uint16_T FrmLostDBInfo;
CCPTEST uint16_T FrmLostEGear;
CCPTEST uint16_T FrmLostMCU1;
CCPTEST uint16_T FrmLostLHB;
CCPTEST uint16_T FrmLostRHB;
CCPTEST uint16_T FrmLostABS;
CCPTEST uint16_T FrmLostKLSRRS;
CCPTEST uint8_T  FlgNoNetworkDiag;
CCPTEST uint8_T  FlgWaitDashVINEchoReply;
CCPTEST uint16_T CntNoDiagAfterKeyOn;
CCPTEST int16_T  CanStatus;
CCPTEST int16_T  PTCanSt;
CCPTEST int16_T  PRCanSt;

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
 /*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
 static void CANMGM_CanSend_10ms(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_CanSend_20ms(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_CanRecv_10ms(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_CanRecv_20ms(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_CanRecv_50ms(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_CanRecv_100ms(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_CanRecv_500ms(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Send_ENGINE1(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Send_ENGINE2(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Send_ENGINE3(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Send_ENGINE4(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Send_ENGINE5(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Send_ENGINE7(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Send_eLean1(void);
/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Send_VIN_CODE_ECU(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Send_ERRS(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_LAM(uint8_T can_ch, uint8_T lam_buf, uint8_T lam_buf_quelen, uint8_T ind_lam);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_KLS_RRS(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_DASHBOARD(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_DASH_INFO(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_ABS(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_MCU1 (void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_LEFT_HB (void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_RIGHT_HB (void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_GPS_02(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_GPS_04(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_EGEAR_1M(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_AXISPOSITION1(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_AXISPOSITION2(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_AXISPOSITION3(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_TPMS(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_VIN_CODE_DASH(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_DASH_Data(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_DASH_INFO_Data(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_ABS_Data(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_MCU1_Data(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_LHB_Data(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_RHB_Data(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_GPS_02_Data(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_GPS_04_Data(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_EGEAR_1M_Data(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_AXISPOSITION1_Data(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_AXISPOSITION2_Data(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_AXISPOSITION3_Data(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_TPMS_Data(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_BusOffTestAndRec(uint8_T channel, uint8_T diag, StrBusOff *busoffrec);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static uint8_T CANMGM_DiagMsg(uint8_T diagID, uint8_T ptFault);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Calc_GearPos (uint8_T *gearPosDashboard, uint8_T gearPos);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Calc_BrakeSignal(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_KeyImmo(void);

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CANMGM_ResetVehOptConfig(void)
{
    EECANNode2En = 0;
    EECANNode3En = 0;
    EECANNode4En = 0;
    EECANNode5En = 0;
    EEFlgsID7 = 0;
    VehOptConfig = 0;
}

 /*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CANMGM_Init(void)
{
    uint8_T i = 0;
    
    /* SUBBED */
    EnCutOffCAN = 0;
    TWaterCAN = (90 * 16);
    VDTWaterCAN = 0;
    FlgTipIn = 0;
    QuickShiftTypeCAN = 1;
    FlgNoTrqCtrSACAN = 1;
    FlgEMDSoftStop = 0;
    LoadCAN = 0;
    VDLoadCAN = 0;
    RpmCAN = 0;
    VDRpmCAN = 0;

    /* ENG7 */
    VDVBattery = 0; //niso added.

    /* ENG1 */
    FlgTracCtrlEn = 0;
    TracCtrlAct = 0;
    KitMapSel = KITMAP;
    GearPosDashboard = 0;

    /* ENG2 */
    VDQFuelLth = 0;
    FdbVehSpeedCtrlCAN = 2;

    /* ENG3 */
    InjDisableIMMOCAN = 1;
    FlgDownload = 0;

    /* ENG4 */
    IDMuxE4 = 0;

    /* ENG5 */
    IDMuxE5 = 0;

    /* MCU1 */
    TAirCAN = (20 * 16);
    VDTAirCAN = 0;
    VDBrakeSignal = 0;
    #ifdef MCU1
    MainRelCmd = 0;
    #endif
    #ifdef MCU1
    ParkBrakeSignal = 0;
     #endif
    BrakeLampCAN = 0;
    StarterCmdFdbkCAN = 0;
    RiderPresenceCAN = 0;
    TransportLock = 1;

    /* ABS */
    VDAbsWL = 0;
    VDVehSpeedFrontCAN = 0;
    VDVehSpeedRearCAN = 0;
    VehSpeedFrontCAN = 0;
    VehSpeedFrontNcCAN = 0;
    VehSpeedRearCAN = 0;
    VehSpeedRearNcCAN = 0;
    ABSPresent = 0;
    ABSWarningLamp = 0;
    VehSpeedFrontRawCAN = 0;
    VehSpeedRearRawCAN = 0;
    FrontWheelPresCAN = 0;
    VDFrontWheelPresCAN = 0;
    while (i < SIZE_VS_CAN)
    {
        BufVehSpeedRearCAN[i] = 0;
        BufVehSpeedFrontCAN[i] = 0;
        i++;
    }
    CntCANNode3En = 0;

    /* DASH */
    EnVSpeedLimM = 0;
    QuickShiftEnCAN = 0;
    FlgLowFuel = 0;
    SetTracCtrlCAN = 0;
    BKLStatusCAN = 0;
    VehSpeedCtrlCAN = 0;
    VehSpeedSetUpCAN = 0;
    StSuspCAN = 0;
    StSuspReqCAN = 0;

    /* DINFO */
    QSSetup = 0;
    OTAUpdate = 0;
    TCSetupRequest = 0;
    TyreTypeCAN = 0;
    InitStarterEnCAN = 0;

    /* DVIN */
    memset(DashVIN, 0, sizeof(DashVIN));
    FlgDashVIN_B0 = 0;
    FlgDashVIN_B1 = 0;
    FlgDashVIN_B2 = 0;
    FlgDashVINEchoReply_B0 = 0;
    FlgDashVINEchoReply_B1 = 0;
    FlgDashVINEchoReply_B2 = 0;

    /* E-GEAR */
    GearShiftWaitCAN = 0;
    DownShiftReqCAN = 0;
    NeutralReqCAN = 0;
    UpShiftReqCAN = 0;
    VDEGearDigCAN = 0;
    VDEGearAnalogCAN = 0;
    VGearPosCAN = 0;
    CntCANNode4En = 0;

    /* E-LEAN */
    VDAzCAN = 0;
    AzCAN = 0;
    VDAyCAN = 0;
    AyCAN = 0;
    VDAxCAN = 0;
    AxCAN = 0;
    VDPitchCAN = 0;
    PitchRawCAN = 0;
    PitchCAN = 0;
    VDRollCAN = 0;
    VDRollCANDeb = 0;
    RollRawCAN = 0;
    RollCAN = 0;
    AbsRollCAN = 0;
    CntCANNode2En = 0;

    /* LHB */
    ClutchSignal1CAN = 0;
    ClutchSignal2CAN = 0;
    HornSignalCAN = 0;
    LHsStError = 0;

    /* RHB */
    StopSignal = 0;
    StartSignalCAN = 0;
    RHsStError = 0;

    /* GPS02 */
    AltitudeGPS = 0;
    VehSpeedGPS = 0;

    /* GPS04 */
    ErrorGPS = 0;
    FixGPS = 0;

    /* TPMS */
    TPMSLoc = 0xFF;
    TPMSPre = 0xFF;
    TPMSTem = 0xFF;
    TPMSWfc = 0xFF;
    TPMSBal = 0xFF; 
    TPMSRst = 0xFF;
    TPMSTim = 0xEFFF;
    TPMSCom = 0xFF;
    TPMSSda = 0xFF;
    TPMSTtd = 0xFF;
    TPMSPdt = 0xFF;
    TPMSNode = 0;
    CntCANNode5En = 0;

    /* CANMGM */
    memset(VehBusOffRec, 0, sizeof(VehBusOffRec));
    memset(PriBusOffRec, 0, sizeof(PriBusOffRec));
    VehOptConfig = 0;
    FlgNoNetworkDiag = 1;
    BrakeSignalCAN = BRAKE_OFF;
    COBStartActive = FC_ELEAN_IDLE;
    ValidCANDiagAx1 = NO_ERROR;
    ValidCANDiagAx2 = NO_ERROR;
    ValidCANDiagAx3 = NO_ERROR;
    FrmLostDashboard = 0;
    FrmLostDBInfo = 0;
    FrmLostEGear = 0;
    FrmLostMCU1 = 0;
    FrmLostLHB = 0;
    FrmLostRHB = 0;
    //BrakeSignal = 0;
    FlgYawRec = 0;
    ATroutineFlagCAN = 0;
    PTCanSt = 0;
    PRCanSt = 0;
    StCanSendEn = CANSEND_STOP;
    KeyStored = (EEECUUId != 0);
    CntNoDiagAfterKeyOn = TNOCANDIAGAFTKEYON;
    if (DISABLEIMMO == 0)
    {
        McuKLSReq = RRS_INIT;
        InjDisableIMMO = 0;
    }
    else if (DISABLEIMMO == 1)
    {
        McuKLSReq = RRS_ACK;
        InjDisableIMMO = 1;
    }
    else
    {
        McuKLSReq = RRS_INIT;
        InjDisableIMMO = 1;
    }
    
    if (EECANNode3En != 0)
    {
        SETBIT(VehOptConfig, 0);
    }
    else { /* MISRA */ }

    if (EECANNode2En != 0)
    {
        SETBIT(VehOptConfig, 1);
    }
    else { /* MISRA */ }

    if (EECANNode4En != 0)
    {
        SETBIT(VehOptConfig, 2);
    }
    else { /* MISRA */ }

    if (EECANNode5En != 0)
    {
        SETBIT(VehOptConfig, 3);
    }
    else { /* MISRA */ }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CANMGM_CanSend5ms(void)
{
    /* BusOff test and recovery */
    PTCanSt = CANMGM_BusOffTestAndRec(VEHICLE_CAN, DIAG_VEHICLE_CAN, &VehBusOffRec);
    PRCanSt = CANMGM_BusOffTestAndRec(PRIVATE_CAN, DIAG_PRIVATE_CAN, &PriBusOffRec);
    
    /* 5ms SEND */
    if ((StCanSendEn != CANSEND_STOP) && (PTCanSt != CAN_BUSOFF))
    {
        /* */
    }
    else { /* MISRA */ }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CANMGM_CanSend10ms(void)
{
    static uint8_T cntS20 = 0;
    static uint16_T cntWaitState = 0;

    /* CAN SEND MACHINE */
    switch (StCanSendEn)
    {
        case CANSEND_STOP:
        {
            if ((FlgVehCanEn != 0) && (KeySignal != 0))
            {
                if (cntWaitState >= CANSENDWAITSTART)
                {
                    StCanSendEn = CANSEND_START;
                    cntWaitState = 0;
                }
                else
                {
                    cntWaitState++;
                }
            }
            else
            {
                cntWaitState = 0;
            }
        }
        break;

        case CANSEND_WAITSTOP:
        {
            if ((FlgVehCanEn == 0) || (KeySignal == 0))
            {
                if (cntWaitState >= CANSENDWAITSTOP)
                {
                    StCanSendEn = CANSEND_STOP;
                    cntWaitState = 0;
                }
                else
                {
                    cntWaitState++;
                }
            }
            else
            {
                StCanSendEn = CANSEND_START;
                cntWaitState = 0;
            }
        }
        break;
        
        case CANSEND_START:
        {
            if ((FlgVehCanEn == 0) || (KeySignal == 0))
            {
                StCanSendEn = CANSEND_WAITSTOP;
                cntWaitState = 0;
            }
            else { /* MISRA */ }
        }
        break;
        
        default:
        {
            StCanSendEn = CANSEND_STOP;
            cntWaitState = 0;
        }
        break;
    }

    /* CAN RECV DIAG HYNIBIT MACHINE */
    if (FlgVehCanEn != 0)
    {
        if ((KeySignal != 0) && (EngineStartCmd == 0))
        {
            if (CntNoDiagAfterKeyOn > 0)
            {
                CntNoDiagAfterKeyOn--;
            }
            else { /* MISRA */ }
        }
        else
        {
            CntNoDiagAfterKeyOn = TNOCANDIAGAFTKEYON;
        }
    }
    else
    {
        CntNoDiagAfterKeyOn = TNOCANDIAGAFTKEYON;
    }

    /* 10ms SEND */
    CANMGM_CanSend_10ms();

    /* 20ms SEND */
    if (cntS20 >= 1)
    {
        CANMGM_CanSend_20ms();
        cntS20 = 0;
    }
    else
    {
        cntS20++;
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CANMGM_CanSend100ms(void)
{
    int16_T CanTxErr;

    /* 100ms SEND 500Kb */
    if ((StCanSendEn != CANSEND_STOP) && (PTCanSt != CAN_BUSOFF))
    {
        CANMGM_Send_ENGINE2();
        CANMGM_Send_ENGINE3();
        CANMGM_Send_ENGINE5();
        CANMGM_Send_ENGINE7();
        CANMGM_Send_eLean1();
        
        if (ATRoutineEnable == 1) // $31 $04 has been received
        {
            /*
            REQ_MV_EM2.0_AT_15:
            (...continue). At the same time, EM2.0 Application shall start sending VIN value currently saved in 
            EEPROM (VIN ECU) on CAN through message VIN_CODE_ECU (ID 0x264) for 10 seconds with a period of 100 msec 
            */
            if (EcuVIN_TxCnt < 100)
            {
                EcuVIN_TxCnt++;
                CANMGM_Send_VIN_CODE_ECU();
            }
            else if (EcuVIN_TxCnt == 100) // Ecu has finished to send VIN_CODE_ECU for 10s each 100ms
            {
                ATRoutineEnable = 0x00; // a new StartRoutine can be processed
                EcuVIN_TxCnt = 0x00;    // reset VIN_CODE_ECU message tx counter
            }
            else { /* MISRA */ }

            /*
            REQ_MV_EM2.0_AT_16:
            When routine Service $31, routineLocalIdentifier 0x04 is completed, Dashboard will set  ATroutineFlag bit of CAN message Dashboard (ID 0x20) start bit 35; lenght 1 to 1.
            As a consequence EM2.0 Application shall set Anti-Tampering Routine Enable Status (RLI 0xB9) to 0x02 Anti-Tampering Routine Result Status (RLI 0xBA) to 0x01. 
            */
            if (ATroutineFlagCAN == 1) // Dash signals that Routine has been executed
            {
                ATRoutineEnable = 0x00;        // a new StartRoutine can be processed
                ATRoutineDashVINCheck = 0x01;  // ECU can compare VIN rx with embedded one
                FlgWaitDashVINEchoReply = 1;
            }
            else { /* MISRA */ }
        }
        else { /* MISRA */ }
    }
    else { /* MISRA */ }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CANMGM_CanSendTDC(void)
{
    /* TDC SEND 500Kb */
    if ((StCanSendEn != CANSEND_STOP) && (PTCanSt != CAN_BUSOFF))
    {
        /* */
    }
    else { /* MISRA */ }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CANMGM_CanSendDwlReq(void)
{
    uint8_T status;
    uint64_T tim;
    
    FlgDownload = 1;
    CANMGM_Send_ENGINE3();
    TIMING_SetTimeout(500u, &tim);
    while (status != TIMEOUT_EXPIRED)
    {
        TIMING_GetTimeoutStatus(tim, &status);
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CANMGM_CanRecv5ms(void)
{
    uint8_T stDiag;
    int16_T CanRxErr;

    /* 5ms 500Kb RECEIVE */
    if (StDiag[DIAG_VEHICLE_CAN] != FAULT)
    {
        CANMGM_Recv_LAM(VEHICLE_CAN, LAM1_BUF, LAM1_BUF_QUELEN, 0);
        CANMGM_Recv_LAM(VEHICLE_CAN, LAM2_BUF, LAM2_BUF_QUELEN, 1);
        CANMGM_Recv_LAM(VEHICLE_CAN, LAM3_BUF, LAM3_BUF_QUELEN, 2);
        CANMGM_Recv_LAM(VEHICLE_CAN, LAM4_BUF, LAM4_BUF_QUELEN, 3);
    
        CanRxErr = CANMGM_Recv_ABS();
        if (EECANNode3En != 0)
        {
            stDiag = CANMGM_DiagMsg(DIAG_VEH_CAN_NODE_3, CanRxErr);
        }
        else
        {
            stDiag = NO_FAULT;
        }
        if ((stDiag == FAULT) || (KeySignal == 0))
        {
            CANMGM_Reset_ABS_Data();
        }
        else { /* MISRA */ }
    }
    else
    {
        CANMGM_Reset_ABS_Data();
    }
    CANMGM_Calc_BrakeSignal();

   
    
    /* 5ms 1Mb RECEIVE */
    if (StDiag[DIAG_PRIVATE_CAN] != FAULT)
    {
        CanRxErr = CANMGM_Recv_EGEAR_1M();
        stDiag = CANMGM_DiagMsg(DIAG_VEH_CAN_NODE_4, CanRxErr);
        if ((stDiag == FAULT) || (KeySignal == 0))
        {
            CANMGM_Reset_EGEAR_1M_Data();
        }
        else { /* MISRA */ }
    }
    else
    {
        CANMGM_Reset_EGEAR_1M_Data();
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CANMGM_CanRecv10ms(void)
{
    static uint8_T cntR20 = 0;
    static uint8_T cntR50 = 0;
    static uint8_T cntR100 = 0;

    /* 10ms 500Kb RECEIVE */
    CANMGM_CanRecv_10ms();

    /* 20ms 500Kb RECEIVE */
    if (cntR20 >= 1)
    {
        CANMGM_CanRecv_20ms();
        cntR20 = 0;
    }
    else
    {
        cntR20++;
    }

    /* 50ms RECEIVE */
    if (cntR50 >= 4)
    {
        CANMGM_CanRecv_50ms();
        cntR50 = 0;
    }
    else
    {
        cntR50++;
    }

    /* 100ms RECEIVE */
    if (cntR100 >= 9)
    {
        CANMGM_CanRecv_100ms();
        cntR100 = 0;
    }
    else
    {
        cntR100++;
    }

    CANMGM_KeyImmo();
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CANMGM_CanRecv100ms(void)
{
    static uint8_T cntR500 = 0;
    
    CANMGM_Recv_VIN_CODE_DASH();

    /* 500ms 500Kb RECEIVE */
    if (cntR500 >= 4)
    {
        CANMGM_CanRecv_500ms();
        cntR500 = 0;
    }
    else
    {
        cntR500++;
    }
}

/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_CanSend_10ms(void)
{
    /* 10ms SEND 500Kb */
    if ((StCanSendEn != CANSEND_STOP) && (PTCanSt != CAN_BUSOFF))
    {
        CANMGM_Send_ERRS();
    }
    else { /* MISRA */ }

    /* 10ms SEND 1Mb */
    if ((StCanSendEn != CANSEND_STOP) && (PRCanSt != CAN_BUSOFF))
    {
        CANMGM_Send_ENGINE4();
    }
    else { /* MISRA */ }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_CanSend_20ms(void)
{
    /* 20ms SEND 500Kb */
    if ((StCanSendEn != CANSEND_STOP) && (PTCanSt != CAN_BUSOFF))
    {
        CANMGM_Send_ENGINE1();
    }
    else { /* MISRA */ }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_CanRecv_10ms(void)
{
    uint8_T stDiag;
    int16_T CanRxErr;
    int16_T CanRxErr2;
    static uint8_T cntCOB = 0;

    /* 10ms 500Kb RECEIVE */
    if (StDiag[DIAG_VEHICLE_CAN] != FAULT)
    {
        CanRxErr = CANMGM_Recv_DASHBOARD();
        CanRxErr2 = CANMGM_Recv_DASH_INFO();
        if ((StDiag[DIAG_VEH_CAN_NODE_1] == FAULT) || (KeySignal == 0))
        {
            CANMGM_Reset_DASH_Data();
            CANMGM_Reset_DASH_INFO_Data();
        }
        else { /* MISRA */ }
        if (CanRxErr != 0)
        {
            if (CanRxErr != CAN_RX_SKIP)
            {
                CanRxErrDash = CanRxErr;
            }
            else { /* MISRA */ }
        }
        else { /* MISRA */ }
        if (CanRxErr2 != 0)
        {
            if (CanRxErr2 != CAN_RX_SKIP)
            {
                CanRxErrDash = CanRxErr2;
            }
            else { /* MISRA */ }
        }
        else { /* MISRA */ }
        if ((CanRxErrDash != 0) && (CanRxErrDash != CAN_RX_SKIP))
        {
            /* MISRA */
        }
        else if ((CanRxErrDash == CAN_RX_SKIP) && ((CanRxErr == 0) || (CanRxErr2 == 0)))
        {
            CanRxErrDash = 0;
        }
        else { /* MISRA */ }

        CANMGM_Recv_KLS_RRS();
        CANMGM_Recv_AXISPOSITION1();
        CANMGM_Recv_AXISPOSITION2();
        CANMGM_Recv_AXISPOSITION3();

        if (COBStartActive != 0)
        {
            cntCOB = 5; /* Skip Diag, Factory calib */
        }
        else if (cntCOB != 0)
        {
            cntCOB--;
        }
        else
        {
            if ((VDRollCAN != 0) && (CntNoDiagAfterKeyOn == 0) && (Rpm != 0))
            {
                DiagMgm_SetDiagNode2Validity(); /* DIAG_VEH_CAN_NODE_2 --> SIG_NOT_PLAUSIBLE */
            }
            else if ((ValidCANDiagAx1 != NO_ERROR) || (ValidCANDiagAx2 != NO_ERROR) || (ValidCANDiagAx3 != NO_ERROR))
            {
                DiagMgm_SetDiagState(DIAG_VEH_CAN_NODE_2, FAULT_CAN_BUFFER_EMPTY, &stDiag);
            }
            else if ((VDPitchCAN != 0) && (CntNoDiagAfterKeyOn == 0) && (Rpm != 0))
            {
                DiagMgm_SetDiagNode2Validity(); /* DIAG_VEH_CAN_NODE_2 --> SIG_NOT_PLAUSIBLE */
            }
            else
            {
                DiagMgm_SetDiagState(DIAG_VEH_CAN_NODE_2, NO_PT_FAULT, &stDiag);
            }
        }
    }
    else
    {
        CANMGM_Reset_DASH_Data();
        CANMGM_Reset_DASH_INFO_Data();
        CANMGM_Reset_AXISPOSITION1_Data();
        CANMGM_Reset_AXISPOSITION2_Data();
        CANMGM_Reset_AXISPOSITION3_Data();
    }

    if (StDiag[DIAG_VEHICLE_CAN] == FAULT)
    {
        FlgYawRec = 1;
    }
    else if (StDiag[DIAG_VEH_CAN_NODE_2] == FAULT)
    {
        if ((VDRollCAN != 0) || (FlgYawRec == 1))
        {
            FlgYawRec = 1;
        }
        else if (((VDPitchCAN != 0) || (FlgYawRec == 2)) && (PtFault[DIAG_VEH_CAN_NODE_2] != FAULT_CAN_BUFFER_EMPTY))
        {
            FlgYawRec = 2;
        }
        else
        {
            FlgYawRec = 1;
        }
    }
    else if (FlgYawRecFO != 0)
    {
        FlgYawRec = 1;
    }
    else
    {
        if ((StDiag[DIAG_VEHICLE_CAN] == NO_FAULT) && (StDiag[DIAG_VEH_CAN_NODE_2] == NO_FAULT))
        {
            FlgYawRec = 0;
        }
        else
        {
            /* STUCK VALUE */
        }
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_CanRecv_20ms(void)
{
    uint8_T stDiag;
    int16_T CanRxErr;

    /* 20ms 500Kb RECEIVE */
    if (StDiag[DIAG_VEHICLE_CAN] != FAULT)
    {
        CanRxErr = CANMGM_Recv_LEFT_HB();
        stDiag = CANMGM_DiagMsg(DIAG_VEH_CAN_NODE_6, CanRxErr);
        if ((stDiag == FAULT) || (KeySignal == 0))
        {
            CANMGM_Reset_LHB_Data();
        }
        else { /* MISRA */ }

        CanRxErr = CANMGM_Recv_RIGHT_HB();
        stDiag = CANMGM_DiagMsg(DIAG_VEH_CAN_NODE_7, CanRxErr);
        if ((stDiag == FAULT) || (KeySignal == 0))
        {
            CANMGM_Reset_RHB_Data();
        }
        else { /* MISRA */ }

        CanRxErr = CANMGM_Recv_MCU1();
        stDiag = CANMGM_DiagMsg(DIAG_VEH_CAN_NODE_8, CanRxErr);
        if ((stDiag == FAULT) || (KeySignal == 0))
        {
            CANMGM_Reset_MCU1_Data();
        }
        else { /* MISRA */ }

        stDiag = CANMGM_DiagMsg(DIAG_VEH_CAN_NODE_1, CanRxErrDash);
        CanRxErrDash = CAN_RX_SKIP;
    }
    else
    {
        CANMGM_Reset_LHB_Data();
        CANMGM_Reset_RHB_Data();
        CANMGM_Reset_MCU1_Data();
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_CanRecv_50ms(void)
{
    uint8_T stDiag;
    int16_T CanRxErr;
    int16_T CanRxErr2;

    /* 50ms 500Kb RECEIVE */
    if (StDiag[DIAG_VEHICLE_CAN] != FAULT)
    {
        CanRxErr = CANMGM_Recv_GPS_02();
        CanRxErr2 = CANMGM_Recv_GPS_04();
        if ((StDiag[DIAG_VEH_CAN_NODE_9] == FAULT) || (KeySignal == 0))
        {
            CANMGM_Reset_GPS_02_Data();
            CANMGM_Reset_GPS_04_Data();
        }
        else { /* MISRA */ }
        if (CanRxErr != 0)
        {
            if (CanRxErr != CAN_RX_SKIP)
            {
                CanRxErrGPS = CanRxErr;
            }
            else { /* MISRA */ }
        }
        else { /* MISRA */ }
        if (CanRxErr2 != 0)
        {
            if (CanRxErr2 != CAN_RX_SKIP)
            {
                CanRxErrGPS = CanRxErr2;
            }
            else { /* MISRA */ }
        }
        else { /* MISRA */ }
        if ((CanRxErrGPS != 0) && (CanRxErrGPS != CAN_RX_SKIP))
        {
            /* MISRA */
        }
        else if ((CanRxErrGPS == CAN_RX_SKIP) && ((CanRxErr == 0) || (CanRxErr2 == 0)))
        {
            CanRxErrGPS = 0;
        }
        else { /* MISRA */ }
    }
    else
    {
        CANMGM_Reset_GPS_02_Data();
        CANMGM_Reset_GPS_04_Data();
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_CanRecv_100ms(void)
{
    uint8_T stDiag;

    /* 100ms 500Kb RECEIVE */
    if (StDiag[DIAG_VEHICLE_CAN] != FAULT)
    {
        stDiag = CANMGM_DiagMsg(DIAG_VEH_CAN_NODE_9, CanRxErrGPS);
        CanRxErrGPS = CAN_RX_SKIP;
    }
    else
    {
        /* */
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_CanRecv_500ms(void)
{
    uint8_T stDiag;
    int16_T CanRxErr;

    /* 500ms 500Kb RECEIVE */
    if (StDiag[DIAG_VEHICLE_CAN] != FAULT)
    {
        CanRxErr = CANMGM_Recv_TPMS();
        if (EECANNode5En != 0)
        {
            stDiag = CANMGM_DiagMsg(DIAG_VEH_CAN_NODE_5, CanRxErr);
        }
        else
        {
            stDiag = NO_FAULT;
        }
        if ((stDiag == FAULT) || (KeySignal == 0))
        {
            CANMGM_Reset_TPMS_Data();
        }
        else { /* MISRA */ }
    }
    else
    {
        CANMGM_Reset_TPMS_Data();
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Send_ENGINE1(void)
{
    ENG1_T *ENG1 = (ENG1_T *)&Gen10[0];
    ENG1_T *intSort = (ENG1_T *)&InvS10[0];
    uint8_T atREn;
    uint8_T mil;
    uint8_T warning;
    uint8_T engOvrLimit;
    uint16_T tmpRpm;
    int32_T tmpWorker;
    int32_T tmpWorker2;
    static uint8_T CntCreepBlink = 0;
    static uint8_T ENG1AliveCounter = 0;

    /*********************/
    /*  CALC PARAMETERS  */
    /*********************/
    // Gear
    CANMGM_Calc_GearPos(&GearPosDashboard, GearPos);

    // Rpm
    if (SELRPMCAN == 0)
    {
        tmpRpm = Rpm;
    }
    else if (SELRPMCAN == 1)
    {
        tmpRpm = RpmF;
    }
    else
    {
        tmpRpm = RpmF2;
    }

    // TC
    if ((StTracCtrl == TC_SMOOTH) || (StTracCtrl == TC_ACC))
    {
        TracCtrlAct = 1;
    }
    else
    {
        TracCtrlAct = 0;
    }
    if (FlgTracCtrl != 0)
    {
        FlgTracCtrlEn = 1;
    }
    else
    {
        FlgTracCtrlEn = 0;
    }

    // Warning Lamp
    if (LOGLAMPOUT[0] < NUMPHYSLMP)
    {
        warning = PhysLampOut[LOGLAMPOUT[0]];
    }
    else
    { 
        warning = 1;
    }

    // MIL Lamp
    if (LOGLAMPOUT[2] < NUMPHYSLMP)
    {
        mil = PhysLampOut[LOGLAMPOUT[2]];
    }
    else 
    { 
        mil = 1;
    }

    // Anti tampering
    if (ATEnable == 1)
    {
        atREn = ATRoutineEnable;
    }
    else
    {
        atREn = 0;
    }

    // Limiter Lamp
    if (FlgCmiLimit != 0)
    {
        if (IdxRpmLCutOff != 0)
        {
            engOvrLimit = 3;
        }
        else if ((StRpmLimiter == CMI_FILTER_LIM) || (StRpmLimiter == SMOOTH_LIM))
        {
            engOvrLimit = 1;
        }
        else
        {
            engOvrLimit = 2;
        }
    }
    else if ((StCreepLim == ST_CREEPING) || (StCreepLim == ST_RETURN))
    {
        if (CntCreepBlink <= TIMCRPFREQ)
        {
            engOvrLimit = 1;
            CntCreepBlink++;
        }
        else if (CntCreepBlink > (2 * TIMCRPFREQ))
        {
            engOvrLimit = 0;
            CntCreepBlink = 0u;
        }
        else
        {
            engOvrLimit = 0;
            CntCreepBlink++;
        }
    }
    else
    {
        engOvrLimit = 0;
    }

    /*********************/
    /*  CLEAR STRUCTURE  */
    /*********************/
    ENG1->Byte0.R = 0;
    ENG1->Byte1.R = 0;
    ENG1->Byte2.R = 0;
    ENG1->Byte3.R = 0;
    ENG1->Byte4.R = 0;
    ENG1->Byte5.R = 0;
    ENG1->Byte6.R = 0;
    ENG1->Byte7.R = 0;

    /*********************/
    /*  SET PARAMETERS   */
    /*********************/
    ENG1->Byte0.B.GearEngaged0 = GearPosDashboard;
    tmpWorker2 = VehSpeed;
    tmpWorker2 = max(VEHSPEED_CAN_OFFSET, tmpWorker2);
    tmpWorker2 = tmpWorker2 - VEHSPEED_CAN_OFFSET;
    tmpWorker2 = (tmpWorker2 >> VEHSPEED_CAN_GAIN);
    tmpWorker = min(M12_11_00, tmpWorker2);
    ENG1->Byte0.B.VehicleSpeed0 = (uint8_T)((tmpWorker & M04_11_08) >> 8);
    
    ENG1->Byte1.B.VehicleSpeed1 = (uint8_T)((tmpWorker & M08_07_00) << 0);

    tmpWorker2 = tmpRpm;
    tmpWorker2 = max(ENGINESPEED_CAN_OFFSET, tmpWorker2);
    tmpWorker2 = tmpWorker2 - ENGINESPEED_CAN_OFFSET;
    tmpWorker2 = (tmpWorker2 >> ENGINESPEED_CAN_GAIN);
    tmpWorker = min(M16_15_00, tmpWorker2);
    ENG1->Byte3.B.EngineSpeed3 = (uint8_T)((tmpWorker & M08_15_08) >> 8);

    ENG1->Byte4.B.EngineSpeed4 = (uint8_T)((tmpWorker & M08_07_00) << 0);

    ENG1->Byte5.B.StarterMotor5 = EngineStartCmd;
    ENG1->Byte5.B.SideStandSts5 = TrestleSignal;
    ENG1->Byte5.B.KitMap5 = KitMapSel;
    ENG1->Byte5.B.FuelPumpCmd5 = LoadCmd;
    ENG1->Byte5.B.MILLampSts5 = mil;
    if (((ENCNTWRONG >> 1) & 0x01) == 0)
    {
        ENG1->Byte5.B.E1AliveCounter5 = ENG1AliveCounter;
    }
    else
    {
        ENG1->Byte5.B.E1AliveCounter5 = VtCntWrong[(ENG1AliveCounter & 0x07)];
    }

    ENG1->Byte6.B.GearEngagedValidData6 = (uint8_T)(StDiag[DIAG_GEAR_SENSOR] == FAULT);
    ENG1->Byte6.B.EngineSpeedValidData6 = (uint8_T)(StDiag[DIAG_RPM_SENS] == FAULT);
    ENG1->Byte6.B.FLCCtrlAct6 = (StSatAw != ST_SAT_MAX);
    ENG1->Byte6.B.VehicleSpeedValidData6 = (uint8_T)(StDiag[DIAG_VEHSPEED] == FAULT);
    ENG1->Byte6.B.WarningLampSts6 = warning;
    ENG1->Byte6.B.EngineOverspeedLimit6 = engOvrLimit;
    ENG1->Byte6.B.ATroutineEnable6 = atREn;

    ENG1->Byte7.B.TracCtrlAct7 = TracCtrlAct;
    ENG1->Byte7.B.ENRIDINGMODE_3_7 = ENRIDINGMODE[RM_CUSTOM];
    ENG1->Byte7.B.FlgTracCtrl7 = FlgTracCtrlEn;
    ENG1->Byte7.B.RidingModeTimeOut7 = RidingModeTOut;
    ENG1->Byte7.B.HeatedGripPresence = HeatedGripSt; //niso added.
    ENG1->Byte7.B.RidingMode7 = RidingModeCAN;

    /*********************/
    /*  SET CRC          */
    /*********************/
    intSort->Byte0.R = ENG1->Byte0.R;
    intSort->Byte1.R = ENG1->Byte1.R;
    intSort->Byte2.R = ENG1->Byte3.R;
    intSort->Byte3.R = ENG1->Byte4.R;
    intSort->Byte4.R = ENG1->Byte5.R;
    intSort->Byte5.R = ENG1->Byte6.R;
    intSort->Byte6.R = ENG1->Byte7.R;
    intSort->Byte7.R = 0;
    
    ENG1->Byte2.B.E1Integrity2 = (CRC8_SAE_J1850_bit(0u, (uint8_T *) intSort, 7u) + ((ENCRCWRONG >> 1) & 0x01));
    ENG1AliveCounter++;

    /*********************/
    /*  SEND DATA        */
    /*********************/
    CAN_TxData(VEHICLE_CAN, ENGINE1_BUF, (uint8_T *) ENG1);
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Send_ENGINE2(void)
{
    ENG2_T *ENG2 = (ENG2_T *)&Gen100[0];
    ENG2_T *intSort = (ENG2_T *)&InvS100[0];
    int32_T tmpWorker;
    int32_T tmpWorker2;
    static uint8_T CntCreepBlink = 0;
    static uint8_T ENG2AliveCounter = 0;

    /*********************/
    /*  CALC PARAMETERS  */
    /*********************/
    // CC
    if (TrigEnTC2WZeroCC != 0)
    {
        FdbVehSpeedCtrlCAN = 2u; /* DISABLE */
    }
    else
    {
        switch (StVehSpeedCtrl)
        {
            case ST_VSC_NORMAL_CL:
            case ST_VSC_NORMAL_CL_CTF:
            {
                FdbVehSpeedCtrlCAN = 0u; /* ACTIVE */
            }
            break;
            
            case ST_VSC_PAUSED:
            {
                FdbVehSpeedCtrlCAN = 1u; /* PAUSE */
            }
            break;

            case ST_VSC_UNUSED:
            case ST_VSC_DISABLED:
            {
                FdbVehSpeedCtrlCAN = 2u; /* DISABLE */
            }
            break;

            case ST_VSC_NORMAL_WAIT:
            case ST_VSC_RESET:
            {
                FdbVehSpeedCtrlCAN = 3u; /* ENABLE */
            }
            break;

            case ST_VSC_ERROR:
            {
                FdbVehSpeedCtrlCAN = 4u; /* RECOVERY */
            }
            break;
            
            default:
            {
                FdbVehSpeedCtrlCAN = 2u; /* DISABLE */
            }
            break;
        }
    }

    /*********************/
    /*  CLEAR STRUCTURE  */
    /*********************/
    ENG2->Byte0.R = 0;
    ENG2->Byte1.R = 0;
    ENG2->Byte2.R = 0;
    ENG2->Byte3.R = 0;
    ENG2->Byte4.R = 0;
    ENG2->Byte5.R = 0;
    ENG2->Byte6.R = 0;
    ENG2->Byte7.R = 0;

    /*********************/
    /*  SET PARAMETERS   */
    /*********************/
    tmpWorker2 = TWater;
    tmpWorker2 = max(WATERTEMP_CAN_OFFSET, tmpWorker2);
    tmpWorker2 = tmpWorker2 - WATERTEMP_CAN_OFFSET;
    tmpWorker2 = (tmpWorker2 >> WATERTEMP_CAN_GAIN);
    tmpWorker = min(M08_07_00, tmpWorker2);
    ENG2->Byte0.B.WaterTemperature0 = (uint8_T)((tmpWorker & M08_07_00) << 0);  
    if (((ENCNTWRONG >> 2) & 0x01) == 0)
    {
        ENG2->Byte2.B.E2AliveCounter2 = ENG2AliveCounter;
    }
    else
    {
        ENG2->Byte2.B.E2AliveCounter2 = VtCntWrong[(ENG2AliveCounter & 0x03)];
    }
    ENG2->Byte2.B.OilPressureStatus2 = (OilPresSignal == 0);
    ENG2->Byte2.B.AMTPresence2 = AMTPresence;
    tmpWorker2 = QFuelLth;
    tmpWorker2 = max(QFUELLIT_CAN_OFFSET, tmpWorker2);
    tmpWorker2 = tmpWorker2 - QFUELLIT_CAN_OFFSET;
    tmpWorker2 = (tmpWorker2 >> QFUELLIT_CAN_GAIN);
    tmpWorker = min(M10_09_00, tmpWorker2);
    ENG2->Byte2.B.FuelConsumption2 = (uint8_T)((tmpWorker & M02_09_08) >> 8);

    ENG2->Byte2.B.BrakeSignal = BrakeSignal; //niso added.

    ENG2->Byte2.B.ParkingBrakeFlag = ParkBrakeSignal; //niso added.

    ENG2->Byte3.B.FuelConsumption3 = (uint8_T)((tmpWorker & M08_07_00) << 0);

    ENG2->Byte4.B.WaterTemperatureValidData4 = (uint8_T)((StDiag[DIAG_T_WATER] == FAULT) || (StDiag[DIAG_T_WATER_2]==FAULT));
    ENG2->Byte4.B.LcStatus4 = StLc;
    ENG2->Byte4.B.FuelConsumptionValidData4 = VDQFuelLth;
    ENG2->Byte4.B.VehicleSpeedControllerStatus4 = FdbVehSpeedCtrlCAN;

    ENG2->Byte5.B.LcKm5 = EELcKm;
    ENG2->Byte5.B.SL_TC2W_Timeout5 = SlTcTimeout;
    ENG2->Byte5.B.SL_TC2W5 = (uint8_T)((SlTc2W & M03_02_00) >> 1);

    ENG2->Byte6.B.SL_TC2W6 = (uint8_T)(SlTc2W & M01_00_00);
    ENG2->Byte6.B.LcTrip6 = LcTrip;
    ENG2->Byte6.B.QuickShiftPresence6 = QuickShiftPresence;
    ENG2->Byte6.B.IDModelYear6 = IDMODELYEAR;

    ENG2->Byte7.B.IDModelV7 = IDMODELV;

    /*********************/
    /*  SET CRC          */
    /*********************/
    intSort->Byte0.R = ENG2->Byte0.R;
    intSort->Byte1.R = ENG2->Byte2.R;
    intSort->Byte2.R = ENG2->Byte3.R;
    intSort->Byte3.R = ENG2->Byte4.R;
    intSort->Byte4.R = ENG2->Byte5.R;
    intSort->Byte5.R = ENG2->Byte6.R;
    intSort->Byte6.R = ENG2->Byte7.R;
    intSort->Byte7.R = 0;
    
    ENG2->Byte1.B.E2Integrity1 = (CRC8_SAE_J1850_bit(0u, (uint8_T *) intSort, 7u) + ((ENCRCWRONG >> 2) & 0x01));
    ENG2AliveCounter++;
    ENG2AliveCounter = (ENG2AliveCounter & 0x03);

    /*********************/
    /*  SEND DATA        */
    /*********************/
    CAN_TxData(VEHICLE_CAN, ENGINE2_BUF, (uint8_T *) ENG2);
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Send_ENGINE3(void)
{
    ENG3_T *ENG3 = (ENG3_T *)&Gen100[0];
    uint8_T vin;
    uint8_T immo;
    static uint8_T ENG3AliveCounter = 0;

    /*********************/
    /*  CALC PARAMETERS  */
    /*********************/
    // Immo
    if (InjDisableIMMOFdbk != 0)
    {
        immo = 0; /* FDBK -> NO IMMO SUPPORTED OR IMMO ACK */
    }
    else
    {
        immo = (InjDisableIMMOCAN != 0); /* EXTERNAL IMMO MGM BY CAN */
    }

    #ifdef _BUILD_ANTITAMPERING_
    /*
    REQ_MV_EM2.0_AT_8:
    If Injection VIN Enable Status is 0x01, EM2.0 Application shall set bit VINerror (Msg Engine 3, ID:0x210, start bit 23; length 1) to 0.
    
    REQ_MV_EM2.0_AT_9:
    If Injection VIN Enable Status is 0x02 , EM2.0 Application shall set bit VINerror (Msg Engine 3, ID:0x210, start bit 23; length 1) to 1.
    Vehicle Dashboard will use bit VINerror, when set to 1, to communicate a warning message to the user
    */
    if (ATEnable == 1)
    {
        if (ECUVINLOCK == 0)
        {
            vin = 0;
        }
        else if (InjVINEnable == 1u)
        {
            vin = 0;
        }
        else if (InjVINEnable == 2u)
        {
            vin = 1;
        }
        else
        {
            vin = 0;
        }
    }
    else
    {
        vin = 0;
    }

    /*
    REQ_MV_EM2.0_AT_10:
    When VINerror bit  value is changed from 0 to 1, Injection VIN Failure Counter (RLI 0xBC) shall be incremented.
    */
    if (vin != 0u)
    {
        AntiTampering_UpdateVINFailCnt();
    }
    #endif

    /*********************/
    /*  CLEAR STRUCTURE  */
    /*********************/
    ENG3->Byte0.R = 0;
    ENG3->Byte1.R = 0;
    ENG3->Byte2.R = 0;
    ENG3->Byte3.R = 0;
    ENG3->Byte4.R = 0;
    ENG3->Byte5.R = 0;
    ENG3->Byte6.R = 0;
    ENG3->Byte7.R = 0;

    /*********************/
    /*  SET PARAMETERS   */
    /*********************/
    ENG3->Byte0.B.airerror0 = (uint8_T)((StDiag[DIAG_V_MAPSIGNAL] == FAULT) || (StDiag[DIAG_PRES_NOT_COH] == FAULT) || (StDiag[DIAG_HOLE_MANIFOLD] == FAULT));
    ENG3->Byte0.B.phaseerror0 = (uint8_T)(StDiag[DIAG_CAMLEVEL] == FAULT);
    ENG3->Byte0.B.airtemperror0 = (uint8_T)(StDiag[DIAG_T_AIR] == FAULT);
    ENG3->Byte0.B.wattemperror0 = (uint8_T)((StDiag[DIAG_T_WATER] == FAULT) || (StDiag[DIAG_T_WATER_2] == FAULT));
    ENG3->Byte0.B.throttleerror0 = (uint8_T)((StDiag[DIAG_VANGTHR_1] == FAULT) || (StDiag[DIAG_VANGTHR_2] == FAULT) || (StDiag[DIAG_COH_VANGTHR] == FAULT));
    ENG3->Byte0.B.lambdasenserror0 = (uint8_T)((StDiag[DIAG_LSD_HLAMBDA] == FAULT) || (StDiag[DIAG_LAM_EL] == FAULT) || (StDiag[DIAG_LAM_FUNC] == FAULT) || (StDiag[DIAG_O2H_FUNC] == FAULT) || (StDiag[DIAG_O2_SLOW_L2R] == FAULT) || (StDiag[DIAG_O2_SLOW_R2L] == FAULT));
    ENG3->Byte0.B.exhvalveerror0 = (uint8_T)((StDiag[DIAG_HBRIDGE_B] == FAULT) || (StDiag[DIAG_EXHVALVEFDBK] == FAULT) || (StDiag[DIAG_EXHVALVPOS] == FAULT));
    ENG3->Byte0.B.egearerror0 = (uint8_T)(StDiag[DIAG_GEAR_SENSOR] == FAULT);

    ENG3->Byte1.B.injlowerror1 = (uint8_T)((StDiag[DIAG_INJECTOR_0] == FAULT) || (StDiag[DIAG_INJECTOR_1] == FAULT) || (StDiag[DIAG_INJECTOR_2] == FAULT) || (StDiag[DIAG_INJECTOR_3] == FAULT));
    ENG3->Byte1.B.injuperror1 = (uint8_T)((StDiag[DIAG_INJECTOR_4] == FAULT) || (StDiag[DIAG_INJECTOR_5] == FAULT) || (StDiag[DIAG_INJECTOR_6] == FAULT) || (StDiag[DIAG_INJECTOR_7] == FAULT));

    ENG3->Byte2.B.gaserror2 = (uint8_T)((StDiag[DIAG_VGASPOS_1] == FAULT) || (StDiag[DIAG_VGASPOS_2] == FAULT) || (StDiag[DIAG_COH_VGASPOS] == FAULT) || (StDiag[DIAG_VGASSWITCH] == FAULT) || (StDiag[DIAG_VGASSWCOH] == FAULT));
    ENG3->Byte2.B.vrserror2 = (uint8_T)(StDiag[DIAG_RPM_SENS] == FAULT);
    ENG3->Byte2.B.igncoilerror2 = (uint8_T)((StDiag[DIAG_COIL_0] == FAULT) || (StDiag[DIAG_COIL_1] == FAULT) || (StDiag[DIAG_COIL_2] == FAULT) || (StDiag[DIAG_COIL_3] == FAULT));
    ENG3->Byte2.B.lambdasens2error2 = (uint8_T)((StDiag[DIAG_LSD_HLAMBDA_2] == FAULT) || (StDiag[DIAG_LAM2_EL] == FAULT) || (StDiag[DIAG_LAM_FUNC_2] == FAULT) || (StDiag[DIAG_O2H_FUNC_2] == FAULT));
    ENG3->Byte2.B.caterror2 = (uint8_T)(StDiag[DIAG_CAT] == FAULT);
    ENG3->Byte2.B.sparkpeakerror2 = (uint8_T)((StDiag[DIAG_VSPARKPEAK_0] == FAULT) || (StDiag[DIAG_VSPARKPEAK_1] == FAULT) || (StDiag[DIAG_VSPARKPEAK_2] == FAULT) || (StDiag[DIAG_VSPARKPEAK_3] == FAULT));

    ENG3->Byte3.B.vehspeederror3 = (uint8_T)((StDiag[DIAG_VEHSPEED] == FAULT) || (StDiag[DIAG_VEHSPEED_FRONT] == FAULT));
    ENG3->Byte3.B.vregerror3 = (uint8_T)(StDiag[DIAG_V_REG] == FAULT);
    ENG3->Byte3.B.eleannodeerror3 = (uint8_T)(StDiag[DIAG_VEH_CAN_NODE_2] == FAULT);
    ENG3->Byte3.B.ecuerror3 = (uint8_T)((StDiag[DIAG_SENS_VBAT] == FAULT) || (StDiag[DIAG_ADC] == FAULT) || (StDiag[DIAG_DIS_LOADS] == FAULT) || (StDiag[DIAG_TSD_TLE6244] == FAULT) ||
                                       (StDiag[DIAG_FLASH] == FAULT) || (StDiag[DIAG_EEPROM] == FAULT) || (StDiag[DIAG_CPU] == FAULT) || (StDiag[DIAG_RAM] == FAULT));
    ENG3->Byte3.B.knockcoherror3 = (uint8_T)((StDiag[DIAG_KNOCK_COH_0] == FAULT) || (StDiag[DIAG_KNOCK_COH_1] == FAULT) || (StDiag[DIAG_KNOCK_COH_2] == FAULT) || (StDiag[DIAG_KNOCK_COH_3] == FAULT));
    ENG3->Byte3.B.dbwerror3 = (uint8_T)((StDiag[DIAG_HBRIDGE_A] == FAULT) || (StDiag[DIAG_DBW_CONTROL] == FAULT));
    ENG3->Byte3.B.senssupplyerror3 = (uint8_T)((StDiag[DIAG_VSENS1] == FAULT) || (StDiag[DIAG_VSENS2] == FAULT));
    ENG3->Byte3.B.clutcherror3 = (uint8_T)(StDiag[DIAG_CLUTCH] == FAULT);

    ENG3->Byte4.B.safetyerror4 = (uint8_T)((StDiag[DIAG_SAFETY_3] == FAULT) || (StDiag[DIAG_SAFETY_2] == FAULT));
    ENG3->Byte4.B.ioncherror4 = (uint8_T)(StDiag[DIAG_ION_CH_A] == FAULT);
    ENG3->Byte4.B.tipovererror4 = (uint8_T)(StDiag[DIAG_TIP_OVER] == FAULT);
    ENG3->Byte4.B.gearsenserror4 = (uint8_T)(StDiag[DIAG_GEAR_SENSOR] == FAULT);
    ENG3->Byte4.B.absnodeerror4 = (uint8_T)(StDiag[DIAG_VEH_CAN_NODE_3] == FAULT);
    ENG3->Byte4.B.vehcanerror4 = (uint8_T)(StDiag[DIAG_VEHICLE_CAN] == FAULT);
    ENG3->Byte4.B.licplateerror4 = (uint8_T)(StDiag[DIAG_REAR_POS] == FAULT);
    ENG3->Byte4.B.dashnodeerror4 = (uint8_T)(StDiag[DIAG_VEH_CAN_NODE_1] == FAULT);

    ENG3->Byte5.B.VINerror5 = (uint8_T)((StDiag[DIAG_VIN_CODE] == FAULT) || (vin != 0));
    ENG3->Byte5.B.QSerror5 = (uint8_T)(StDiag[DIAG_QSHIFT_ANALOG] == FAULT);
    ENG3->Byte5.B.misfireerror5 = (uint8_T)(StDiag[DIAG_MISF_RND] == FAULT);
    ENG3->Byte5.B.egearnoderror5 = (uint8_T)(StDiag[DIAG_VEH_CAN_NODE_4] == FAULT);
    ENG3->Byte5.B.mcunodeerror5 = (uint8_T)(StDiag[DIAG_VEH_CAN_NODE_8] == FAULT);
    ENG3->Byte5.B.tpmsnoderror5 = (uint8_T)(StDiag[DIAG_VEH_CAN_NODE_5] == FAULT);
    ENG3->Byte5.B.lhbnodeerror5 = (uint8_T)(StDiag[DIAG_VEH_CAN_NODE_6] == FAULT);
    ENG3->Byte5.B.rhbnodeerror5 = (uint8_T)(StDiag[DIAG_VEH_CAN_NODE_7] == FAULT);

    ENG3->Byte6.B.ptraincanerror6 = (uint8_T)(StDiag[DIAG_PRIVATE_CAN] == FAULT);
    ENG3->Byte6.B.gpsnodeerror6 = (uint8_T)(StDiag[DIAG_VEH_CAN_NODE_9] == FAULT);
    ENG3->Byte6.B.Download6 = FlgDownload;
    ENG3->Byte6.B.Immoflg6 = immo;
    if (((ENCNTWRONG >> 3) & 0x01) == 0)
    {
        ENG3->Byte6.B.E3AliveCounter6 = ENG3AliveCounter;
    }
    else
    {
        ENG3->Byte6.B.E3AliveCounter6 = VtCntWrong[(ENG3AliveCounter & 0x07)];
    }

    /*********************/
    /*  SET CRC          */
    /*********************/
    ENG3->Byte7.B.E3Integrity7 = (CRC8_SAE_J1850_bit(0u, (uint8_T *) ENG3, 7u) + ((ENCRCWRONG >> 3) & 0x01));
    ENG3AliveCounter++;
    ENG3AliveCounter = (ENG3AliveCounter & 0x07);

    /*********************/
    /*  SEND DATA        */
    /*********************/
    CAN_TxData(VEHICLE_CAN, ENGINE3_BUF, (uint8_T *) ENG3);
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Send_ENGINE4(void)
{
    ENG4_T *ENG4 = (ENG4_T *)&Gen10[0];
    int32_T tmpWorker;
    int32_T tmpWorker2;

    /*********************/
    /*  CALC PARAMETERS  */
    /*********************/
    /* */

    /*********************/
    /*  CLEAR STRUCTURE  */
    /*********************/
    ENG4->Byte0.R = 0;
    ENG4->Byte1.R = 0;
    ENG4->Byte2.R = 0;
    ENG4->Byte3.R = 0;
    ENG4->Byte4.R = 0;
    ENG4->Byte5.R = 0;
    ENG4->Byte6.R = 0;
    ENG4->Byte7.R = 0;

    /*********************/
    /*  SET PARAMETERS   */
    /*********************/
    tmpWorker2 = CmeEst;
    tmpWorker2 = max(CMEEST_CAN_MIN, tmpWorker2);
    tmpWorker2 = tmpWorker2 - CMEEST_CAN_OFFSET;
    tmpWorker2 = (tmpWorker2 >> CMEEST_CAN_GAIN);
    tmpWorker = min(M16_15_00, tmpWorker2);
    ENG4->Byte0.B.Effective_Torque0 = (uint8_T)((tmpWorker & M08_15_08) >> 8);

    ENG4->Byte1.B.Effective_Torque1 = (uint8_T)((tmpWorker & M08_07_00) << 0);

    tmpWorker2 = GasPosCC;
    tmpWorker2 = max(GASPOSCC_CAN_OFFSET, tmpWorker2);
    tmpWorker2 = tmpWorker2 - GASPOSCC_CAN_OFFSET;
    tmpWorker2 = (tmpWorker2 >> GASPOSCC_CAN_GAIN);
    tmpWorker = min(M08_07_00, tmpWorker2);
    ENG4->Byte2.B.GasPos2 = tmpWorker;

    ENG4->Byte3.B.GearDWNEnable3 = FlgEnQuickShiftDn;
    ENG4->Byte3.B.GearUPEnable3 = FlgEnQuickShiftUp;
    ENG4->Byte3.B.GearNEnable3 = FlgEnQuickShiftN;
    ENG4->Byte3.B.StartSignal3 = StartSignalCAN;
    ENG4->Byte3.B.Cutoff3 = CutoffFlg;
    if (((ENCNTWRONG >> 4) & 0x01) == 0)
    {
        ENG4->Byte3.B.E4IDMux3 = IDMuxE4;
    }
    else
    {
        ENG4->Byte3.B.E4IDMux3 = VtCntWrong[(IDMuxE4 & 0x07)];
    }

    /* MUXED AREA */
    switch (IDMuxE4)
    {
        case 0:
        {
            /* */
        }
        break;

        case 1:
        {
            /* */
        }
        break;

        case 2:
        {
            /* */
        }
        break;

        case 3:
        {
            /* */
        }
        break;

        case 4:
        {
            /* */
        }
        break;

        case 5:
        {
            /* */
        }
        break;

        default:
        {
            /* NONE */
        }
        break;
    }

    /*********************/
    /*  SET CRC          */
    /*********************/
    ENG4->Byte7.B.E4Integrity7 = (CRC8_SAE_J1850_bit(0u, (uint8_T *) ENG4, 7u) + ((ENCRCWRONG >> 4) & 0x01));
    if (FOIDMUXE4 < 0)
    {
        if (IDMuxE4 >= 5)
        {
            IDMuxE4 = 0;
        }
        else
        {
            IDMuxE4++;
        }
    }
    else
    {
        IDMuxE4 = (uint8_T)FOIDMUXE4;
    }

    /*********************/
    /*  SEND DATA        */
    /*********************/
    CAN_TxData(PRIVATE_CAN, ENGINE4_BUF, (uint8_T *) ENG4);
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Send_ENGINE5(void)
{
    ENG5_T *ENG5 = (ENG4_T *)&Gen10[0];
    int32_T tmpWorker;
    int32_T tmpWorker2;

    /*********************/
    /*  CALC PARAMETERS  */
    /*********************/
    /* */

    /*********************/
    /*  CLEAR STRUCTURE  */
    /*********************/
    ENG5->Byte0.R = 0;
    ENG5->Byte1.R = 0;
    ENG5->Byte2.R = 0;
    ENG5->Byte3.R = 0;
    ENG5->Byte4.R = 0;
    ENG5->Byte5.R = 0;
    ENG5->Byte6.R = 0;
    ENG5->Byte7.R = 0;

    /*********************/
    /*  SET PARAMETERS   */
    /*********************/

    /* NORMAL AREA */
    ENG5->Byte6.B.NcmKL15_6 = KeyEnDiagSignal;
    
    if (((ENCNTWRONG >> 5) & 0x01) == 0)
    {
        ENG5->Byte6.B.E5IDMux6 = IDMuxE5;
    }
    else
    {
        ENG5->Byte6.B.E5IDMux6 = VtCntWrong[(IDMuxE5 & 0x03)];
    }
    
    /* MUXED AREA */
    switch (IDMuxE5)
    {
        case 0:
        {
            tmpWorker2 = MaxVSPCC;
            tmpWorker2 = max(VSPEEDCCSP_CAN_OFFSET, tmpWorker2);
            tmpWorker2 = tmpWorker2 - VSPEEDCCSP_CAN_OFFSET;
            tmpWorker2 = (tmpWorker2 >> VSPEEDCCSP_CAN_GAIN);
            tmpWorker = min(M08_07_00, tmpWorker2);
            ENG5->Byte0.B.M0.MaxVSPCC0 = (uint8_T)tmpWorker;

            tmpWorker2 = MinVSPCC;
            tmpWorker2 = max(VSPEEDCCSP_CAN_OFFSET, tmpWorker2);
            tmpWorker2 = tmpWorker2 - VSPEEDCCSP_CAN_OFFSET;
            tmpWorker2 = (tmpWorker2 >> VSPEEDCCSP_CAN_GAIN);
            tmpWorker = min(M08_07_00, tmpWorker2);
            ENG5->Byte1.B.M0.MinVSPCC1 = (uint8_T)tmpWorker;

            tmpWorker2 = MAXVSPLIM;
            tmpWorker2 = max(VSPEEDCCSP_CAN_OFFSET, tmpWorker2);
            tmpWorker2 = tmpWorker2 - VSPEEDCCSP_CAN_OFFSET;
            tmpWorker2 = (tmpWorker2 >> VSPEEDCCSP_CAN_GAIN);
            tmpWorker = min(M08_07_00, tmpWorker2);
            ENG5->Byte2.B.M0.MaxVSPLim2 = (uint8_T)tmpWorker;

            tmpWorker2 = MINVSPLIM;
            tmpWorker2 = max(VSPEEDCCSP_CAN_OFFSET, tmpWorker2);
            tmpWorker2 = tmpWorker2 - VSPEEDCCSP_CAN_OFFSET;
            tmpWorker2 = (tmpWorker2 >> VSPEEDCCSP_CAN_GAIN);
            tmpWorker = min(M08_07_00, tmpWorker2);
            ENG5->Byte3.B.M0.MinVSPLim3 = (uint8_T)tmpWorker;
        }
        break;

        case 1:
        {
            ENG5->Byte0.B.M1.AuxCfg0 = EECANNode5En;
            ENG5->Byte0.B.M1.DisEBK0 = (RidingMode == IDXRMDISESS);

            ENG5->Byte1.B.M1.EnTCMax01 = EnTcMaxLevel[0];
            ENG5->Byte1.B.M1.EnTCMax11 = EnTcMaxLevel[1];

            ENG5->Byte2.B.M1.EnTCMax22 = EnTcMaxLevel[2];
            ENG5->Byte2.B.M1.EnTCMax32 = EnTcMaxLevel[3];

            ENG5->Byte5.B.M1.EnTCMax45 = EnTcMaxLevel[4];
        }
        break;

        case 2:
        {
            tmpWorker2 = TAirCAN;
            tmpWorker2 = max(TAIRDASH_CAN_OFFSET, tmpWorker2);
            tmpWorker2 = tmpWorker2 - TAIRDASH_CAN_OFFSET;
            tmpWorker2 = (tmpWorker2 >> TAIRDASH_CAN_GAIN);
            tmpWorker = min(M08_07_00, tmpWorker2);
            ENG5->Byte0.B.M2.AirTempDash0 = (uint8_T)tmpWorker;

            ENG5->Byte1.B.M2.VDAirTempDash1 = VDTAirDash;
            ENG5->Byte1.B.M2.TransportLock1 = TransportLockEcu;
            ENG5->Byte1.B.M2.UnlockTime1 = (TempUnlockCntDown >> 3);

            ENG5->Byte2.B.M2.UnlockTime2 = (TempUnlockCntDown & M03_02_00);
        }
        break;

        case 3:
        {
            /* */
        }
        break;

        default:
        {
            /* NONE */
        }
        break;
    }

    ENG5->Byte5.B.M1.RiderPresence5 = RiderPresence; //niso added.

    ENG5->Byte6.B.EnHeatGripMaxLevel = EnHeatGripMaxLevel; //niso added.

    /*********************/
    /*  SET CRC          */
    /*********************/
    ENG5->Byte7.B.E5Integrity7 = (CRC8_SAE_J1850_bit(0u, (uint8_T *) ENG5, 7u) + ((ENCRCWRONG >> 5) & 0x01));
    if (FOIDMUXE5 < 0)
    {
        IDMuxE5++;
        IDMuxE5 = (IDMuxE5 & 0x03);
    }
    else
    {
        IDMuxE5 = (uint8_T)FOIDMUXE5;
    }

    /*********************/
    /*  SEND DATA        */
    /*********************/
    CAN_TxData(VEHICLE_CAN, ENGINE5_BUF, (uint8_T *) ENG5);
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Send_ENGINE7(void)
{
    ENG7_T *ENG7 = (ENG7_T *)&Gen100[0];
    ENG7_T *intSort = (ENG7_T *)&InvS100[0];
    int32_T tmpWorker;
    int32_T tmpWorker2;
    static uint8_T ENG7AliveCounter = 0;

    /*********************/
    /*  CALC PARAMETERS  */
    /*********************/
    /* */

    /*********************/
    /*  CLEAR STRUCTURE  */
    /*********************/
    ENG7->Byte0.R = 0;
    ENG7->Byte1.R = 0;
    ENG7->Byte2.R = 0;
    ENG7->Byte3.R = 0;
    ENG7->Byte4.R = 0;
    ENG7->Byte5.R = 0;
    ENG7->Byte6.R = 0;
    ENG7->Byte7.R = 0;

    VDVBattery = ((StDiag[DIAG_SENS_VBAT] == FAULT) /* || (StDiag[DIAG_V_REG] == FAULT)*/); //niso added.

    /*********************/
    /*  SET PARAMETERS   */
    /*********************/
    ENG7->Byte1.B.BatteryVoltage = (uint8_T) ((VBattery * 10) / 16);

    tmpWorker2 = TAir;
    tmpWorker2 = max(AIRTEMP_CAN_OFFSET, tmpWorker2);
    tmpWorker2 = tmpWorker2 - AIRTEMP_CAN_OFFSET;
    tmpWorker2 = (tmpWorker2 >> AIRTEMP_CAN_GAIN);
    tmpWorker = min(M08_07_00, tmpWorker2);
    ENG7->Byte2.B.AirTemperature2 = tmpWorker;

    tmpWorker2 = PresIntake;
    tmpWorker2 = max(PRESINTK_CAN_OFFSET, tmpWorker2);
    tmpWorker2 = tmpWorker2 - PRESINTK_CAN_OFFSET;
    tmpWorker2 = (tmpWorker2 >> PRESINTK_CAN_GAIN);
    tmpWorker = min(M08_07_00, tmpWorker2);
    ENG7->Byte3.B.PresIntake3 = tmpWorker;

    ENG7->Byte4.B.ClutchSignal4 = ClutchSignal;
    ENG7->Byte4.B.AirTemperatureValidData4 = (uint8_T)(StDiag[DIAG_T_AIR] == FAULT);
    tmpWorker2 = AngThrottle;
    tmpWorker2 = max(ANGTHR_CAN_OFFSET, tmpWorker2);
    tmpWorker2 = tmpWorker2 - ANGTHR_CAN_OFFSET;
    tmpWorker2 = (tmpWorker2 >> ANGTHR_CAN_GAIN);
    tmpWorker = min(M08_07_00, tmpWorker2);
    ENG7->Byte4.B.AngThrottle4 = (uint8_T)((tmpWorker & M07_07_01) >> 1);

    ENG7->Byte5.B.AngThrottle5 = (uint8_T)((tmpWorker & M01_00_00) << 0);
    tmpWorker2 = GasPos0;
    tmpWorker2 = max(GASPOS_CAN_OFFSET, tmpWorker2);
    tmpWorker2 = tmpWorker2 - GASPOS_CAN_OFFSET;
    tmpWorker2 = (tmpWorker2 >> GASPOS_CAN_GAIN);
    tmpWorker = min(M07_06_00, tmpWorker2);
    ENG7->Byte5.B.GasPos5 = tmpWorker;

    ENG7->Byte6.B.VDBatteryVoltage = VDVBattery; //niso added.



    ENG7->Byte6.B.EnAwMaxLevel6 = EnAwMaxLevel;
    ENG7->Byte6.B.EnLcMaxLevel6 = (EnLcMaxLevel >> 1);
    if (((ENCNTWRONG >> 7) & 0x01) == 0)
    {
        ENG7->Byte6.B.E7AliveCounter6 = ENG7AliveCounter;
    }
    else
    {
        ENG7->Byte6.B.E7AliveCounter6 = VtCntWrong[(ENG7AliveCounter & 0x07)];
    }

    ENG7->Byte7.B.EnLcMaxLevel7 = EnLcMaxLevel;
    ENG7->Byte7.B.ClutchSignal27 = ClutchSignal2;
    ENG7->Byte7.B.FanCmd7 = FanCoilCmd;
    ENG7->Byte7.B.EngineStarted7 = EndStartFlg;
    ENG7->Byte7.B.IDVersion7 = EEIDVersion;

    /*********************/
    /*  SET CRC          */
    /*********************/
    intSort->Byte0.R = ENG7->Byte1.R;
    intSort->Byte1.R = ENG7->Byte2.R;
    intSort->Byte2.R = ENG7->Byte3.R;
    intSort->Byte3.R = ENG7->Byte4.R;
    intSort->Byte4.R = ENG7->Byte5.R;
    intSort->Byte5.R = ENG7->Byte6.R;
    intSort->Byte6.R = ENG7->Byte7.R;
    intSort->Byte7.R = 0;
    
    ENG7->Byte0.B.E7Integrity0 = (CRC8_SAE_J1850_bit(0u, (uint8_T *) intSort, 7u) + ((ENCRCWRONG >> 7) & 0x01));
    ENG7AliveCounter++;
    ENG7AliveCounter = (ENG7AliveCounter & 0x07);

    /*********************/
    /*  SEND DATA        */
    /*********************/
    CAN_TxData(VEHICLE_CAN, ENGINE7_BUF, (uint8_T *) ENG7);
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Send_eLean1(void)
{
    ELR1_T *ELR1 = (ELR1_T *)&Gen100[0];
    static uint8_T eLeanSetup = 0;
    static uint16_T eLeanTO = 0;
    static uint8_T cnteZeroStop = 0;

    /*********************/
    /*  CALC PARAMETERS  */
    /*********************/
    switch (eLeanSetup)
    {
        case FC_ELEAN_IDLE:
        {
            if (Get_ELean_Calib_Status() == ROUTINE_IN_EXECUTION)
            {
                if (StDiag[DIAG_VEH_CAN_NODE_2] != FAULT)
                {
                    eLeanTO = 0;
                    eLeanSetup = FC_ELEAN_START;
                    COBStartActive = 1;
                }
                else
                {
                    Set_ELean_Calib_Status(ROUTINE_HALTED_TIMEOUT);
                    eLeanSetup = FC_ELEAN_FLT;
                    COBStartActive = 0;
                }
            }
            else
            {
                COBStartActive = 0;
            }
        }
        break;

        case FC_ELEAN_START:
        {
            if ((PitchRawCAN == 0xFFFF) && (RollRawCAN == 0xFFFF))
            {
                eLeanSetup = FC_ELEAN_WAIT;
            }
            else if (eLeanTO > FCELEANTO)
            {
                eLeanSetup = FC_ELEAN_IDLE;
                Set_ELean_Calib_Status(ROUTINE_HALTED_TIMEOUT);
                COBStartActive = 0;
            }
            else
            {
                eLeanTO++;
            }
        }
        break;

        case FC_ELEAN_WAIT:
        {
            if ((PitchRawCAN != 0xFFFF) && (RollRawCAN != 0xFFFF))
            {
                eLeanSetup = FC_ELEAN_IDLE;
                Set_ELean_Calib_Status(ROUTINE_COMPLETED);
                COBStartActive = 0;
            }
            else if (eLeanTO > FCELEANTO)
            {
                eLeanSetup = FC_ELEAN_IDLE;
                Set_ELean_Calib_Status(ROUTINE_HALTED_TIMEOUT);
                COBStartActive = 0;
            }
            else
            {
                eLeanTO++;
            }
        }
        break;

        case FC_ELEAN_FLT:
        {
            Set_ELean_Calib_Status(ROUTINE_HALTED_TIMEOUT);
            if (StDiag[DIAG_VEH_CAN_NODE_2] == NO_FAULT)
            {
                eLeanSetup = FC_ELEAN_IDLE;
                COBStartActive = 0;
            }
            else { /* MISRA */ }
        }
        break;

        default:
        {
            eLeanSetup = FC_ELEAN_IDLE;
            COBStartActive = 0;
        }
        break;
    }

    /*********************/
    /*  CLEAR STRUCTURE  */
    /*********************/
    ELR1->Byte0.R = 0;
    ELR1->Byte1.R = 0;
    ELR1->Byte2.R = 0;
    ELR1->Byte3.R = 0;
    ELR1->Byte4.R = 0;
    ELR1->Byte5.R = 0;
    ELR1->Byte6.R = 0;
    ELR1->Byte7.R = 0;

    /*********************/
    /*  SET PARAMETERS   */
    /*********************/
    ELR1->Byte0.B.C_OB_Start0 = COBStartActive;

    /*********************/
    /*  SET CRC          */
    /*********************/
    /* */

    /*********************/
    /*  SEND DATA        */
    /*********************/
    if ((eLeanSetup != FC_ELEAN_IDLE) && (eLeanSetup != FC_ELEAN_FLT))
    {
        cnteZeroStop = 25;
        CAN_TxData(VEHICLE_CAN , ELEAN1_BUF, (uint8_T *) ELR1);
    }
    else if (cnteZeroStop > 0)
    {
        cnteZeroStop--;
        CAN_TxData(VEHICLE_CAN , ELEAN1_BUF, (uint8_T *) ELR1);
    }
    else { /* MISRA */ }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Send_VIN_CODE_ECU(void)
{
    EVIN_T *EVIN = (EVIN_T *)&Gen100[0];
    static uint8_T seq_num;
    static uint8_T attempts;

    switch (seq_num)
    {
        case 0:
        {
            EVIN->Byte0.B.data0 = seq_num;
            EVIN->Byte1.B.data1 = EcuVIN[0];
            EVIN->Byte2.B.data2 = EcuVIN[1];
            EVIN->Byte3.B.data3 = EcuVIN[2];
            EVIN->Byte4.B.data4 = EcuVIN[3];
            EVIN->Byte5.B.data5 = EcuVIN[4];
            EVIN->Byte6.B.data6 = EcuVIN[5];
            EVIN->Byte7.B.data7 = EcuVIN[6];
        }
        break;

        case 1:
        {
            EVIN->Byte0.B.data0 = seq_num;
            EVIN->Byte1.B.data1 = EcuVIN[7];
            EVIN->Byte2.B.data2 = EcuVIN[8];
            EVIN->Byte3.B.data3 = EcuVIN[9];
            EVIN->Byte4.B.data4 = EcuVIN[10];
            EVIN->Byte5.B.data5 = EcuVIN[11];
            EVIN->Byte6.B.data6 = EcuVIN[12];
            EVIN->Byte7.B.data7 = EcuVIN[13];
        }
        break;

        case 2:
        {
            EVIN->Byte0.B.data0 = seq_num;
            EVIN->Byte1.B.data1 = EcuVIN[14];
            EVIN->Byte2.B.data2 = EcuVIN[15];
            EVIN->Byte3.B.data3 = EcuVIN[16];
            EVIN->Byte4.B.data4 = 0;
            EVIN->Byte5.B.data5 = 0;
            EVIN->Byte6.B.data6 = 0;
            EVIN->Byte7.B.data7 = 0;
        }
        break;

        default:
        {
            /* */
        }
        break;
    }

    seq_num++;
    if (seq_num == 3)
    {
        seq_num = 0;
    }
    else { /* MISRA */ }

    CAN_TxData(VEHICLE_CAN , VIN_CODE_ECU_BUF, (uint8_t *) EVIN);
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Send_ERRS(void)
{
    static uint8_T ERRSAliveCounter = 0;
    static uint8_T McuRRSRetry = 0;
    ERRS_T *ERRS = (ERRS_T *) &Gen10[0];
    int16_T retval = NO_ERROR;
    uint32_T a1;
    uint32_T b2;
    uint32_T c3;
    uint32_T d4;
    uint32_T tmpWorker;
    uint32_T tmpWorker2;

    if (McuKLSReq == RRS_REQ)
    {
        McuKLSReq = RRS_SIGN;
    
        /* RND GENERATION */
        EERRSCnt++;
        a1 = VSens1;
        b2 = ((VTAir & 0xFF) >> 6);
        b2 = b2 | (VTAir << 2);
        c3 = ((VBatteryIn & 0xFF) >> 4);
        c3 = c3 | (VBatteryIn << 4);
        d4 = ((TBoardTemperature & 0xFF) >> 2);
        d4 = d4 | (TBoardTemperature << 6);
        RndAnalogIn = (uint16_T)(a1 ^ b2 ^ c3 ^ d4);
        tmpWorker = (((RndAnalogIn ^  Odometer) * (MCU1Timestamp + 1)) >> 12) + EERRSCnt;
        tmpWorker2 = DASHTimestamp + EERRSCnt;
        a1 = ((uint32_T)(CRC8_J1850[(tmpWorker & 0xFF)]));
        b2 = ((uint32_T)(CRC8_J1850[((RndAnalogIn + EERRSCnt) & 0xFF)]));
        c3 = ((uint32_T)(CRC8_J1850[(tmpWorker2 & 0xFF)]));
        d4 = ((uint32_T)(CRC8_J1850[(MCU1Timestamp & 0xFF)]));
        KLSRnd = (d4 << 24) | (c3 << 16) | (b2 << 8) | a1;

        /* Clear CAN structure */
        ERRS->Byte0.R = 0;
        ERRS->Byte1.R = 0;
        ERRS->Byte2.R = 0;
        ERRS->Byte3.R = 0;
        ERRS->Byte4.R = 0;
        ERRS->Byte5.R = 0;
        ERRS->Byte6.R = 0;

        ERRS->Byte0.B.RRSRequest0 = 0x55;

        ERRS->Byte1.B.KLSRnd01 = (uint8_T)d4;

        ERRS->Byte2.B.KLSRnd02 = (uint8_T)c3;

        ERRS->Byte3.B.KLSRnd03 = (uint8_T)b2;

        ERRS->Byte4.B.KLSRnd04 = (uint8_T)a1;

        ERRS->Byte6.B.ERRSIdx6 = ECU_RRS_ID;
        
        ERRS->Byte6.B.ERRSAliveCounter6 = ERRSAliveCounter;
        ERRSAliveCounter++;
        if (ERRSAliveCounter > 15u)
        {
            ERRSAliveCounter = 0u;
        }
        else { /* MISRA */ }

        ERRS->Byte7.B.ERRSCheckSum7 = CRC8_SAE_J1850_bit(0u, (uint8_T *) ERRS, 7u);

        retval = CAN_TxData(VEHICLE_CAN, ERRS_BUF, (uint8_T *) ERRS);
    }
    else { /* MISRA */ }
    
    return retval;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_LAM(uint8_T can_ch, uint8_T lam_buf, uint8_T lam_buf_quelen, uint8_T ind_lam)
{
    struct CAN_buff *ptrDataBuf = NULL;     // pointer to message data structure
    struct CAN_buff *pDataBufNew = NULL;     // aux pointer
    int16_T CanRxErrtmp;
    int16_T canQueLen;
    uint32_T tmp;
    uint8_T endFifoFlag;

    NRxFrLAM[ind_lam] = 0;
    canQueLen =  lam_buf_quelen;

    CanRxErrtmp = CAN_RxData(can_ch, lam_buf, &ptrDataBuf);

    if ((CanRxErrtmp != CAN_RX_BUFFER_EMPTY) &&  (CanRxErrtmp != CAN_BUSOFF))
    {
        if (CanRxErrtmp == CAN_RX_BUFFER_OVERRUN)
        {
            /* */
        } 
        else // no errors  
        {
            /* */
        }
        ++NRxFrLAM[ind_lam];
        pDataBufNew = NULL; // new pointer
        endFifoFlag = 0;
        while ((endFifoFlag == 0) && (NRxFrLAM[ind_lam] < canQueLen))
        {
            if (CAN_RxData(can_ch, lam_buf, &pDataBufNew) == CAN_RX_BUFFER_EMPTY)
            {
                endFifoFlag = 1;
            }
            else
            {
                ++NRxFrLAM[ind_lam];
                ptrDataBuf = pDataBufNew; // save the previous pointer
            }
        }
        tmp = LAMCANGAIN[ind_lam] * COMPOSE_DATA16(ptrDataBuf->b[3],ptrDataBuf->b[2]);
        tmp = tmp >> (24 - 10);
        LamCAN[ind_lam] = (uint16_T) tmp + LAMCANOFS[ind_lam];
    }
    else if (CanRxErrtmp == CAN_RX_BUFFER_EMPTY) 
    {
        /* */
    }
    else { /* MISRA */ }
    return 0;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_KLS_RRS (void)
{
    KLS_RRS_T *KLS_RRS = (KLS_RRS_T *) &Gen100[0];
    int16_T retval;
    uint8_T canQueLen;
    uint32_T tmpWorker;
    uint8_T NRxFrWSInfo;                 
    uint8_T endFifoFlag;
    int16_T canRxErrtmp;
    uint8_T integrityIn = 0;
    uint8_T integrityChk = 0;
    struct CAN_buff *ptrDataBuf  = NULL; // pointer to message data structure
    struct CAN_buff *pDataBufNew = NULL; // aux pointer
    static uint8_T aliveCounterKR = 0;
    static uint8_T emptyCounterKR = 0;

    NRxFrWSInfo = 0;
    endFifoFlag = 0;
    retval = NO_ERROR;
    canQueLen = KLS_RRS_BUF_QUELEN;

    canRxErrtmp = CAN_RxData(VEHICLE_CAN, KLS_RRS_BUF, &ptrDataBuf);
    if ((canRxErrtmp != CAN_RX_BUFFER_EMPTY) && (canRxErrtmp != CAN_BUSOFF))
    {
        NRxFrWSInfo++;
        if (canRxErrtmp == CAN_RX_BUFFER_OVERRUN)
        {
            retval = CAN_RX_BUFFER_OVERRUN;
        }
        else
        {
            retval = NO_ERROR; 
        }
        while ((endFifoFlag == 0) && (NRxFrWSInfo < canQueLen))
        {
            if (CAN_RxData(VEHICLE_CAN, KLS_RRS_BUF, &pDataBufNew) == CAN_RX_BUFFER_EMPTY)
            {
                endFifoFlag = 1;
            }
            else
            {
                NRxFrWSInfo++;
                ptrDataBuf = pDataBufNew; // save the previous pointer
            }
        }

        /* ORDINAMENTO DEL MESSAGGIO */
        KLS_RRS->Byte0.R = ptrDataBuf->b[0]; // data0
        KLS_RRS->Byte1.R = ptrDataBuf->b[1]; // data1
        KLS_RRS->Byte2.R = ptrDataBuf->b[2]; // data2
        KLS_RRS->Byte3.R = ptrDataBuf->b[3]; // data3
        KLS_RRS->Byte4.R = ptrDataBuf->b[4]; // data4
        KLS_RRS->Byte5.R = ptrDataBuf->b[5]; // data5
        KLS_RRS->Byte6.R = ptrDataBuf->b[6]; // data6
        KLS_RRS->Byte7.R = ptrDataBuf->b[7]; // checksum

        /* CHECK LIVENESS */
        tmpWorker = KLS_RRS->Byte6.B.KRAliveCounter6;
        if (tmpWorker == ((aliveCounterKR + 1) & 0x07))
        {
            /* NONE */
        }
        else
        {
            FrmLostKLSRRS++;
            if (DISCANCNT == 0)
            {
                retval = CAN_RX_CNT_ERR;
            }
            else { /* Skip Diag */ }
        }
        aliveCounterKR = tmpWorker;

        /* CHECK INTEGRITY */
        integrityChk = KLS_RRS->Byte7.B.KRCheckSum7;
        integrityIn = CRC8_SAE_J1850_bit(0u, (uint8_T *) KLS_RRS, 7u);
        if ((integrityIn == integrityChk) || (DISCANCRC != 0))
        {
            /* ELABORAZIONE MESSAGGIO */
            if (McuKLSReq == RRS_SIGN)
            {
                if (KLS_RRS->Byte0.B.KLSSign0 == 0xAA)
                {
                    tmpWorker = (KLS_RRS->Byte1.B.KLSSign01 << 24);
                    
                    tmpWorker = tmpWorker | (KLS_RRS->Byte2.B.KLSSign02 << 16);
                    
                    tmpWorker = tmpWorker | (KLS_RRS->Byte3.B.KLSSign03 << 8);
                    
                    KLSSign = tmpWorker | (KLS_RRS->Byte4.B.KLSSign04);

                    tmpWorker = ((KLSSign >> LSQ) | (KLSSign << (32 - LSQ)));
                    tmpWorker = tmpWorker ^ KLSRnd;
                    if (EEECUUId == 0)
                    {
                        if (tmpWorker != 0)
                        {
                            if (tmpWorker == OldECUUID)
                            {
                                EEECUUId = OldECUUID;
                                KeyStored = 1;
                                InjDisableIMMO = 1;
                                McuKLSReq = RRS_ACK;
                            }
                            else
                            {
                                OldECUUID = tmpWorker;
                                if (CntKLSResend < RRS_N_RETRY)
                                {
                                    McuKLSReq = RRS_INIT;
                                    CntKLSResend++;
                                }
                                else
                                {
                                    McuKLSReq = RRS_NACK;
                                }
                            }
                        }
                        else
                        {
                            if (CntKLSResend < RRS_N_RETRY)
                            {
                                McuKLSReq = RRS_INIT;
                                CntKLSResend++;
                            }
                            else
                            {
                                McuKLSReq = RRS_NACK;
                            }
                        }
                    }
                    else if (tmpWorker == EEECUUId)
                    {
                        InjDisableIMMO = 1;
                        McuKLSReq = RRS_ACK;
                    }
                    else
                    {
                        if (CntKLSResend < RRS_N_RETRY)
                        {
                            McuKLSReq = RRS_INIT;
                            CntKLSResend++;
                        }
                        else
                        {
                            McuKLSReq = RRS_NACK;
                        }
                    }
                }
                else if (KLS_RRS->Byte0.B.KLSSign0 == 0xBB)
                {
                    McuKLSReq = RRS_STOP;
                }
                else { /* MISRA */ }
            }
        }
        else
        {
            retval = CAN_RX_CRC_ERR;
        }
        emptyCounterKR = 0;
    }
    else
    {
        if (canRxErrtmp == CAN_RX_BUFFER_EMPTY) 
        {
            if (emptyCounterKR >= KLS_RRS_EMPTY)
            {
                retval = CAN_RX_BUFFER_EMPTY;
            }
            else 
            {
                emptyCounterKR++;
                retval = CAN_RX_SKIP; 
            }
        }
        else
        {
            retval = CAN_BUSOFF;
            emptyCounterKR = 0;
        }
    }
    return retval;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_DASHBOARD(void)
{
    DASH_T *DASH = (DASH_T *)&Gen10[0];
    uint16_T timestamp;
    int16_T retval;
    uint8_T canQueLen;
    int32_T tmpWorker;
    uint8_T NRxFrWSInfo;
    uint8_T endFifoFlag;
    int16_T canRxErrtmp;
    uint8_T integrityIn = 0;
    uint8_T integrityChk = 0;
    struct CAN_buff *ptrDataBuf  = NULL; // pointer to message data structure
    struct CAN_buff *pDataBufNew = NULL; // aux pointer
    static uint8_T aliveCounterD = 0;
    static uint8_T emptyCounterD = 0;

    NRxFrWSInfo = 0;
    endFifoFlag = 0;
    retval = NO_ERROR;
    canQueLen = DASHBOARD_BUF_QUELEN;

    canRxErrtmp = CAN_RxData(VEHICLE_CAN, DASHBOARD_BUF, &ptrDataBuf);
    if ((canRxErrtmp != CAN_RX_BUFFER_EMPTY) && (canRxErrtmp != CAN_BUSOFF))
    {
        NRxFrWSInfo++;
        if (canRxErrtmp == CAN_RX_BUFFER_OVERRUN)
        {
            retval = CAN_RX_BUFFER_OVERRUN;
        }
        else
        {
            retval = NO_ERROR; 
        }
        while ((endFifoFlag == 0) && (NRxFrWSInfo < canQueLen))
        {
            if (CAN_RxData(VEHICLE_CAN, DASHBOARD_BUF, &pDataBufNew) == CAN_RX_BUFFER_EMPTY)
            {
                endFifoFlag = 1;
            }
            else
            {
                NRxFrWSInfo++;
                ptrDataBuf = pDataBufNew; // save the previous pointer
            }
        }

        /* TIMESTAMP */
        timestamp = ptrDataBuf->timestamp;

        /* ORDINAMENTO DEL MESSAGGIO */
        DASH->Byte0.R = ptrDataBuf->b[0]; // data0
        DASH->Byte1.R = ptrDataBuf->b[1]; // data1
        DASH->Byte2.R = ptrDataBuf->b[2]; // data2
        DASH->Byte3.R = ptrDataBuf->b[3]; // data3
        DASH->Byte4.R = ptrDataBuf->b[4]; // data4
        DASH->Byte5.R = ptrDataBuf->b[5]; // data5
        DASH->Byte6.R = ptrDataBuf->b[6]; // data6
        DASH->Byte7.R = ptrDataBuf->b[7]; // checksum

        /* CHECK LIVENESS */
        tmpWorker = DASH->Byte2.B.DsAliveCounter2;
        if (tmpWorker == ((aliveCounterD + 1) & 0x0F))
        {
            /* NONE */
        }
        else
        {
            FrmLostDashboard++;
            if (DISCANCNT == 0)
            {
                retval = CAN_RX_CNT_ERR;
            }
            else { /* Skip Diag */ }
        }
        aliveCounterD = (uint8_T)tmpWorker;

        /* CHECK INTEGRITY */
        integrityChk = DASH->Byte7.B.DsCheckSum7;
        integrityIn = CRC8_SAE_J1850_bit(0u, (uint8_T *) DASH, 7u);
        if ((integrityIn == integrityChk) || (DISCANCRC != 0))
        {
            /* ELABORAZIONE MESSAGGIO */
            
            // Lc TO
            LcToExitCAN =  DASH->Byte0.B.LcTimeout0;
            
            // Lc Level
            LcLevelCAN = DASH->Byte0.B.LcLevel0;

            // CC Status
            VehSpeedCtrlCAN = DASH->Byte0.B.VehicleSpeedControlStatus0;
            
            // Veh Speed Setup CAN
            tmpWorker = ((uint16_T)DASH->Byte0.B.VehicleSpeedSetUp0 << 8);
            tmpWorker = tmpWorker | DASH->Byte1.B.VehicleSpeedSetUp1;
            tmpWorker = (tmpWorker << VSCCSETUP_CAN_GAIN);
            tmpWorker = tmpWorker + VSCCSETUP_CAN_OFFSET;
            tmpWorker = max(tmpWorker, VSCCSETUP_CAN_OFFSET);
            VehSpeedSetUpCAN = (uint16_T)tmpWorker;

            // Low fuel Flag
            FlgLowFuel = DASH->Byte2.B.LowFuelFlag2;

            // TC Status
            SetTracCtrlCAN = DASH->Byte3.B.TractionControlStatus3;

            // AT routine fleg
            ATroutineFlagCAN = DASH->Byte3.B.ATroutineFlag3;

            // SCU requst
            StSuspReqCAN = DASH->Byte3.B.SuspensionModeRequest3;

            // Gas sens
            SetAccSens = DASH->Byte4.B.GasSensitivity4;

            // Max Trq
            SetMaxTrq = DASH->Byte4.B.MaxEngineTorque4;

            // Eng brake
            SetEngBrake = DASH->Byte4.B.EngineBrake4;

            // Eng resp
            SetEngResp = DASH->Byte4.B.EngineResponse4;

            // Rpm lim
            SetRpmLim = DASH->Byte4.B.RpmLimiter4;

            // QS En
            QuickShiftEnCAN = DASH->Byte4.B.QuickShiftEN4;

            GripHeatLevelCAN = DASH->Byte2.B.HeatedGripStatus2;  /* updated according to MV8 dbc file*/ // niso added.

            // Crepuscolare
            BKLStatusCAN = DASH->Byte6.B.BKLStatus;

            // Bike load
            StSuspCAN = DASH->Byte6.B.BikeLoadStatus6;

            // FLC Level
            AwLevelCAN = DASH->Byte6.B.FLCLevel6;

            /* POST PROCESSING */
            EnVSpeedLimM = (VehSpeedCtrlCAN == VEH_SPEED_CTRL_STS_LIMITER);
            if (McuKLSReq == RRS_RND)
            {
                DASHTimestamp = timestamp;
                McuKLSReq = RRS_REQ;
            }
            else { /* MISRA */ }
        }
        else
        {
            retval = CAN_RX_CRC_ERR;
        }
        emptyCounterD = 0;
    }
    else
    {
        if (canRxErrtmp == CAN_RX_BUFFER_EMPTY) 
        {
            if (emptyCounterD >= DASH_EMPTY)
            {
                retval = CAN_RX_BUFFER_EMPTY;
            }
            else 
            {
                emptyCounterD++;
                retval = CAN_RX_SKIP; 
            }
        }
        else
        {
            retval = CAN_BUSOFF;
            emptyCounterD = 0;
        }
    }
    return retval;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_DASH_INFO(void)
{
    DINFO_T *DINFO = (DINFO_T *)&Gen10[0];
    int16_T retval;
    uint8_T canQueLen;
    int32_T tmpWorker;
    uint8_T NRxFrWSInfo;
    uint8_T endFifoFlag;
    int16_T canRxErrtmp;
    uint8_T integrityIn = 0;
    uint8_T integrityChk = 0;
    struct CAN_buff *ptrDataBuf  = NULL; // pointer to message data structure
    struct CAN_buff *pDataBufNew = NULL; // aux pointer
    static uint8_T aliveCounterI = 0;
    static uint8_T emptyCounterI = 0;

    NRxFrWSInfo = 0;
    endFifoFlag = 0;
    retval = NO_ERROR;
    canQueLen = DASHBOARD_INFO_BUF_QUELEN;

    canRxErrtmp = CAN_RxData(VEHICLE_CAN, DASHBOARD_INFO_BUF, &ptrDataBuf);
    if ((canRxErrtmp != CAN_RX_BUFFER_EMPTY) && (canRxErrtmp != CAN_BUSOFF))
    {
        NRxFrWSInfo++;
        if (canRxErrtmp == CAN_RX_BUFFER_OVERRUN)
        {
            retval = CAN_RX_BUFFER_OVERRUN;
        }
        else
        {
            retval = NO_ERROR; 
        }
        while ((endFifoFlag == 0) && (NRxFrWSInfo < canQueLen))
        {
            if (CAN_RxData(VEHICLE_CAN, DASHBOARD_INFO_BUF, &pDataBufNew) == CAN_RX_BUFFER_EMPTY)
            {
                endFifoFlag = 1;
            }
            else
            {
                NRxFrWSInfo++;
                ptrDataBuf = pDataBufNew; // save the previous pointer
            }
        }

        /* ORDINAMENTO DEL MESSAGGIO */
        DINFO->Byte0.R = ptrDataBuf->b[0]; // data0
        DINFO->Byte1.R = ptrDataBuf->b[1]; // data1
        DINFO->Byte2.R = ptrDataBuf->b[2]; // data2
        DINFO->Byte3.R = ptrDataBuf->b[3]; // data3
        DINFO->Byte4.R = ptrDataBuf->b[4]; // data4
        DINFO->Byte5.R = ptrDataBuf->b[5]; // data5
        DINFO->Byte6.R = ptrDataBuf->b[6]; // data6
        DINFO->Byte7.R = ptrDataBuf->b[7]; // checksum

        /* CHECK LIVENESS */
        tmpWorker = DINFO->Byte5.B.DsiAliveCounter5;
        if (tmpWorker == ((aliveCounterI + 1) & 0x0F))
        {
            /* NONE */
        }
        else
        {
            FrmLostDBInfo++;
            if (DISCANCNT == 0)
            {
                retval = CAN_RX_CNT_ERR;
            }
            else { /* Skip Diag */ }
        }
        aliveCounterI = (uint8_T)tmpWorker;

        /* CHECK INTEGRITY */
        integrityChk = DINFO->Byte7.B.DsiCheckSum7;
        integrityIn = CRC8_SAE_J1850_bit(0u, (uint8_T *) DINFO, 7u);
        if ((integrityIn == integrityChk) || (DISCANCRC != 0))
        {
            /* ELABORAZIONE MESSAGGIO */
            
            // TC2W Setup request
            TCSetupRequest = DINFO->Byte0.B.TC_Setup_request0;

            // QS Mode
            QSSetup = DINFO->Byte0.B.QSCyborgMode0;

            // OTA Update
            OTAUpdate = DINFO->Byte2.B.OTAUpdate2;

            // Tyre Type
            TyreTypeCAN = DINFO->Byte2.B.TyreType2;

            // Init Starter Enable
            InitStarterEnCAN = DINFO->Byte2.B.InitialCheckFlag2;

            /* POST PROCESSING */
            if (FOTYRETYPE >= 0)
            {
                TyreTypeCAN = (uint8_T)FOTYRETYPE;
            }
            else { /* MISRA */ }
        }
        else
        {
            retval = CAN_RX_CRC_ERR;
        }
        emptyCounterI = 0;
    }
    else
    {
        if (canRxErrtmp == CAN_RX_BUFFER_EMPTY) 
        {
            if (emptyCounterI >= DS_INFO_EMPTY)
            {
                retval = CAN_RX_BUFFER_EMPTY;
            }
            else 
            {
                emptyCounterI++;
                retval = CAN_RX_SKIP; 
            }
        }
        else
        {
            retval = CAN_BUSOFF;
            emptyCounterI = 0;
        }
    }
    return retval;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_ABS(void)
{
    ABS_T *ABS = (ABS_T *)&Gen05[0];
    int8_T i;
    uint8_T flgFrz;
    int16_T retval;
    uint16_T tmpVSpeed;
    uint8_T canQueLen;
    int32_T tmpWorker;
    uint8_T NRxFrWSInfo;                 
    uint8_T endFifoFlag;
    int16_T canRxErrtmp;
    uint8_T integrityIn = 0;
    uint8_T integrityChk = 0;
    struct CAN_buff *ptrDataBuf  = NULL; // pointer to message data structure
    struct CAN_buff *pDataBufNew = NULL; // aux pointer
    static uint8_T aliveCounterA = 0;
    static uint8_T emptyCounterA = 0;
    static uint16_T oldVehSpeedFrontCAN = 0;
    static uint16_T oldVehSpeedRearCAN = 0;
    static uint8_T  stVehFilt = 0;
    static uint16_T cntExit = 0;
    static uint8_T  stABSWL = 0;

    NRxFrWSInfo = 0;
    endFifoFlag = 0;
    retval = NO_ERROR;
    canQueLen = ABS_BUF_QUELEN;

    canRxErrtmp = CAN_RxData(VEHICLE_CAN, ABS_BUF, &ptrDataBuf);
    if ((canRxErrtmp != CAN_RX_BUFFER_EMPTY) && (canRxErrtmp != CAN_BUSOFF))
    {
        NRxFrWSInfo++;
        if (canRxErrtmp == CAN_RX_BUFFER_OVERRUN)
        {
            retval = CAN_RX_BUFFER_OVERRUN;
        }
        else
        {
            retval = NO_ERROR; 
        }
        while ((endFifoFlag == 0) && (NRxFrWSInfo < canQueLen))
        {
            if (CAN_RxData(VEHICLE_CAN, ABS_BUF, &pDataBufNew) == CAN_RX_BUFFER_EMPTY)
            {
                endFifoFlag = 1;
            }
            else
            {
                NRxFrWSInfo++;
                ptrDataBuf = pDataBufNew; // save the previous pointer
            }
        }

        /* ORDINAMENTO DEL MESSAGGIO */
        ABS->Byte0.R = ptrDataBuf->b[0]; // data0
        ABS->Byte1.R = ptrDataBuf->b[1]; // data1
        ABS->Byte2.R = ptrDataBuf->b[2]; // data2
        ABS->Byte3.R = ptrDataBuf->b[3]; // data3
        ABS->Byte4.R = ptrDataBuf->b[4]; // data4
        ABS->Byte5.R = ptrDataBuf->b[5]; // data5
        ABS->Byte6.R = ptrDataBuf->b[6]; // data6
        ABS->Byte7.R = ptrDataBuf->b[7]; // data7

        #if 0
        /* CHECK LIVENESS */
        tmpWorker = ABS->Byte2.B.DsAliveCounter2;
        if (tmpWorker == ((aliveCounterA + 1) & 0x0F))
        {
            /* NONE */
        }
        else
        {
            FrmLostABS++;
            if (DISCANCNT == 0)
            {
                retval = CAN_RX_CNT_ERR;
            }
            else { /* Skip Diag */ }
        }
        aliveCounterA = (uint8_T)tmpWorker;

        /* CHECK INTEGRITY */
        integrityChk = ABS->Byte7.B.DsCheckSum7;
        integrityIn = CRC8_SAE_J1850_bit(0u, (uint8_T *) ABS, 7u);
        #else
        integrityIn = integrityChk;
        #endif
        if ((integrityIn == integrityChk) || (DISCANCRC != 0))
        {
            /* ELABORAZIONE MESSAGGIO */
            
            // Front speed
            tmpWorker = ((uint16_T)ABS->Byte0.B.VehSpeedFrontCAN0 << 8);
            tmpWorker = (tmpWorker | ABS->Byte1.B.VehSpeedFrontCAN1);
            tmpWorker = (tmpWorker << VEHSPEEDFRONT_ABS_CAN_GAIN);
            tmpWorker = tmpWorker + VEHSPEEDFRONT_ABS_CAN_OFFSET;
            tmpWorker = max(tmpWorker, VEHSPEEDFRONT_ABS_CAN_OFFSET);
            tmpWorker = min(tmpWorker, MAX_uint16_T);
            VehSpeedFrontRawCAN = tmpWorker;

            // Rear speed
            tmpWorker = ((uint16_T)ABS->Byte2.B.VehSpeedRearCAN2 << 8);
            tmpWorker = (tmpWorker | ABS->Byte3.B.VehSpeedRearCAN3);
            tmpWorker = (tmpWorker << VEHSPEEDREAR_ABS_CAN_GAIN);
            tmpWorker = tmpWorker + VEHSPEEDREAR_ABS_CAN_OFFSET;
            tmpWorker = max(tmpWorker, VEHSPEEDREAR_ABS_CAN_OFFSET);
            tmpWorker = min(tmpWorker, MAX_uint16_T);
            VehSpeedRearRawCAN = tmpWorker;

            // Warning Lamp
            ABSWarningLamp = ABS->Byte4.B.ABSWarningLamp4;

            // ABS Presence
            ABSPresent = ABS->Byte4.B.ABSPresent4;

            // VD Front brake pressure
            VDFrontWheelPresCAN = ((ABS->Byte6.B.VDFrontWheelPressure6 != 0) /* || (ABSWarningLamp == 1) */);
            if (VDFrontWheelPresCAN == 0)
            {
                // Front brake pressure
                tmpWorker = ABS->Byte5.B.Front_Wheel_Pressure5;
                tmpWorker = (tmpWorker << FRONTWHLPRESSURE_CAN_GAIN);
                tmpWorker = tmpWorker + FRONTWHLPRESSURE_CAN_OFFSET;
                tmpWorker = max(tmpWorker, FRONTWHLPRESSURE_CAN_OFFSET);
                FrontWheelPresCAN = min(tmpWorker, MAX_uint8_T);
            }
            else 
            {
                FrontWheelPresCAN = 0;
            }

            if (ENFRZVEHCANENGSTR != 0)
            {
                switch (stVehFilt)
                {
                    case 0:
                    {
                        if (EngineStartCmd != 0)
                        {
                            flgFrz = 1;
                            cntExit = 0;
                            stVehFilt = 1;
                        }
                        else
                        {
                            flgFrz = 0;
                        }
                    }
                    break;

                    default:
                    {
                        if ((EndStartFlg == 0) && (cntExit < MaxRelayKeeping))
                        {
                            flgFrz = 1;
                            cntExit++;
                        }
                        else
                        {
                            flgFrz = 0;
                            stVehFilt = 0;
                        }
                    }
                    break;
                }
            }
            else
            {
                flgFrz = 0;
                stVehFilt = 0;
            }
            if (flgFrz != 0)
            {
                VehSpeedFrontCAN = oldVehSpeedFrontCAN;
                VehSpeedRearCAN = oldVehSpeedRearCAN;
                VDVehSpeedRearCAN = 0;
                VDVehSpeedFrontCAN = 0;
            }
            else
            {
                VehSpeedFrontCAN = VehSpeedFrontRawCAN;
                oldVehSpeedFrontCAN = VehSpeedFrontCAN;
                VehSpeedRearCAN = VehSpeedRearRawCAN;
                oldVehSpeedRearCAN = VehSpeedRearCAN;

                // VD Rear speed
                VDVehSpeedRearCAN = ABS->Byte4.B.VDVehSpeedRearCAN4;

                // VD Front speed
                VDVehSpeedFrontCAN = ABS->Byte4.B.VDVehSpeedFrontCAN4;
            }
            
            /* POST PROCESSING */
            if (VehFrontSourceSel != 0) /* ABS Veh_Speed. */
            {
                i = (SIZE_VS_CAN - 1);
                
                Update_FrontRollSpeed(&VehSpeedFrontNcCAN, &FrontWheelRadiusNc, 0u);
                Update_FrontRollSpeed(&tmpVSpeed, &FrontWheelRadius, 1u);
                if (VDVehSpeedFrontCAN == 0)
                {
                    while (i > 0)
                    {
                        BufVehSpeedFrontCAN[i] = BufVehSpeedFrontCAN[(i - 1)];
                        i--;
                    }
                    BufVehSpeedFrontCAN[0] = tmpVSpeed;
                }
                else { /* Skip */ }
            }
            else { /* Skip */ }
        
            if (VehRearSourceSel != 0) /* ABS Veh_Speed. */
            {
                i = (SIZE_VS_CAN - 1);
                
                Update_RearRollSpeed(&VehSpeedRearNcCAN, &RearWheelRadiusNc, 0u);
                Update_RearRollSpeed(&tmpVSpeed, &RearWheelRadius, 1u);
                if (VDVehSpeedRearCAN == 0)
                {
                    while (i > 0)
                    {
                        BufVehSpeedRearCAN[i] = BufVehSpeedRearCAN[(i - 1)];
                        i--;
                    }
                    BufVehSpeedRearCAN[0] = tmpVSpeed;
                }
                else { /* Skip */ }
            }
            else { /* Skip */ }

            switch (stABSWL)
            {
                case 0u:
                {
                    VDAbsWL = 0u;
                    if (KeySignal == 0)
                    {
                        stABSWL = 1u;
                    }
                    else { /* MISRA */ }
                }
                break;

                case 1u:
                {
                    VDAbsWL = 0u;
                    if (VehSpeed > ((uint16_T)(7.0f * 16.0f)))
                    {
                        stABSWL = 2u;
                    }
                    else { /* MISRA */ }
                }
                break;

                case 2u:
                {
                    if (ABSWarningLamp != 0)
                    {
                        VDAbsWL = 1u;
                    }
                    else
                    {
                        VDAbsWL = 0u;
                    }
                    if (KeySignal == 0)
                    {
                        stABSWL = 0u;
                    }
                    else { /* MISRA */ }
                }
                break;

                default:
                {
                    /* */
                }
                break;
            }
            
            if (EECANNode3En == 0)
            {
                if (CntCANNode3En >= NMSGCANNODE3EN)
                {
                    EECANNode3En = 1;
                    SETBIT(VehOptConfig, 0);
                    CntCANNode3En = 0;
                }
                else
                {
                    CntCANNode3En++;
                }
            }
            else { /* Skip */ }
        }
        else
        {
            retval = CAN_RX_CRC_ERR;
        }
        emptyCounterA = 0;
    }
    else
    {
        if (canRxErrtmp == CAN_RX_BUFFER_EMPTY) 
        {
            if (emptyCounterA >= ABS_EMPTY)
            {
                retval = CAN_RX_BUFFER_EMPTY;
            }
            else 
            {
                emptyCounterA++;
                retval = CAN_RX_SKIP; 
            }
        }
        else
        {
            retval = CAN_BUSOFF;
            emptyCounterA = 0;
        }
    }
    return retval;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_MCU1 (void)
{
    MCU1_T *MCU1 = (MCU1_T *)&Gen10[0];
    uint16_T timestamp;
    int16_T retval;
    uint8_T canQueLen;
    int32_T tmpWorker;
    uint8_T NRxFrWSInfo;
    uint8_T endFifoFlag;
    int16_T canRxErrtmp;
    uint8_T integrityIn = 0;
    uint8_T integrityChk = 0;
    struct CAN_buff *ptrDataBuf  = NULL; // pointer to message data structure
    struct CAN_buff *pDataBufNew = NULL; // aux pointer
    static uint8_T aliveCounterM = 0;
    static uint8_T emptyCounterM = 0;

    NRxFrWSInfo = 0;
    endFifoFlag = 0;
    retval = NO_ERROR;
    canQueLen = MCU1_BUF_QUELEN;

    canRxErrtmp = CAN_RxData(VEHICLE_CAN, MCU1_BUF, &ptrDataBuf);
    if ((canRxErrtmp != CAN_RX_BUFFER_EMPTY) && (canRxErrtmp != CAN_BUSOFF))
    {
        NRxFrWSInfo++;
        if (canRxErrtmp == CAN_RX_BUFFER_OVERRUN)
        {
            retval = CAN_RX_BUFFER_OVERRUN;
        }
        else
        {
            retval = NO_ERROR; 
        }
        while ((endFifoFlag == 0) && (NRxFrWSInfo < canQueLen))
        {
            if (CAN_RxData(VEHICLE_CAN, MCU1_BUF, &pDataBufNew) == CAN_RX_BUFFER_EMPTY)
            {
                endFifoFlag = 1;
            }
            else
            {
                NRxFrWSInfo++;
                ptrDataBuf = pDataBufNew; // save the previous pointer
            }
        }

        /* TIMESTAMP */
        timestamp = ptrDataBuf->timestamp;

        /* ORDINAMENTO DEL MESSAGGIO */
        MCU1->Byte0.R = ptrDataBuf->b[0]; // data0
        MCU1->Byte1.R = ptrDataBuf->b[1]; // data1
        MCU1->Byte2.R = ptrDataBuf->b[2]; // data2
        MCU1->Byte3.R = ptrDataBuf->b[3]; // data3
        MCU1->Byte4.R = ptrDataBuf->b[4]; // data4
        MCU1->Byte5.R = ptrDataBuf->b[5]; // data5
        MCU1->Byte6.R = ptrDataBuf->b[6]; // data6
        MCU1->Byte7.R = ptrDataBuf->b[7]; // checksum

        /* CHECK LIVENESS */
        tmpWorker = MCU1->Byte6.B.Mcu1AliveCounter6;
        if (tmpWorker == ((aliveCounterM + 1) & 0x0F))
        {
            /* NONE */
        }
        else
        {
            FrmLostMCU1++;
            if (DISCANCNT == 0)
            {
                retval = CAN_RX_CNT_ERR;
            }
            else { /* Skip Diag */ }
        }
        aliveCounterM = (uint8_T)tmpWorker;

        /* CHECK INTEGRITY */
        integrityChk = MCU1->Byte7.B.Mcu1CheckSum7;
        integrityIn = CRC8_SAE_J1850_bit(0u, (uint8_T *) MCU1, 7u);
        if ((integrityIn == integrityChk) || (DISCANCRC != 0))
        {
            /* ELABORAZIONE MESSAGGIO */
            
            // Enviromental Air temperature
            tmpWorker = (MCU1->Byte0.B.AirTempEnv0 << TAIRENV_CAN_GAIN);
            tmpWorker = tmpWorker + TAIRENV_CAN_OFFSET;
            TAirCAN = max(tmpWorker, TAIRENV_CAN_OFFSET);

            // Rider presence
            #ifdef MCU1
            RiderPresenceCAN = MCU1->Byte2.B.RiderPresence2;
            #endif
         

            // NCM Immo
            FlgNCMImmo = MCU1->Byte2.B.NCMImmo2;

            // Validity data
            VDBrakeSignal = MCU1->Byte2.B.VDBrakeSignal2;

            // Main relay command
            #ifdef MCU1
            MainRelCmd = MCU1->Byte2.B.MainRelayCtrl2;
            #endif

            // Validity data
            VDTAirCAN = MCU1->Byte4.B.VDAirTempEnv4;

            // Brake signal
            BrakeLampCAN = MCU1->Byte4.B.BrakeSignalLamp4;

            // Parking brake signal
            #ifdef MCU1
            ParkBrakeSignal = MCU1->Byte4.B.ParkingBrake4; //niso.
            #endif

            // Transport lock
            TransportLock = MCU1->Byte4.B.TransportLock4;

            // Starter feedback
            StarterCmdFdbkCAN = MCU1->Byte4.B.StarterMotorCtrl4;

            /* POST PROCESSING */
            CntMCU1CAN++;
            if ((FlgNCMImmo != 0) && (McuKLSReq == RRS_INIT))
            {
                MCU1Timestamp = timestamp;
                McuKLSReq = RRS_RND;
            }
            else { /* MISRA */ }
        }
        else
        {
            retval = CAN_RX_CRC_ERR;
        }
        emptyCounterM = 0;
    }
    else
    {
        if (canRxErrtmp == CAN_RX_BUFFER_EMPTY) 
        {
            if (emptyCounterM >= MCU1_EMPTY)
            {
                retval = CAN_RX_BUFFER_EMPTY;
            }
            else 
            {
                emptyCounterM++;
                retval = CAN_RX_SKIP; 
            }
        }
        else
        {
            retval = CAN_BUSOFF;
            emptyCounterM = 0;
        }
    }
    return retval;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_LEFT_HB (void)
{
    LHB_T *LHB = (LHB_T *)&Gen10[0];
    int16_T retval;
    uint8_T canQueLen;
    int32_T tmpWorker;
    uint8_T NRxFrWSInfo;                 
    uint8_T endFifoFlag;
    int16_T canRxErrtmp;
    uint8_T integrityIn = 0;
    uint8_T integrityChk = 0;
    struct CAN_buff *ptrDataBuf  = NULL; // pointer to message data structure
    struct CAN_buff *pDataBufNew = NULL; // aux pointer
    static uint8_T aliveCounterL = 0;
    static uint8_T emptyCounterL = 0;

    NRxFrWSInfo = 0;
    endFifoFlag = 0;
    retval = NO_ERROR;
    canQueLen = LHB_BUF_QUELEN;

    canRxErrtmp = CAN_RxData(VEHICLE_CAN, LHB_BUF, &ptrDataBuf);
    if ((canRxErrtmp != CAN_RX_BUFFER_EMPTY) && (canRxErrtmp != CAN_BUSOFF))
    {
        NRxFrWSInfo++;
        if (canRxErrtmp == CAN_RX_BUFFER_OVERRUN)
        {
            retval = CAN_RX_BUFFER_OVERRUN;
        }
        else
        {
            retval = NO_ERROR; 
        }
        while ((endFifoFlag == 0) && (NRxFrWSInfo < canQueLen))
        {
            if (CAN_RxData(VEHICLE_CAN, LHB_BUF, &pDataBufNew) == CAN_RX_BUFFER_EMPTY)
            {
                endFifoFlag = 1;
            }
            else
            {
                NRxFrWSInfo++;
                ptrDataBuf = pDataBufNew; // save the previous pointer
            }
        }

        /* ORDINAMENTO DEL MESSAGGIO */
        LHB->Byte0.R = ptrDataBuf->b[0]; // data0
        LHB->Byte1.R = ptrDataBuf->b[1]; // data1
        LHB->Byte2.R = ptrDataBuf->b[2]; // data2
        LHB->Byte3.R = ptrDataBuf->b[3]; // data3
        LHB->Byte4.R = ptrDataBuf->b[4]; // data4
        LHB->Byte5.R = ptrDataBuf->b[5]; // data5
        LHB->Byte6.R = ptrDataBuf->b[6]; // data6
        LHB->Byte7.R = ptrDataBuf->b[7]; // checksum

        /* CHECK LIVENESS */
        tmpWorker = LHB->Byte6.B.LHs_Counter6;
        if (tmpWorker == ((aliveCounterL + 1) & 0x0F))
        {
            /* NONE */
        }
        else
        {
            FrmLostLHB++;
            if (DISCANCNT == 0)
            {
                retval = CAN_RX_CNT_ERR;
            }
            else { /* Skip Diag */ }
        }
        aliveCounterL = (uint8_T)tmpWorker;

        /* CHECK INTEGRITY */
        integrityChk = LHB->Byte7.B.LHs_CheckSum7;
        integrityIn = CRC8_SAE_J1850_bit(0u, (uint8_T *) LHB, 7u);
        if ((integrityIn == integrityChk) || (DISCANCRC != 0))
        {
            /* ELABORAZIONE MESSAGGIO */
            
            // Clutch switch 2
            ClutchSignal2CAN = LHB->Byte2.B.ClutchSwitch2_2;

            // Clutch switch 1;
            ClutchSignal1CAN = LHB->Byte2.B.ClutchSwitch1_2;

            // Ecu error
            LHsStError = LHB->Byte4.B.LHsStError4;

            // Horn signaltest
            // if(FORCEHORNSIGNAL == 1)
            // {
            //     HornSignalCAN = 1;
            // }
            // else
            // {
            //     HornSignalCAN = LHB->Byte1.B.HornButton1;
            // }

            HornSignalCAN = LHB->Byte1.B.HornButton1;
            

            /* POST PROCESSING */
            /* */ 
        }
        else
        {
            retval = CAN_RX_CRC_ERR;
        }
        emptyCounterL = 0;
    }
    else
    {
        if (canRxErrtmp == CAN_RX_BUFFER_EMPTY) 
        {
            if (emptyCounterL >= LHB_EMPTY)
            {
                retval = CAN_RX_BUFFER_EMPTY;
            }
            else 
            {
                emptyCounterL++;
                retval = CAN_RX_SKIP; 
            }
        }
        else
        {
            retval = CAN_BUSOFF;
            emptyCounterL = 0;
        }
    }
    return retval;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_RIGHT_HB (void)
{
    RHB_T *RHB = (RHB_T *)&Gen10[0];
    int16_T retval;
    uint8_T canQueLen;
    int32_T tmpWorker;
    uint8_T NRxFrWSInfo;                 
    uint8_T endFifoFlag;
    int16_T canRxErrtmp;
    uint8_T integrityIn = 0;
    uint8_T integrityChk = 0;
    struct CAN_buff *ptrDataBuf  = NULL; // pointer to message data structure
    struct CAN_buff *pDataBufNew = NULL; // aux pointer
    static uint8_T aliveCounterR = 0;
    static uint8_T emptyCounterR = 0;

    NRxFrWSInfo = 0;
    endFifoFlag = 0;
    retval = NO_ERROR;
    canQueLen = RHB_BUF_QUELEN;

    canRxErrtmp = CAN_RxData(VEHICLE_CAN, RHB_BUF, &ptrDataBuf);
    if ((canRxErrtmp != CAN_RX_BUFFER_EMPTY) && (canRxErrtmp != CAN_BUSOFF))
    {
        NRxFrWSInfo++;
        if (canRxErrtmp == CAN_RX_BUFFER_OVERRUN)
        {
            retval = CAN_RX_BUFFER_OVERRUN;
        }
        else
        {
            retval = NO_ERROR; 
        }
        while ((endFifoFlag == 0) && (NRxFrWSInfo < canQueLen))
        {
            if (CAN_RxData(VEHICLE_CAN, RHB_BUF, &pDataBufNew) == CAN_RX_BUFFER_EMPTY)
            {
                endFifoFlag = 1;
            }
            else
            {
                NRxFrWSInfo++;
                ptrDataBuf = pDataBufNew; // save the previous pointer
            }
        }

        /* ORDINAMENTO DEL MESSAGGIO */
        RHB->Byte0.R = ptrDataBuf->b[0]; // data0
        RHB->Byte1.R = ptrDataBuf->b[1]; // data1
        RHB->Byte2.R = ptrDataBuf->b[2]; // data2
        RHB->Byte3.R = ptrDataBuf->b[3]; // data3
        RHB->Byte4.R = ptrDataBuf->b[4]; // data4
        RHB->Byte5.R = ptrDataBuf->b[5]; // data5
        RHB->Byte6.R = ptrDataBuf->b[6]; // data6
        RHB->Byte7.R = ptrDataBuf->b[7]; // checksum

        /* CHECK LIVENESS */
        tmpWorker = RHB->Byte6.B.RHs_Counter6;
        if (tmpWorker == ((aliveCounterR + 1) & 0x0F))
        {
            /* NONE */
        }
        else
        {
            FrmLostRHB++;
            if (DISCANCNT == 0)
            {
                retval = CAN_RX_CNT_ERR;
            }
            else { /* Skip Diag */ }
        }
        aliveCounterR = (uint8_T)tmpWorker;

        /* CHECK INTEGRITY */
        integrityChk = RHB->Byte7.B.RHs_CheckSum7;
        integrityIn = CRC8_SAE_J1850_bit(0u, (uint8_T *) RHB, 7u);
        if ((integrityIn == integrityChk) || (DISCANCRC != 0))
        {
            /* ELABORAZIONE MESSAGGIO */
            
            // Stop signal
            StopSignal = RHB->Byte0.B.RunStopButton0;

            // Starter signal
            StartSignalCAN = RHB->Byte0.B.StartMap0;

            // Ecu Error
            RHsStError = RHB->Byte4.B.RHsStError4;

            /* POST PROCESSING */
            /* */
        }
        else
        {
            retval = CAN_RX_CRC_ERR;
        }
        emptyCounterR = 0;
    }
    else
    {
        if (canRxErrtmp == CAN_RX_BUFFER_EMPTY) 
        {
            if (emptyCounterR >= RHB_EMPTY)
            {
                retval = CAN_RX_BUFFER_EMPTY;
            }
            else 
            {
                emptyCounterR++;
                retval = CAN_RX_SKIP; 
            }
        }
        else
        {
            retval = CAN_BUSOFF;
            emptyCounterR = 0;
        }
    }
    return retval;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_GPS_02 (void)
{
    GPS02_T *GPS02 = (GPS02_T *)&Gen10[0];
    int16_T retval;
    uint8_T canQueLen;
    int32_T tmpWorker;
    int16_T tmpWorker2;
    uint8_T NRxFrWSInfo;                 
    uint8_T endFifoFlag;
    int16_T canRxErrtmp;
    uint8_T integrityIn = 0;
    uint8_T integrityChk = 0;
    struct CAN_buff *ptrDataBuf  = NULL; // pointer to message data structure
    struct CAN_buff *pDataBufNew = NULL; // aux pointer
    static uint8_T aliveCounterGP2 = 0;
    static uint8_T emptyCounterGP2 = 0;

    NRxFrWSInfo = 0;
    endFifoFlag = 0;
    retval = NO_ERROR;
    canQueLen = GPS_02_BUF_QUELEN;

    canRxErrtmp = CAN_RxData(VEHICLE_CAN, GPS_02_BUF, &ptrDataBuf);
    if ((canRxErrtmp != CAN_RX_BUFFER_EMPTY) && (canRxErrtmp != CAN_BUSOFF))
    {
        NRxFrWSInfo++;
        if (canRxErrtmp == CAN_RX_BUFFER_OVERRUN)
        {
            retval = CAN_RX_BUFFER_OVERRUN;
        }
        else
        {
            retval = NO_ERROR; 
        }
        while ((endFifoFlag == 0) && (NRxFrWSInfo < canQueLen))
        {
            if (CAN_RxData(VEHICLE_CAN, GPS_02_BUF, &pDataBufNew) == CAN_RX_BUFFER_EMPTY)
            {
                endFifoFlag = 1;
            }
            else
            {
                NRxFrWSInfo++;
                ptrDataBuf = pDataBufNew; // save the previous pointer
            }
        }

        /* ORDINAMENTO DEL MESSAGGIO */
        GPS02->Byte0.R = ptrDataBuf->b[0]; // data0
        GPS02->Byte1.R = ptrDataBuf->b[1]; // data1
        GPS02->Byte2.R = ptrDataBuf->b[2]; // data2
        GPS02->Byte3.R = ptrDataBuf->b[3]; // data3
        GPS02->Byte4.R = ptrDataBuf->b[4]; // data4
        GPS02->Byte5.R = ptrDataBuf->b[5]; // data5
        GPS02->Byte6.R = ptrDataBuf->b[6]; // data6
        GPS02->Byte7.R = ptrDataBuf->b[7]; // data7

        #if 0
        /* CHECK LIVENESS */
        tmpWorker = GPS02->Byte2.B.DsAliveCounter2;
        if (tmpWorker == ((aliveCounterGP2 + 1) & 0x0F))
        {
            /* NONE */
        }
        else
        {
            FrmLostABS++;
            if (DISCANCNT == 0)
            {
                retval = CAN_RX_CNT_ERR;
            }
            else { /* Skip Diag */ }
        }
        aliveCounterGP2 = (uint8_T)tmpWorker;

        /* CHECK INTEGRITY */
        integrityChk = GPS02->Byte7.B.DsCheckSum7;
        integrityIn = CRC8_SAE_J1850_bit(0u, (uint8_T *) GPS02, 7u);
        #else
        integrityIn = integrityChk;
        #endif
        if ((integrityIn == integrityChk) || (DISCANCRC != 0))
        {
            /* ELABORAZIONE MESSAGGIO */
            
            // Altitude GPS
            tmpWorker = ((uint16_T)GPS02->Byte1.B.Altitude1 << 8);
            tmpWorker = tmpWorker | GPS02->Byte0.B.Altitude0;
            tmpWorker2 = (int16_T)tmpWorker;
            tmpWorker2 = (tmpWorker2 * ALT_GPS_GAIN);
            tmpWorker2 = tmpWorker2 + ALT_GPS_OFFSET;
            tmpWorker2 = max(tmpWorker2, ALT_GPS_MIN);
            AltitudeGPS = min(tmpWorker2, ALT_GPS_MAX);
        
            // VehSpeed GPS
            tmpWorker = ((uint16_T)GPS02->Byte6.B.Speed6 << 6);
            tmpWorker2 = GPS02->Byte7.B.Speed7;
            tmpWorker = tmpWorker | tmpWorker2;
            tmpWorker = (tmpWorker << SPEED_GPS_GAIN);
            tmpWorker = tmpWorker + SPEED_GPS_OFFSET;
            tmpWorker = max(tmpWorker, SPEED_GPS_MIN);
            VehSpeedGPS = min(tmpWorker, SPEED_GPS_MAX);
            
            /* POST PROCESSING */
            /* */
        }
        else
        {
            retval = CAN_RX_CRC_ERR;
        }
        emptyCounterGP2 = 0;
    }
    else
    {
        if (canRxErrtmp == CAN_RX_BUFFER_EMPTY) 
        {
            if (emptyCounterGP2 >= GPS_02_EMPTY)
            {
                retval = CAN_RX_BUFFER_EMPTY;
            }
            else 
            {
                emptyCounterGP2++;
                retval = CAN_RX_SKIP; 
            }
        }
        else
        {
            retval = CAN_BUSOFF;
            emptyCounterGP2 = 0;
        }
    }
    return retval;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_GPS_04 (void)
{
    GPS04_T *GPS04 = (GPS04_T *)&Gen10[0];
    int16_T retval;
    uint8_T canQueLen;
    int32_T tmpWorker;
    int16_T tmpWorker2;
    uint8_T NRxFrWSInfo;                 
    uint8_T endFifoFlag;
    int16_T canRxErrtmp;
    uint8_T integrityIn = 0;
    uint8_T integrityChk = 0;
    struct CAN_buff *ptrDataBuf  = NULL; // pointer to message data structure
    struct CAN_buff *pDataBufNew = NULL; // aux pointer
    static uint8_T aliveCounterGP4 = 0;
    static uint8_T emptyCounterGP4 = 0;

    NRxFrWSInfo = 0;
    endFifoFlag = 0;
    retval = NO_ERROR;
    canQueLen = GPS_04_BUF_QUELEN;

    canRxErrtmp = CAN_RxData(VEHICLE_CAN, GPS_04_BUF, &ptrDataBuf);
    if ((canRxErrtmp != CAN_RX_BUFFER_EMPTY) && (canRxErrtmp != CAN_BUSOFF))
    {
        NRxFrWSInfo++;
        if (canRxErrtmp == CAN_RX_BUFFER_OVERRUN)
        {
            retval = CAN_RX_BUFFER_OVERRUN;
        }
        else
        {
            retval = NO_ERROR; 
        }
        while ((endFifoFlag == 0) && (NRxFrWSInfo < canQueLen))
        {
            if (CAN_RxData(VEHICLE_CAN, GPS_04_BUF, &pDataBufNew) == CAN_RX_BUFFER_EMPTY)
            {
                endFifoFlag = 1;
            }
            else
            {
                NRxFrWSInfo++;
                ptrDataBuf = pDataBufNew; // save the previous pointer
            }
        }

        /* ORDINAMENTO DEL MESSAGGIO */
        GPS04->Byte0.R = ptrDataBuf->b[0]; // data0
        GPS04->Byte1.R = ptrDataBuf->b[1]; // data1
        GPS04->Byte2.R = ptrDataBuf->b[2]; // data2
        GPS04->Byte3.R = ptrDataBuf->b[3]; // data3
        GPS04->Byte4.R = ptrDataBuf->b[4]; // data4
        GPS04->Byte5.R = ptrDataBuf->b[5]; // data5
        GPS04->Byte6.R = ptrDataBuf->b[6]; // data6
        GPS04->Byte7.R = ptrDataBuf->b[7]; // data7

        #if 0
        /* CHECK LIVENESS */
        tmpWorker = GPS04->Byte2.B.DsAliveCounter2;
        if (tmpWorker == ((aliveCounterGP4 + 1) & 0x0F))
        {
            /* NONE */
        }
        else
        {
            FrmLostABS++;
            if (DISCANCNT == 0)
            {
                retval = CAN_RX_CNT_ERR;
            }
            else { /* Skip Diag */ }
        }
        aliveCounterGP4 = (uint8_T)tmpWorker;

        /* CHECK INTEGRITY */
        integrityChk = GPS04->Byte7.B.DsCheckSum7;
        integrityIn = CRC8_SAE_J1850_bit(0u, (uint8_T *) GPS04, 7u);
        #else
        integrityIn = integrityChk;
        #endif
        if ((integrityIn == integrityChk) || (DISCANCRC != 0))
        {
            /* ELABORAZIONE MESSAGGIO */
            
            // Error GPS
            ErrorGPS = GPS04->Byte0.B.GPS_Error0;

            // Lock GPS
            FixGPS = GPS04->Byte0.B.GPS_Error0;

            /* POST PROCESSING */
            /* */
        }
        else
        {
            retval = CAN_RX_CRC_ERR;
        }
        emptyCounterGP4 = 0;
    }
    else
    {
        if (canRxErrtmp == CAN_RX_BUFFER_EMPTY) 
        {
            if (emptyCounterGP4 >= GPS_04_EMPTY)
            {
                retval = CAN_RX_BUFFER_EMPTY;
            }
            else 
            {
                emptyCounterGP4++;
                retval = CAN_RX_SKIP; 
            }
        }
        else
        {
            retval = CAN_BUSOFF;
            emptyCounterGP4 = 0;
        }
    }
    return retval;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_EGEAR_1M(void)
{
    EGEAR_T *EGEAR = (EGEAR_T *)&Gen05[0];
    int16_T retval;
    uint8_T canQueLen;
    int32_T tmpWorker;
    uint8_T NRxFrWSInfo;
    uint8_T endFifoFlag;
    int16_T canRxErrtmp;
    uint8_T integrityIn = 0;
    uint8_T integrityChk = 0;
    struct CAN_buff *ptrDataBuf  = NULL; // pointer to message data structure
    struct CAN_buff *pDataBufNew = NULL; // aux pointer
    static uint8_T aliveCounterG = 0;
    static uint8_T emptyCounterG = 0;

    NRxFrWSInfo = 0;
    endFifoFlag = 0;
    retval = NO_ERROR;
    canQueLen = E_GEAR_BUF_QUELEN;

    canRxErrtmp = CAN_RxData(PRIVATE_CAN, E_GEAR_BUF, &ptrDataBuf);
    if ((canRxErrtmp != CAN_RX_BUFFER_EMPTY) && (canRxErrtmp != CAN_BUSOFF))
    {
        NRxFrWSInfo++;
        if (canRxErrtmp == CAN_RX_BUFFER_OVERRUN)
        {
            retval = CAN_RX_BUFFER_OVERRUN;
        }
        else
        {
            retval = NO_ERROR; 
        }
        while ((endFifoFlag == 0) && (NRxFrWSInfo < canQueLen))
        {
            if (CAN_RxData(PRIVATE_CAN, E_GEAR_BUF, &pDataBufNew) == CAN_RX_BUFFER_EMPTY)
            {
                endFifoFlag = 1;
            }
            else
            {
                NRxFrWSInfo++;
                ptrDataBuf = pDataBufNew; // save the previous pointer
            }
        }

        /* ORDINAMENTO DEL MESSAGGIO */
        EGEAR->Byte0.R = ptrDataBuf->b[0]; // data0
        EGEAR->Byte1.R = ptrDataBuf->b[1]; // data1
        EGEAR->Byte2.R = ptrDataBuf->b[2]; // data2
        EGEAR->Byte3.R = ptrDataBuf->b[3]; // data3
        EGEAR->Byte4.R = ptrDataBuf->b[4]; // data4
        EGEAR->Byte5.R = ptrDataBuf->b[5]; // data5
        EGEAR->Byte6.R = ptrDataBuf->b[6]; // data6
        EGEAR->Byte7.R = ptrDataBuf->b[7]; // checksum

        /* CHECK LIVENESS */
        tmpWorker = EGEAR->Byte6.B.EgAliveCounter6;
        if (tmpWorker == ((aliveCounterG + 1) & 0x0F))
        {
            /* NONE */
        }
        else
        {
            FrmLostEGear++;
            if (DISCANCNT == 0)
            {
                retval = CAN_RX_CNT_ERR;
            }
            else { /* Skip Diag */ }
        }
        aliveCounterG = (uint8_T)tmpWorker;

        /* CHECK INTEGRITY */
        integrityChk = EGEAR->Byte7.B.EgCheckSum7;
        integrityIn = CRC8_SAE_J1850_bit(0u, (uint8_T *) EGEAR, 7u);
        if ((integrityIn == integrityChk) || (DISCANCRC != 0))
        {
            /* ELABORAZIONE MESSAGGIO */
            
            // Cyborg Status
            GearShiftWaitCAN = EGEAR->Byte0.B.GearShiftWait0;

            // Down request
            DownShiftReqCAN = EGEAR->Byte0.B.DOWN_Request_flag0;

            // Neutral request
            NeutralReqCAN = EGEAR->Byte0.B.NEUTRAL_Request_flag0;

            // Up request
            UpShiftReqCAN = EGEAR->Byte0.B.UP_Request_flag0;

            // Validity
            VDEGearDigCAN = EGEAR->Byte0.B.VD_request_flag0;

            // Validity
            VDEGearAnalogCAN = EGEAR->Byte0.B.VD_VGearPos_CAN0;

            // VGear
            tmpWorker = (EGEAR->Byte5.B.VGearPos_CAN5 << 4);
            tmpWorker = tmpWorker | (EGEAR->Byte6.B.VGearPos_CAN6);
            VGearPosCAN = (uint16_T)tmpWorker;

            /* POST PROCESSING */
            if (EECANNode4En == 0)
            {
                if (CntCANNode4En >= NMSGCANNODE4EN)
                {
                    EECANNode4En = 1;
                    SETBIT(VehOptConfig, 2);
                    CntCANNode4En = 0;
                }
                else
                {
                    CntCANNode4En++;
                }
            }
            else { /* Skip */ }
        }
        else
        {
            retval = CAN_RX_CRC_ERR;
        }
        emptyCounterG = 0;
    }
    else
    {
        if (canRxErrtmp == CAN_RX_BUFFER_EMPTY) 
        {
            if (emptyCounterG >= EGEAR_EMPTY)
            {
                retval = CAN_RX_BUFFER_EMPTY;
            }
            else 
            {
                emptyCounterG++;
                retval = CAN_RX_SKIP; 
            }
        }
        else
        {
            retval = CAN_BUSOFF;
            emptyCounterG = 0;
        }
    }
    return retval;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_AXISPOSITION1(void)
{
    AXP1_T *AXP1 = (AXP1_T *)&Gen10[0];
    uint8_T NRxFrWSInfo;
    int32_T tmpWorker;
    int32_T tmpWorker2;
    struct CAN_buff *ptrDataBuf  = NULL;     // pointer to message data structure
    struct CAN_buff *pDataBufNew = NULL;     // aux pointer
    int16_T CanRxErrtmp;
    int16_T canQueLen;
    uint8_T endFifoFlag;
    uint8_T integrityIn = 0;
    uint8_T integrityChk = 0;
    static uint8_T cntValidCANDiagAx1 = 0;

    NRxFrWSInfo = 0;
    canQueLen = AXS_POSITION_1_BUF_QUELEN;

    CanRxErrtmp = CAN_RxData(VEHICLE_CAN, AXS_POSITION_1_BUF, &ptrDataBuf);

    if ((CanRxErrtmp != CAN_RX_BUFFER_EMPTY) &&  (CanRxErrtmp != CAN_BUSOFF))
    {
        if (CanRxErrtmp == CAN_RX_BUFFER_OVERRUN)
        {
            /* CanRxErrAxsPos1 = CAN_RX_BUFFER_OVERRUN; */
            CanRxErrAxsPos1 = NO_ERROR; /* WA (EBO-774) */
        } 
        else // no errors  
        {
            CanRxErrAxsPos1 = NO_ERROR; 
        }
        ++NRxFrWSInfo;
        endFifoFlag = 0;
        while ((endFifoFlag == 0) && (NRxFrWSInfo < canQueLen))
        {
            if (CAN_RxData(VEHICLE_CAN, AXS_POSITION_1_BUF, &pDataBufNew) == CAN_RX_BUFFER_EMPTY)
            {
                endFifoFlag = 1;
            }
            else
            {
                ++NRxFrWSInfo;
                ptrDataBuf = pDataBufNew; // save the previous pointer
            }
        }

        /* ORDINAMENTO DEL MESSAGGIO */
        AXP1->Byte0.R = ptrDataBuf->b[0]; // data0
        AXP1->Byte1.R = ptrDataBuf->b[1]; // data1
        AXP1->Byte2.R = ptrDataBuf->b[2]; // data2
        AXP1->Byte3.R = ptrDataBuf->b[3]; // data3
        AXP1->Byte4.R = ptrDataBuf->b[4]; // data4
        AXP1->Byte5.R = ptrDataBuf->b[5]; // data5
        AXP1->Byte6.R = ptrDataBuf->b[6]; // data6
        AXP1->Byte7.R = ptrDataBuf->b[7]; // data7

        #if 0
        /* CHECK LIVENESS */
        tmpWorker = AXP1->Byte2.B.DsAliveCounter2;
        if (tmpWorker == ((aliveCounterA + 1) & 0x0F))
        {
            /* NONE */
        }
        else
        {
            FrmLostAXP1++;
            if (DISCANCNT == 0)
            {
                retval = CAN_RX_CNT_ERR;
            }
            else { /* Skip Diag */ }
        }
        aliveCounterA = tmpWorker;

        /* CHECK INTEGRITY */
        integrityChk = AXP1->Byte7.B.DsCheckSum7;
        integrityIn = CRC8_SAE_J1850_bit(0u, (uint8_T *) AXP1, 7u);
        #else
        integrityIn = integrityChk;
        #endif
        if ((integrityIn == integrityChk) || (DISCANCRC != 0))
        {
            /* ELABORAZIONE DEL MESSAGGIO */
            
            // Az
            tmpWorker = AXP1->Byte0.B.AzCAN0;
            tmpWorker2 = AXP1->Byte1.B.AzCAN1;
            tmpWorker = (tmpWorker << 8) | tmpWorker2;
            tmpWorker = (tmpWorker * AZ_CAN_GAIN);
            tmpWorker = tmpWorker + AZ_CAN_OFFSET;
            tmpWorker = max(tmpWorker, AZ_CAN_MIN);
            AzCAN = min(tmpWorker, AZ_CAN_MAX);

            // Ay
            tmpWorker = AXP1->Byte2.B.AyCAN2;
            tmpWorker2 = AXP1->Byte3.B.AyCAN3;
            tmpWorker = (tmpWorker << 8) | tmpWorker2;
            tmpWorker = (tmpWorker * AY_CAN_GAIN);
            tmpWorker = tmpWorker + AY_CAN_OFFSET;
            tmpWorker = max(tmpWorker, AY_CAN_MIN);
            AyCAN = min(tmpWorker, AY_CAN_MAX);

            // Ax
            tmpWorker = AXP1->Byte4.B.AxCAN4;
            tmpWorker2 = AXP1->Byte5.B.AxCAN5;
            tmpWorker = (tmpWorker << 8) | tmpWorker2;
            tmpWorker = (tmpWorker * AX_CAN_GAIN);
            tmpWorker = tmpWorker + AX_CAN_OFFSET;
            tmpWorker = max(tmpWorker, AX_CAN_MIN);
            AxCAN = min(tmpWorker, AX_CAN_MAX);

            /* POST PROCESSING */
            /* */
        }
        else
        {
            CanRxErrAxsPos1 = CAN_RX_CRC_ERR;
        }
    }
    else if (CanRxErrtmp == CAN_RX_BUFFER_EMPTY) 
    {
        CanRxErrAxsPos1 = CAN_RX_BUFFER_EMPTY;
    }
    else
    {
        /**/
    }
    if ((CntNoDiagAfterKeyOn == 0) && (CanRxErrAxsPos1 != NO_ERROR) && (VBattery >= CANVBATTHRMIN) && (EECANNode2En != 0))
    {
        if (cntValidCANDiagAx1 >= DIV_ELEAN_CAN_MSG_10MS)
        {
            ValidCANDiagAx1 = CanRxErrAxsPos1;
        }
        else
        {
            cntValidCANDiagAx1++;
        }
    }
    else
    {
        cntValidCANDiagAx1 = 0;
        ValidCANDiagAx1 = NO_ERROR;
    }
    return 0;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_AXISPOSITION2(void)
{
    AXP2_T *AXP2 = (AXP2_T *)&Gen10[0];
    uint8_T NRxFrWSInfo;
    int32_T tmpWorker;
    int32_T tmpWorker2;
    struct CAN_buff *ptrDataBuf  = NULL;     // pointer to message data structure
    struct CAN_buff *pDataBufNew = NULL;     // aux pointer
    int16_T CanRxErrtmp;
    int16_T canQueLen;
    uint8_T endFifoFlag;
    uint8_T integrityIn = 0;
    uint8_T integrityChk = 0;
    static uint8_T cntValidCANDiagAx2 = 0;

    NRxFrWSInfo = 0;
    canQueLen = AXS_POSITION_2_BUF_QUELEN;

    CanRxErrtmp = CAN_RxData(VEHICLE_CAN, AXS_POSITION_2_BUF, &ptrDataBuf);

    if ((CanRxErrtmp != CAN_RX_BUFFER_EMPTY) &&  (CanRxErrtmp != CAN_BUSOFF))
    {
        if (CanRxErrtmp == CAN_RX_BUFFER_OVERRUN)
        {
            /* CanRxErrAxsPos2 = CAN_RX_BUFFER_OVERRUN; */
            CanRxErrAxsPos2 = NO_ERROR; /* WA (EBO-774) */
        } 
        else // no errors  
        {
            CanRxErrAxsPos2 = NO_ERROR; 
        }
        ++NRxFrWSInfo;
        endFifoFlag = 0;
        while ((endFifoFlag == 0) && (NRxFrWSInfo < canQueLen))
        {
            if (CAN_RxData(VEHICLE_CAN, AXS_POSITION_2_BUF, &pDataBufNew) == CAN_RX_BUFFER_EMPTY)
            {
                endFifoFlag = 1;
            }
            else
            {
                ++NRxFrWSInfo;
                ptrDataBuf = pDataBufNew; // save the previous pointer
            }
        }

        /* ORDINAMENTO DEL MESSAGGIO */
        AXP2->Byte0.R = ptrDataBuf->b[0]; // data0
        AXP2->Byte1.R = ptrDataBuf->b[1]; // data1
        AXP2->Byte2.R = ptrDataBuf->b[2]; // data2
        AXP2->Byte3.R = ptrDataBuf->b[3]; // data3
        AXP2->Byte4.R = ptrDataBuf->b[4]; // data4
        AXP2->Byte5.R = ptrDataBuf->b[5]; // data5
        AXP2->Byte6.R = ptrDataBuf->b[6]; // data6
        AXP2->Byte7.R = ptrDataBuf->b[7]; // data7

        #if 0
        /* CHECK LIVENESS */
        tmpWorker = AXP2->Byte2.B.DsAliveCounter2;
        if (tmpWorker == ((aliveCounterA + 1) & 0x0F))
        {
            /* NONE */
        }
        else
        {
            FrmLostAXP2++;
            if (DISCANCNT == 0)
            {
                retval = CAN_RX_CNT_ERR;
            }
            else { /* Skip Diag */ }
        }
        aliveCounterA = tmpWorker;

        /* CHECK INTEGRITY */
        integrityChk = AXP2->Byte7.B.DsCheckSum7;
        integrityIn = CRC8_SAE_J1850_bit(0u, (uint8_T *) AXP2, 7u);
        #else
        integrityIn = integrityChk;
        #endif
        if ((integrityIn == integrityChk) || (DISCANCRC != 0))
        {
            /* ELABORAZIONE DEL MESSAGGIO */
            // Gz
            tmpWorker = AXP2->Byte0.B.GzCAN0;
            tmpWorker2 = AXP2->Byte1.B.GzCAN1;
            tmpWorker = (tmpWorker << 8) | tmpWorker2;
            tmpWorker = (tmpWorker * WZ_CAN_GAIN);
            tmpWorker = tmpWorker + WZ_CAN_OFFSET;
            tmpWorker = max(tmpWorker, WZ_CAN_MIN);
            WzCAN = min(tmpWorker, WZ_CAN_MAX);

            // Gy
            tmpWorker = AXP2->Byte2.B.GyCAN2;
            tmpWorker2 = AXP2->Byte3.B.GyCAN3;
            tmpWorker = (tmpWorker << 8) | tmpWorker2;
            tmpWorker = (tmpWorker * WY_CAN_GAIN);
            tmpWorker = tmpWorker + WY_CAN_OFFSET;
            tmpWorker = max(tmpWorker, WY_CAN_MIN);
            WyCAN = min(tmpWorker, WY_CAN_MAX);

            // Gx
            tmpWorker = AXP2->Byte4.B.GxCAN4;
            tmpWorker2 = AXP2->Byte5.B.GxCAN5;
            tmpWorker = (tmpWorker << 8) | tmpWorker2;
            tmpWorker = (tmpWorker * WX_CAN_GAIN);
            tmpWorker = tmpWorker + WX_CAN_OFFSET;
            tmpWorker = max(tmpWorker, WX_CAN_MIN);
            WxCAN = min(tmpWorker, WX_CAN_MAX);

            /* POST PROCESSING */
            /* */
        }
        else
        {
            CanRxErrAxsPos2 = CAN_RX_CRC_ERR;
        }
    }
    else if (CanRxErrtmp == CAN_RX_BUFFER_EMPTY) 
    {
        CanRxErrAxsPos2 = CAN_RX_BUFFER_EMPTY;
    }
    else
    {
        /**/
    }
    if ((CntNoDiagAfterKeyOn == 0) && (CanRxErrAxsPos2 != NO_ERROR) && (VBattery >= CANVBATTHRMIN) && (EECANNode2En != 0))
    {
        if (cntValidCANDiagAx2 >= DIV_ELEAN_CAN_MSG_10MS)
        {
            ValidCANDiagAx2 = CanRxErrAxsPos2;
        }
        else
        {
            cntValidCANDiagAx2++;
        }
    }
    else
    {
        cntValidCANDiagAx2 = 0;
        ValidCANDiagAx2 = NO_ERROR;
    }
    return 0;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_AXISPOSITION3(void)
{
    AXP3_T *AXP3 = (AXP3_T *)&Gen10[0];
    uint8_T NRxFrWSInfo;
    int32_T tmpWorker;
    int32_T tmpWorker2;
    struct CAN_buff *ptrDataBuf = NULL;     // pointer to message data structure
    struct CAN_buff *pDataBufNew = NULL;     // aux pointer
    int16_T CanRxErrtmp;
    int16_T canQueLen;
    uint8_T endFifoFlag;
    uint8_T integrityIn = 0;
    uint8_T integrityChk = 0;
    static uint8_T cntValidCANDiagAx3 = 0;

    NRxFrWSInfo = 0;
    canQueLen = AXS_POSITION_3_BUF_QUELEN;

    CanRxErrtmp = CAN_RxData(VEHICLE_CAN, AXS_POSITION_3_BUF, &ptrDataBuf);

    if ((CanRxErrtmp != CAN_RX_BUFFER_EMPTY) &&  (CanRxErrtmp != CAN_BUSOFF))
    {
        if (CanRxErrtmp == CAN_RX_BUFFER_OVERRUN)
        {
            CanRxErrAxsPos3 = CAN_RX_BUFFER_OVERRUN;
        } 
        else // no errors  
        {
            CanRxErrAxsPos3 = NO_ERROR; 
        }
        ++NRxFrWSInfo;
        endFifoFlag = 0;
        while ( (endFifoFlag == 0) && (NRxFrWSInfo < canQueLen))
        {
            if (CAN_RxData(VEHICLE_CAN, AXS_POSITION_3_BUF, &pDataBufNew) == CAN_RX_BUFFER_EMPTY)
            {
                endFifoFlag = 1;
            }
            else
            {
                ++NRxFrWSInfo;
                ptrDataBuf = pDataBufNew; // save the previous pointer
            }
        }

        /* ORDINAMENTO DEL MESSAGGIO */
        AXP3->Byte0.R = ptrDataBuf->b[0]; // data0
        AXP3->Byte1.R = ptrDataBuf->b[1]; // data1
        AXP3->Byte2.R = ptrDataBuf->b[2]; // data2
        AXP3->Byte3.R = ptrDataBuf->b[3]; // data3
        AXP3->Byte4.R = ptrDataBuf->b[4]; // data4
        AXP3->Byte5.R = ptrDataBuf->b[5]; // data5
        AXP3->Byte6.R = ptrDataBuf->b[6]; // data6
        AXP3->Byte7.R = ptrDataBuf->b[7]; // checksum

        #if 0
        /* CHECK LIVENESS */
        tmpWorker = AXP3->Byte4.B.EL3AliveCounter4;
        if (tmpWorker == ((aliveCounterEL3 + 1) & 0x0F))
        {
            /* NONE */
        }
        else
        {
            FrmLostAXP3++;
            if (DISCANCNT == 0)
            {
                retval = CAN_RX_CNT_ERR;
            }
            else { /* Skip Diag */ }
        }
        aliveCounterEL3 = tmpWorker;
        #endif

        /* CHECK INTEGRITY */
        integrityChk = AXP3->Byte7.B.EL3Integrity7;
        integrityIn = CRC8_SAE_J1850_bit(0u, (uint8_T *) AXP3, 7u);
        if ((integrityIn == integrityChk) || (DISCANCRC != 0))
        {
            /* ELABORAZIONE DEL MESSAGGIO */
            // Pitch
            tmpWorker = AXP3->Byte0.B.PitchCAN0;
            tmpWorker2 = AXP3->Byte1.B.PitchCAN1;
            tmpWorker = (tmpWorker << 8) | tmpWorker2;
            PitchRawCAN = tmpWorker;
            tmpWorker = (tmpWorker * PITCH_CAN_GAIN);
            tmpWorker = tmpWorker + PITCH_CAN_OFFSET;
            tmpWorker = max(tmpWorker, PITCH_CAN_MIN);
            PitchCAN = min(tmpWorker, PITCH_CAN_MAX);

            // Roll
            tmpWorker = AXP3->Byte2.B.RollCAN2;
            tmpWorker2 = AXP3->Byte3.B.RollCAN3;
            tmpWorker = (tmpWorker << 8) | tmpWorker2;
            RollRawCAN = tmpWorker;
            tmpWorker = (tmpWorker * ROLL_CAN_GAIN);
            tmpWorker = tmpWorker + ROLL_CAN_OFFSET;
            tmpWorker = max(tmpWorker, ROLL_CAN_MIN);
            RollCAN = min(tmpWorker, ROLL_CAN_MAX);
            
            // VD Roll
            if (FOVDROLLCAN > 0)
            {
                VDRollCAN = 1;
            }
            else if (FOVDROLLCAN == 0)
            {
                VDRollCAN = 0;
            }
            else
            {
                VDRollCAN = AXP3->Byte4.B.VDRollCAN4;
            }

            // VD Pitch
            if (FOVDPITCHCAN > 0)
            {
                VDPitchCAN = 1;
            }
            else if (FOVDPITCHCAN == 0)
            {
                VDPitchCAN = 0;
            }
            else
            {
                VDPitchCAN = AXP3->Byte4.B.VDPitchCAN4;
            }

            /* POST PROCESSING */
            AbsRollCAN = abs(RollCAN);

            if (EECANNode2En == 0)
            {
                if (CntCANNode2En >= NMSGCANNODE2EN)
                {
                    EECANNode2En = 1;
                    SETBIT(VehOptConfig, 1);
                    CntCANNode2En = 0;
                }
                else
                {
                    CntCANNode2En++;
                }
            }
            else { /* MISRA */ }
        }
        else
        {
            CanRxErrAxsPos3 = CAN_RX_CRC_ERR;
        } 
    }
    else if (CanRxErrtmp == CAN_RX_BUFFER_EMPTY) 
    {
        CanRxErrAxsPos3 = CAN_RX_BUFFER_EMPTY;
    }
    else
    {
        /**/
    }
    if ((CntNoDiagAfterKeyOn == 0) && (CanRxErrAxsPos3 != NO_ERROR) && (VBattery >= CANVBATTHRMIN)&& (EECANNode2En != 0))
    {
        if (cntValidCANDiagAx3 >= DIV_ELEAN_CAN_MSG_10MS)
        {
            ValidCANDiagAx3 = CanRxErrAxsPos3;
        }
        else
        {
            cntValidCANDiagAx3++;
        }
    }
    else
    {
        cntValidCANDiagAx3 = 0;
        ValidCANDiagAx3 = NO_ERROR;
    }
    return 0;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_TPMS(void)
{
    TPMS_T *TPMS = (TPMS_T *)&Gen100[0];
    int16_T retval;
    uint8_T canQueLen;
    int32_T tmpWorker;
    uint8_T NRxFrWSInfo;
    uint8_T endFifoFlag;
    int16_T canRxErrtmp;
    uint8_T integrityIn = 0;
    uint8_T integrityChk = 0;
    struct CAN_buff *ptrDataBuf  = NULL; // pointer to message data structure
    struct CAN_buff *pDataBufNew = NULL; // aux pointer
    static uint8_T aliveCounterT = 0;
    static uint8_T emptyCounterT = 0;

    NRxFrWSInfo = 0;
    endFifoFlag = 0;
    retval = NO_ERROR;
    canQueLen = TPMS_BUF_QUELEN;

    canRxErrtmp = CAN_RxData(VEHICLE_CAN, TPMS_BUF, &ptrDataBuf);
    if ((canRxErrtmp != CAN_RX_BUFFER_EMPTY) && (canRxErrtmp != CAN_BUSOFF))
    {
        NRxFrWSInfo++;
        if (canRxErrtmp == CAN_RX_BUFFER_OVERRUN)
        {
            retval = CAN_RX_BUFFER_OVERRUN;
        }
        else
        {
            retval = NO_ERROR; 
        }
        while ((endFifoFlag == 0) && (NRxFrWSInfo < canQueLen))
        {
            if (CAN_RxData(VEHICLE_CAN, TPMS_BUF, &pDataBufNew) == CAN_RX_BUFFER_EMPTY)
            {
                endFifoFlag = 1;
            }
            else
            {
                NRxFrWSInfo++;
                ptrDataBuf = pDataBufNew; // save the previous pointer
            }
        }

        /* ORDINAMENTO DEL MESSAGGIO */
        TPMS->Byte0.R = ptrDataBuf->b[0]; // data0
        TPMS->Byte1.R = ptrDataBuf->b[1]; // data1
        TPMS->Byte2.R = ptrDataBuf->b[2]; // data2
        TPMS->Byte3.R = ptrDataBuf->b[3]; // data3
        TPMS->Byte4.R = ptrDataBuf->b[4]; // data4
        TPMS->Byte5.R = ptrDataBuf->b[5]; // data5
        TPMS->Byte6.R = ptrDataBuf->b[6]; // data6
        TPMS->Byte7.R = ptrDataBuf->b[7]; // checksum

        #if 0
        /* CHECK LIVENESS */
        tmpWorker = TPMS->Byte5.B.DsiAliveCounter5;
        if (tmpWorker == ((aliveCounterT + 1) & 0x0F))
        {
            /* NONE */
        }
        else
        {
            FrmLostTPMS++;
            if (DISCANCNT == 0)
            {
                retval = CAN_RX_CNT_ERR;
            }
            else { /* Skip Diag */ }
        }
        aliveCounterT = (uint8_T)tmpWorker;

        /* CHECK INTEGRITY */
        integrityChk = TPMS->Byte7.B.DsiCheckSum7;
        integrityIn = CRC8_SAE_J1850_bit(0u, (uint8_T *) TPMS, 7u);
        #else
        integrityIn = integrityChk;
        #endif
        if ((integrityIn == integrityChk) || (DISCANCRC != 0))
        {
            /* ELABORAZIONE MESSAGGIO */
            
            // LOC
            TPMSLoc = TPMS->Byte0.B.LOC0;

            // PRE
            TPMSPre = TPMS->Byte1.B.PRE1;

            // TEM
            TPMSTem = TPMS->Byte2.B.TEM2;

            // WFC
            TPMSWfc = TPMS->Byte3.B.WFC3;

            // BAL
            TPMSBal = TPMS->Byte3.B.BAL3;

            // RST
            TPMSRst = TPMS->Byte4.B.RST4;

            // TIM
            tmpWorker = TPMS->Byte5.B.TIM5;
            tmpWorker = ((tmpWorker) | (TPMS->Byte6.B.TIM6 << 8));
            TPMSTim = (uint16_T)tmpWorker;

            // COM
            TPMSCom = TPMS->Byte7.B.COM7;

            // SDA
            TPMSSda = TPMS->Byte7.B.SDA7;

            // TTD
            TPMSTtd = TPMS->Byte7.B.TTD7;

            // PDT
            TPMSPdt = TPMS->Byte7.B.PDT7;

            /* POST PROCESSING */
            TPMSNode = 1; // Presence.

            if (EECANNode5En == 0)
            {
                if (CntCANNode5En >= NMSGCANNODE5EN)
                {
                    EECANNode5En = 1;
                    SETBIT(VehOptConfig, 3);
                    CntCANNode5En = 0;
                }
                else
                {
                    CntCANNode5En++;
                }
            }
            else { /* MISRA */ }
        }
        else
        {
            retval = CAN_RX_CRC_ERR;
        }
        emptyCounterT = 0;
    }
    else
    {
        if (canRxErrtmp == CAN_RX_BUFFER_EMPTY) 
        {
            if (emptyCounterT >= TPMS_EMPTY)
            {
                retval = CAN_RX_BUFFER_EMPTY;
            }
            else 
            {
                emptyCounterT++;
                retval = CAN_RX_SKIP; 
            }
        }
        else
        {
            retval = CAN_BUSOFF;
            emptyCounterT = 0;
        }
    }
    return retval;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_Recv_VIN_CODE_DASH(void)
{
    uint8_T NRxFrWSInfo;
    struct CAN_buff *ptrDataBuf = NULL;     // pointer to message data structure
    struct CAN_buff *pDataBufNew = NULL;     // aux pointer
    int16_T CanRxErrtmp;
    int16_T canQueLen;
    uint8_T endFifoFlag;

    NRxFrWSInfo = 0;
    canQueLen = VIN_CODE_DASH_BUF_QUELEN;

    CanRxErrtmp = CAN_RxData(VEHICLE_CAN, VIN_CODE_DASH_BUF, &ptrDataBuf);

    if ((CanRxErrtmp != CAN_RX_BUFFER_EMPTY) &&  (CanRxErrtmp != CAN_BUSOFF))
    {
        if (CanRxErrtmp == CAN_RX_BUFFER_OVERRUN)
        {
            CanRxErrVinCodeDASH = CAN_RX_BUFFER_OVERRUN;
        } 
        else // no errors  
        {
            CanRxErrVinCodeDASH = NO_ERROR; 
        }
        ++NRxFrWSInfo;
        endFifoFlag = 0;
        while ((endFifoFlag == 0) && (NRxFrWSInfo < canQueLen))
        {
            if (CAN_RxData(VEHICLE_CAN, VIN_CODE_DASH_BUF, &pDataBufNew) == CAN_RX_BUFFER_EMPTY)
            {
                endFifoFlag = 1;
            }
            else
            {
                ++NRxFrWSInfo;
                ptrDataBuf = pDataBufNew; // save the previous pointer
            }
        }

        /* ELABORAZIONE DEL MESSAGGIO */
        if (ptrDataBuf->b[0] == 0)
        {
            DashVIN[0] = ptrDataBuf->b[1];
            DashVIN[1] = ptrDataBuf->b[2];
            DashVIN[2] = ptrDataBuf->b[3];
            DashVIN[3] = ptrDataBuf->b[4];
            DashVIN[4] = ptrDataBuf->b[5];
            DashVIN[5] = ptrDataBuf->b[6];
            DashVIN[6] = ptrDataBuf->b[7];
            if (FlgWaitDashVINEchoReply == 1)
            {
                FlgDashVINEchoReply_B0 = 1;
            }
            else
            {
                FlgDashVIN_B0 = 1;
            }
        }
        else if (ptrDataBuf->b[0] == 1)
        {
            DashVIN[7] = ptrDataBuf->b[1];
            DashVIN[8] = ptrDataBuf->b[2];
            DashVIN[9] = ptrDataBuf->b[3];
            DashVIN[10] = ptrDataBuf->b[4];
            DashVIN[11] = ptrDataBuf->b[5];
            DashVIN[12] = ptrDataBuf->b[6];
            DashVIN[13] = ptrDataBuf->b[7];
            if (FlgWaitDashVINEchoReply == 1)
            {
                FlgDashVINEchoReply_B1 = 1;
            }
            else
            {
                FlgDashVIN_B1 = 1;
            }
        }
        else if (ptrDataBuf->b[0] == 2)
        {
            DashVIN[14] = ptrDataBuf->b[1];
            DashVIN[15] = ptrDataBuf->b[2];
            DashVIN[16] = ptrDataBuf->b[3];
            if (FlgWaitDashVINEchoReply == 1)
            {
                FlgDashVINEchoReply_B2 = 1;
            }
            else
            {
                FlgDashVIN_B2 = 1;
            }
        }
        else
        {
            /* Implement Diag */
        }
    }
    else if (CanRxErrtmp == CAN_RX_BUFFER_EMPTY) 
    {
        CanRxErrVinCodeDASH = CAN_RX_BUFFER_EMPTY;
    }
    else
    {
        /**/
    }
    if ((CntNoDiagAfterKeyOn == 0) && (CanRxErrVinCodeDASH != NO_ERROR) && (VBattery >= CANVBATTHRMIN))
    {
        /* Implement Diag */
    }
    else
    {
        /* Implement Diag */
    }

    /*REQ_MV_EM2.0_AT_20:
    If the value of calibration �ATEnable� is set to 0(RLI 0xB8=0x02) , the value of RLI 0xBD "VIN Dashboard" shall be
    set to 0xFF.
    */
    if (ATEnable == 0)
    {
        memset(DashVIN, 255, sizeof(DashVIN));
    }
    else { /* MISRA */ }
    return 0;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_DASH_Data(void)
{
    if (KeySignal == 0)
    {   // Off

    }
    else 
    {   // Diag
        // -> AwLevelCAN
        // -> QuickShiftEnCAN
        FlgLowFuel = 1;
        // -> SetAccSens
        // -> SetMaxTrq
        // -> SetEngBrake
        // -> SetEngResp
        // -> SetRpmLim
        // -> BKLStatusCAN
        // -> StSuspCAN
        // -> StSuspReqCAN
        LcToExitCAN = 1;
        LcLevelCAN = 0;
        // -> EnVSpeedLimM
        // -> SetTracCtrlCAN
        // -> VehSpeedSetUpCAN
        if (StopSignal != 0)
        {
            /* */
        }
        else { /* MISRA */ }
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_DASH_INFO_Data(void)
{
    if (KeySignal == 0)
    {   // Off
        InitStarterEnCAN = 0;
    }
    else 
    {   // Diag
        // -> TCSetupRequest
        // -> TyreTypeCAN
        InitStarterEnCAN = 0;
        if (StopSignal != 0)
        {
            /* */
        }
        else { /* MISRA */ }
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_ABS_Data(void)
{
    uint8_T i;
    
    if (KeySignal == 0)
    {   // Off

    }
    else 
    {   // Diag
        VDAbsWL = 1;
        VDVehSpeedFrontCAN = 1;
        VDVehSpeedRearCAN = 1;
        VehSpeedFrontCAN = 0;
        VehSpeedFrontNcCAN = 0;
        VehSpeedRearCAN = 0;
        VehSpeedRearNcCAN = 0;
        FrontWheelPresCAN = 0;
        VDFrontWheelPresCAN = 1;
        ABSWarningLamp = 0;
        for (i = 0; i < SIZE_VS_CAN; i++)
        {
            BufVehSpeedRearCAN[SIZE_VS_CAN] = 0;
            BufVehSpeedFrontCAN[SIZE_VS_CAN] = 0;
        }
        if (StopSignal != 0)
        {
            /* */
        }
        else { /* MISRA */ }
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_MCU1_Data(void)
{
    if (KeySignal == 0)
    {   // Off

    }
    else 
    {   // Diag
        TAirCAN = TAir;
        VDTAirCAN = 1;
        #ifdef MCU1
        MainRelCmd = 1;
        #endif
        #ifdef MCU1
        ParkBrakeSignal = 0;
        #endif
        #ifdef MCU1
        RiderPresenceCAN = 0;
        #endif
        StarterCmdFdbkCAN = 0;
        VDBrakeSignal = 1;
        BrakeLampCAN = 0;
        if (StopSignal != 0)
        {
            /* */
        }
        else { /* MISRA */ }
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_LHB_Data(void)
{
    if (KeySignal == 0)
    {   // Off
        HornSignalCAN = 0;
    }
    else 
    {   // Diag
        // -> LHsStError
        ClutchSignal1CAN = 1;
        ClutchSignal2CAN = 1;
        HornSignalCAN = 0;
        if (StopSignal != 0)
        {
            /* */
        }
        else { /* MISRA */ }
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_RHB_Data(void)
{
    if (KeySignal == 0)
    {   // Off

    }
    else 
    {   // Diag
        // -> RHsStError
        // -> StopSignal
        StartSignalCAN = 0;
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_GPS_02_Data(void)
{
    if (KeySignal == 0)
    {   // Off

    }
    else 
    {   // Diag
        AltitudeGPS = 0;
        VehSpeedGPS = 0;
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_GPS_04_Data(void)
{
    if (KeySignal == 0)
    {   // Off

    }
    else 
    {   // Diag
        ErrorGPS = 1;
        FixGPS = 0;
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_EGEAR_1M_Data(void)
{
    if (KeySignal == 0)
    {   // Off

    }
    else 
    {   // Diag
        // -> VDEGearDigCAN
        GearShiftWaitCAN = 0;
        DownShiftReqCAN = 0;
        NeutralReqCAN = 0;
        UpShiftReqCAN = 0;
        VGearPosCAN = 1;
        VDEGearAnalogCAN = 1;
        if (StopSignal != 0)
        {
            /* */
        }
        else { /* MISRA */ }
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_AXISPOSITION1_Data(void)
{
    if (KeySignal == 0)
    {   // Off

    }
    else 
    {   // Diag
        VDAzCAN = 1;
        VDAyCAN = 1;
        VDAxCAN = 1;
        AxCAN = 0;
        AyCAN = 0;
        AzCAN = 0;
        if (StopSignal != 0)
        {
            /* */
        }
        else { /* MISRA */ }
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_AXISPOSITION2_Data(void)
{
    if (KeySignal == 0)
    {   // Off

    }
    else 
    {   // Diag
        VDWzCAN = 1;
        VDWyCAN = 1;
        VDWxCAN = 1;
        WxCAN = 0;
        WyCAN = 0;
        WzCAN = 0;
        if (StopSignal != 0)
        {
            /* */
        }
        else { /* MISRA */ }
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_AXISPOSITION3_Data(void)
{
    if (KeySignal == 0)
    {   // Off

    }
    else 
    {   // Diag
        VDPitchCAN = 1;
        VDRollCAN = 1;
        PitchCAN = 0;
        RollCAN = 0;
        AbsRollCAN = 0;
        if (StopSignal != 0)
        {
            /* */
        }
        else { /* MISRA */ }
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Reset_TPMS_Data(void)
{
    if (KeySignal == 0)
    {   // Off

    }
    else 
    {   // Diag
        // -> TPMSTem
        // -> TPMSBal
        // -> TPMSTim
        // -> TPMSCom
        // -> TPMSTtd
        // -> TPMSPdt
        TPMSLoc = 0;
        TPMSPre = 0;
        TPMSWfc = 0;
        TPMSRst = 0;
        TPMSSda = 0;
        TPMSNode = 0;
        // -> All data
        if (StopSignal != 0)
        {
            /* */
        }
        else { /* MISRA */ }
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T CANMGM_BusOffTestAndRec(uint8_T channel, uint8_T diag, StrBusOff *busoffrec)
{
    int16_T ptCanSt = 0;
    uint8_T stDiag = NO_PT_FAULT;
      
    ptCanSt = CAN_GetStatus(channel);
    if (ptCanSt == NO_ERROR)
    {
        DiagMgm_SetDiagState(diag, NO_PT_FAULT, &stDiag);
        busoffrec->cntQuickBusOffRec = 0;
        busoffrec->cntSlowBusOffRec = 0;
        busoffrec->cntBusOffRec = 0u;
        busoffrec->flgRecBusOffRan = 0u;
    }
    else if (ptCanSt == CAN_BUSOFF)
    {
        if (busoffrec->flgRecBusOffRan != 0u)
        {
            // If the on previous call the BusOff recovery ran, and the fault is still present
            // the diagnostic line has tu be updated
            busoffrec->flgRecBusOffRan = 0u;
            if ((CntNoDiagAfterKeyOn == 0) && (VBattery >= CANVBATTHRMIN))
            {
                DiagMgm_SetDiagState(diag, FAULT_CAN_BUSOFF, &stDiag);
            }
            else { /* MISRA */ }
            
            if (stDiag == FAULT)
            {
            
            }
            else { /* MISRA */ }
        }
        
        if (busoffrec->cntBusOffRec < 0xFF)
        {
            // Saturate counter
            busoffrec->cntBusOffRec++;
        }
        else { /* MISRA */ }
        
        if ((busoffrec->cntBusOffRec > BUS_OFF_QUICK_DELAY) && (busoffrec->cntQuickBusOffRec < QUICKBUSOFFRECMAXREP))
        {
            // Test if the 50ms have elapsed and if the Max Quick recovery have already been run
            (void)CAN_BusOffRecovery(channel);    // Run recovery
            busoffrec->cntQuickBusOffRec++;
            // Set Flag for diagnostic Call for nex BusOff Event
            busoffrec->flgRecBusOffRan = 1u;
            busoffrec->cntBusOffRec = 0u;
            
        }
        else if ((busoffrec->cntBusOffRec > BUS_OFF_SLOW_DELAY) && (busoffrec->cntSlowBusOffRec <= SLOWBUSOFFRECMAXREP))
        {
            // Test if the 200ms have elapsed
            (void)CAN_BusOffRecovery(channel);    // Run recovery
            busoffrec->cntSlowBusOffRec++;

            // Set Flag for diagnostic Call for nex BusOff Event
            busoffrec->flgRecBusOffRan = 1u;
            busoffrec->cntBusOffRec = 0u;
        }
        else
        {
            // Delay
        }
    }
    else
    {
        /* errori di CAN_ERR_PASSIVE e CAN_ERR_ACTIVE non gestiti*/
    }
    return ptCanSt;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static uint8_T CANMGM_DiagMsg(uint8_T diagID, uint8_T ptFault)
{
    uint8_T fault = NO_PT_FAULT;
    uint8_T state = NO_FAULT;

    if ((CntNoDiagAfterKeyOn == 0) && (VBattery >= CANVBATTHRMIN))
    {
        if (ptFault == CAN_RX_BUFFER_EMPTY)
        {
            fault = FAULT_CAN_BUFFER_EMPTY;
        }
        else if (ptFault == CAN_BUSOFF)
        {
            fault = FAULT_CAN_BUSOFF;
        }
        else if (ptFault == CAN_RX_BUFFER_OVERRUN)
        {
            fault = FAULT_CAN_BUFFER_OVERRUN;
        }
        else if (ptFault == CAN_RX_CRC_ERR)
        {
            fault = FAULT_CAN_BUFFER_CRC;
        }
        else if (ptFault == CAN_RX_CNT_ERR)
        {
            fault = FAULT_CAN_BUFFER_ALIVE_CNT;
        }
        else
        {
            fault = NO_PT_FAULT;
        }

        if (ptFault != CAN_RX_SKIP)
        {
            DiagMgm_SetDiagState(diagID, fault, &state);
        }
        else { /* SKIP */ }
        
        FlgNoNetworkDiag = 0;
    }
    else 
    {
        FlgNoNetworkDiag = 1;
    }
    return state;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Calc_GearPos (uint8_T *gearPosDashboard, uint8_T gearPos)
{
    static uint8_T gpCounter = 0;
    static uint8_T gpDebuonce = 0;
    static uint8_T gpdStatus = GP_SWITCH_1ST_NEUTRAL;
    static uint8_T oldGearPos = 0;
    if (oldGearPos == gearPos)
    {
        if (gpDebuonce >= TIMGPDEBOUNCE)
        {
            switch (gpdStatus)
            {
                case GP_SWITCH_1ST_NEUTRAL:
                {
                    gpCounter = 0;
                    *gearPosDashboard = gearPos;
                    if (gearPos > 1)
                    {
                        gpdStatus = GP_SWITCH_OTHER;
                    }
                    else
                    {
                        gpdStatus = GP_SWITCH_1ST_NEUTRAL;
                    }
                }
                break;
                case GP_SWITCH_OTHER:
                {
                    if (gearPos > 1)
                    {
                        gpCounter = 0;
                        *gearPosDashboard = gearPos;
                        gpdStatus = GP_SWITCH_OTHER;
                    }
                    else if (gearPos == 1)
                    {
                        gpCounter = 0;
                        *gearPosDashboard = gearPos;
                        gpdStatus = GP_SWITCH_1ST_NEUTRAL;
                    }
                    else
                    {
                        if (gpCounter >= TIMGPCOUNTER)
                        {
                            *gearPosDashboard = gearPos;
                            gpdStatus = GP_SWITCH_1ST_NEUTRAL;
                        }
                        else
                        {
                            gpCounter++;
                        }
                    }
                }
                break;
                default:
                {
                    /* Non fare niente. */
                }
                break;
            }
        }
        else
        {
            gpDebuonce++;    
        }
    }
    else
    {
        gpDebuonce = 0;
        oldGearPos = gearPos;
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_Calc_BrakeSignal(void)
{
    #ifdef  MV7EU5P_BRAKE_PART   // This part comes from MV7EU SW
    if ((VDAbsWL == 1) || (VDBrakeSignal != 0))
    {
        BrakeSignalCAN = BRAKE_INVALID;
        //BrakeSignal = BRAKE_OFF;
    }
    else
    {
        if ((FrontWheelPresCAN > MINFWPRESBRAKE) || (BrakeLampCAN != 0))
        {
            BrakeSignalCAN = BRAKE_ON;
            //BrakeSignal = BRAKE_ON;
        }
        else
        {
            BrakeSignalCAN = BRAKE_OFF;
            //BrakeSignal = BRAKE_OFF;
        }
    }
    #endif


     BrakeSignalCAN = BrakeSignal;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void CANMGM_KeyImmo(void)
{
    static uint8_T McuRRSRetry = 0;

    if (McuKLSReq == RRS_ACK)
    {
        CntKeyImmo = 0;
    }
    else
    {
        if ((McuKLSReq == RRS_STOP) || (McuKLSReq == RRS_NACK))
        {
            McuKLSReq = RRS_STOP;
        }
        else if (CntKeyImmo > TIMWAITIMMO)
        {
            if (McuRRSRetry == 0)
            {
                McuKLSReq = RRS_INIT;
                McuRRSRetry = 1;
                CntKeyImmo = 0;
            }
            else
            {
                McuKLSReq = RRS_STOP;
            }
        }
        else
        {
            CntKeyImmo++;
        }    
    }
}

#endif // _BUILD_CANMGM_950_

