/*****************************************************************************************************************/
/* $HeadURL:: http://***********:8080/svn/Rep_Bo/EM/appl_calib/branches/MV1/tree/DD/VSPEEDMGM/src/vs#        $   */
/* $ Description:                                                                                                */
/* $Revision:: 1898   $                                                                                          */
/* $Date:: 2009-11-03 13:52:49 +0100 (mar, 03 nov 2009)   $                                                      */
/* $Author:: GirasoleG               $                                                                           */
/*****************************************************************************************************************/

#include "typedefs.h"
#include "tasksdefs.h"
#include "vspeedmgm.h"
#include "canmgm.h"
#include "ws.h"
#include "timing.h"
#include "Mpc563m.h"
#include "Mathlib.h"
#include "trc2wzero_out.h"
#include "Utils.h"
#include "idle_mgm.h"

#ifdef  MATLAB_MEX_FILE
#define _BUILD_VSPEEDMGM_
#include "mpc5554_config.h"
#endif

#define DELTA_T_VEH_SPEED 10000
#define KM_CAN_VEHSPEED_FRONT 9  /* 9/10 = 16/17,7777... -> (2^-4) */
#define KD_CAN_VEHSPEED_FRONT 10 /* 9/10 = 16/17,7777... -> (2^-4) */
#define KM_CAN_VEHSPEED_REAR  9  /* 9/10 = 16/17,7777... -> (2^-4) */
#define KD_CAN_VEHSPEED_REAR  10 /* 9/10 = 16/17,7777... -> (2^-4) */

#define INIT_RB_VF 0
#define CALC_RB_VF_INT 1
#define WHEELING_EXEC 2

/* Variabili Uscita */

#ifdef _BUILD_VSPEEDMGM_

/* Calibrazioni */
extern int16_T  THRDVSINF;
extern int16_T  THRDVSSUP;
extern uint16_T THRVSFRTZERO;
extern uint8_T  VSNADVS;
extern uint8_T  VSNAACCR;
extern uint8_T  VSNAACCF;
extern uint8_T  VSNAACCFP;
extern uint8_T  ACCREARRBVFDIM;
extern uint8_T  WHTVSFINT;
extern int16_T  THRRBVFACCDEV;
extern int16_T  THRAXCANACCDEV;
extern int16_T  THRRBVFDVS;
extern uint8_T  ENRBVSFAW;
extern uint8_T  ENRBVSFWE;
extern uint8_T  ENVSKALM;
extern uint8_T  NKALDEEP;
extern int16_T  QKALMANFR;
extern int16_T  RKALMANFR;
extern int16_T  QKALMAN;
extern int16_T  RKALMAN;
extern int16_T  PAPOEST0;
extern int16_T  DBMINAXCAN;
extern int16_T  HYSWHLEXVSFKALF;
extern uint16_T HYSVSFENDAW;
extern uint16_T HYSVSFDRBVFAW;
extern uint8_T  USEA1RSIGNAL;
extern uint8_T  USEA2RSIGNAL; 

extern uint32_T KVSPEEDCALC;

extern uint16_T FRONTWHEELRADIUS[];
extern uint16_T REARWHEELRADIUS[];

extern uint8_T  VEHFRONTSOURCESEL;
extern uint8_T  VEHREARSOURCESEL;

extern uint16_T BKVEHSPEED[BKVEHSPEEDDimension];
extern uint8_T VTPULSETHR[BKVEHSPEEDDimension];
extern uint32_T VEHSTALLPERIOD;

extern uint8_T VTRATIOVSPEED[];

extern uint16_T TBFWHEELRADIUSCORR[BKTYRECORRDimension][BKROLLANGLEDimension];
extern uint16_T TBRWHEELRADIUSCORR[BKTYRECORRDimension][BKROLLANGLEDimension];
extern uint16_T TBFWHEELCENTCORR[BKTYRECORRDimension][BKVCENTCORRDimension];
extern uint16_T TBRWHEELCENTCORR[BKTYRECORRDimension][BKVCENTCORRDimension];
extern uint16_T BKTYRECORR[BKTYRECORRDimension];
extern int16_T  BKROLLANGLE[BKROLLANGLEDimension];
extern uint16_T BKVCENTCORR[BKVCENTCORRDimension];
extern int16_T  BKRTDISTAWKALF[BKRTDISTAWKALF_dim];
extern int16_T  VTRTDISTAWKALF[BKRTDISTAWKALF_dim];

extern uint16_T BKVEHSPEEDRATIOCAN[BKVEHSPEEDCANDimension];
extern uint16_T VTRATIOVSCANDTIME[BKVEHSPEEDCANDimension];

/* Variabili di Test */
uint16_T DEdgeFront, DEdgeRear;
uint32_T DTimeFront, DTimeRear, DTimeRearOld, DTimeRearOld2;
uint16_T VEdgeThrFront;
uint16_T VEdgeThrOldFront;
uint16_T VEdgeThrRear;
uint16_T VEdgeThrOldRear;
uint16_T VehSpeedFront;
uint16_T VehSpeedFrontNc;
uint16_T VehSpeedFrontNz;
uint16_T VehSpeedFrontKalF;
uint16_T VehSpeedRear;
uint16_T VehSpeedRearNc;
uint16_T VehSpeedRearKalF;
int16_T  AccRear;
int16_T  AccRear2;
int16_T  AccRear3;
int16_T  AccFront;
int16_T  AccFront2;
int16_T  AccFront3;
int16_T  DistAWKalF;
int16_T  MinRtAWKalF;
int16_T  MinVehRbVfInt;
uint16_T RatioVSpeedFront;
uint16_T RatioVSpeedFrontNc;
uint16_T RatioVSpeedRear;
uint16_T RatioVSpeedRearNc;
uint16_T FrontWheelRadius;
uint16_T FrontWheelRadiusNc;
uint16_T VehSpeedFrontEndAW;
uint16_T RearWheelRadius;
uint16_T RearWheelRadiusNc;
uint8_T  VehFrontSourceSel;
uint8_T  VehRearSourceSel;
uint16_T VehSpeedFrontAW;
uint16_T IdxVsCAN;
int16_T  DeltaVehRbVfInt;
int16_T  DeltaVehSpeed;
int16_T  DeltaVehSpeedKal;
int16_T  DeltaAccRbVf;
int16_T  AccFrontRbVf;
int16_T  AccRearRbVf;
int16_T  AccRearRbVfAvg;
int16_T  AccDeltaVehSpeed;
uint8_T  FlgDeltaVehSpeedSup;
uint8_T  FlgDeltaVehSpeedInf;
uint8_T  FlgMaxVSFPre;
uint8_T  FlgMaxVSFPost;
uint8_T  StRbVf;

int32_T  VehRbVfInt;
int32_T  VehRbVfAxInt;
int32_T  TmpVehRbVfAxInt;
int16_T  OldVehSpeedRear[VS_BUFF_DEEP];
int16_T  OldVehSpeedFront[VS_BUFF_DEEP];
int16_T  OldDeltaVehSpeed[VS_BUFF_DVS_DEEP];
uint16_T BufVehSpeedRearOld[4];

uint16_T VehSpeedFrontKal[VS_BUFF_KAL_DEEP];
uint16_T VehSpeedRearKal[VS_BUFF_KAL_DEEP];
int16_T  VtXApoEst[VS_BUFF_KAL_DEEP];
int16_T  VtXApriEst[VS_BUFF_KAL_DEEP];
int16_T  VtPApoEst[VS_BUFF_KAL_DEEP];
int16_T  VtPApriEst[VS_BUFF_KAL_DEEP];
int16_T  VtXApoEstFr[VS_BUFF_KAL_DEEP];
int16_T  VtXApriEstFr[VS_BUFF_KAL_DEEP];
int16_T  VtPApoEstFr[VS_BUFF_KAL_DEEP];
int16_T  VtPApriEstFr[VS_BUFF_KAL_DEEP];
int16_T  VtAccRearRbVf[VS_BUFF_ACC_DEEP];
int16_T  QKalman;
int16_T  RKalman;
int16_T  KgKalman;
int16_T  QKalmanFr;
int16_T  RKalmanFr;
int16_T  KgKalmanFr;

timeoutHandler_t stalltimer;

uint8_T flg_novehedge;

uint32_T deltaedge, tmpVehSpeed;
uint64_t deltatime, lastwtimefront, lastwtimerear, lastwtimefrontold, lastwtimerearold;
uint8_T FlgStallFront, FlgStallRear, FirstSpeedCalcFront, FirstSpeedCalcRear;

extern uint8_T EECANNode3En;
extern uint8_T StDiag[];

static int16_T calc_RearAccCAN (uint16_T *bufVehSpeedIn, uint8_T size);
static void calc_RearAcc (uint16_T *buffVehSpeed, uint8_T size, uint16_T vehSpeed);

/* POWER ON TASK */
void VSpeedMgm_Init(void)
{
    uint8_T i;

    VehSpeedFront = 0;
    VehSpeedFrontNc = 0;
    VehSpeedRear = 0;
    VehSpeedRearNc = 0;
    BufVehSpeedRearOld[0] = 0;
    BufVehSpeedRearOld[1] = 0;
    BufVehSpeedRearOld[2] = 0;
    BufVehSpeedRearOld[3] = 0;
    DTimeRear = 0;
    DTimeRearOld = 0;
    DTimeRearOld2 = 0;
    AccRear = 0;
    AccRear2 = 0;
    AccRear3 = 0;
    lastwtimefrontold = 0;
    lastwtimerearold = 0;
    FirstSpeedCalcFront = 1;
    FirstSpeedCalcRear = 1;
    FrontWheelRadius = FRONTWHEELRADIUS[TyreTypeCAN];
    FrontWheelRadiusNc = FRONTWHEELRADIUS[TyreTypeCAN];
    RearWheelRadius = REARWHEELRADIUS[TyreTypeCAN];
    RearWheelRadiusNc = REARWHEELRADIUS[TyreTypeCAN];

    for (i = 0u; i < VS_BUFF_KAL_DEEP; i++)
    {
        VtXApoEst[i] = ((int16_T)(0.0f * 16.0f));
        VtPApoEst[i] = ((int16_T)(1.0f * 4096.0f));
        VtXApriEst[i] = ((int16_T)(0.0f * 16.0f));
        VtPApriEst[i] = ((int16_T)(0.0f * 4096.0f));
        VtXApoEstFr[i] = ((int16_T)(0.0f * 16.0f));
        VtPApoEstFr[i] = ((int16_T)(1.0f * 4096.0f));
        VtXApriEstFr[i] = ((int16_T)(0.0f * 16.0f));
        VtPApriEstFr[i] = ((int16_T)(0.0f * 4096.0f));
    }
    VtPApoEst[0] = PAPOEST0;
    VtPApoEstFr[0] = PAPOEST0;
    QKalman = QKALMAN;
    RKalman = RKALMAN;
    
    if (VEHFRONTSOURCESEL != 0)
    {
        if (VEHFRONTSOURCESEL == 1)
        {
            VehFrontSourceSel = 1; /* ABS Veh_Speed. */
            // if (USEA1RSIGNAL != 0)
            // {
            //     DIGIO_InCfgExt(ITE_Spd_FrontW, DISABLE_PULL);
            // }
            // else { /* MISRA */ }
        }
        else
        {
            VehFrontSourceSel = 0; /* Internal Veh_Speed. */
        }
    }
    else
    {
        if (EECANNode3En != 0)
        {
            VehFrontSourceSel = 1; /* ABS Veh_Speed. */
            // if (USEA1RSIGNAL != 0)
            // {
            //     DIGIO_InCfgExt(ITE_Spd_FrontW, DISABLE_PULL);
            // }
            // else { /* MISRA */ }
        }
        else
        {
            if (USEA1RSIGNAL != 0)
            {
                //DIGIO_InCfgExt(ITE_Spd_FrontW, DISABLE_PULL);
                VehFrontSourceSel = 1; /* ABS Veh_Speed. */
            }
            else
            {
                VehFrontSourceSel = 0; /* Internal Veh_Speed. */
            }
        }
    }

    if (VehFrontSourceSel != 0) /* ABS Veh_Speed. */
    {
        /* Non fare niente, Veh_Speed by CAN */
    }
    else /* Internal Veh_Speed. */
    {
        /* CONFIGURAZIONE SIU PER ACQUISIZIONE VSPEED  */
        EMIOS_InCfg(ITE_Spd_FrontW); // CH_FRONT_WHEEL --> NON FUNZIONA SU ECU 2.4!!!

        /* CONFIGURAZIONE ED ABILITAZIONE DEI CANALI EMIOS PER ACQUISIZIONE VSPEED */
        VEdgeThrFront = (uint16_T)VTPULSETHR[0];
        VEdgeThrOldFront = VEdgeThrFront;    
        WS_Config(CH_FRONT_WHEEL, VEdgeThrFront, SINGLE_RISING_EDGE, RISING_EDGE);
        WS_Enable(CH_FRONT_WHEEL);
        
        WS_SetThreshold(CH_FRONT_WHEEL, VEdgeThrFront);
    }

    if (VEHREARSOURCESEL != 0)
    {
        if (VEHREARSOURCESEL == 1)
        {
            VehRearSourceSel = 1; /* ABS Veh_Speed. */
            if (USEA2RSIGNAL == 1)
            {
                DIGIO_InCfgExt(ITE_Spd_RearW, DISABLE_PULL);
            }
            else if (USEA2RSIGNAL == 2)
            {
                ExhValPwm_Init();
                EMIOS_InCfg(ITE_Spd_RearW);
            }
            else { /* MISRA */ }
        }
        else
        {
            VehRearSourceSel = 0; /* Internal Veh_Speed. */
        }
    }
    else
    {
        if (EECANNode3En != 0)
        {
            VehRearSourceSel = 1; /* ABS Veh_Speed. */
            if (USEA2RSIGNAL == 1)
            {
                DIGIO_InCfgExt(ITE_Spd_RearW, DISABLE_PULL);
            }
            else if (USEA2RSIGNAL == 2)
            {
                ExhValPwm_Init();
                EMIOS_InCfg(ITE_Spd_RearW);
            }
            else { /* MISRA */ }
        }
        else
        {
            if (USEA2RSIGNAL == 1)
            {
                DIGIO_InCfgExt(ITE_Spd_RearW, DISABLE_PULL);
                VehRearSourceSel = 1; /* ABS Veh_Speed. */
            }
            else if (USEA2RSIGNAL == 2)
            {
                ExhValPwm_Init();
                EMIOS_InCfg(ITE_Spd_RearW);
                VehRearSourceSel = 1; /* Internal Veh_Speed. */
            }
            else
            {
                VehRearSourceSel = 0; /* Internal Veh_Speed. */
            }
        }
    }

    if (VehRearSourceSel != 0) /* ABS Veh_Speed. */
    {
        /* Non fare niente, Veh_Speed by CAN */
    }
    else /* Internal Veh_Speed. */
    {
        /* CONFIGURAZIONE SIU PER ACQUISIZIONE VSPEED  */
        EMIOS_InCfg(ITE_Spd_RearW); // CH_REAR_WHEEL

        /* CONFIGURAZIONE ED ABILITAZIONE DEI CANALI EMIOS PER ACQUISIZIONE VSPEED */
        VEdgeThrRear = (uint16_T)VTPULSETHR[0];
        VEdgeThrOldRear = VEdgeThrRear; 
        WS_Config(CH_REAR_WHEEL, VEdgeThrRear, SINGLE_RISING_EDGE, RISING_EDGE);
        WS_Enable(CH_REAR_WHEEL);

        WS_SetThreshold(CH_REAR_WHEEL, VEdgeThrRear);
    }
} /* VSpeedMgm_Init() */

void VSpeedMgm_10ms(void)
{
    int8_T j;
    int16_T k;
    uint8_T i;
    uint8_T idxKmax;
    uint8_T accPlaus;
    uint8_T flgMaxVSFPre;
    uint8_T flgMaxVSFPost;
    int16_T tmpAxCAN;
    int16_T tmpDVSpeed;
    int32_T tmpVehRbVfInt;
    int32_T tmpAccRearRbVf;
    uint16_T tmpVEdgeThr;
    uint16_T tmpCentVSpeed;
    uint16_T tmpRatioVSpeed;
    int16_T  tmpVehSpeedFrontAW;
    uint16_T tmpCalcWheelRadius;
    int32_T  tmpAccDeltaSpd;
    static uint8_T filtOn = 0u;
    static int16_T oldVehSpeedFrontAW = 0;
    static uint16_T tmpVehSpeedFront = 0u;
    static uint16_T tmpVehSpeedRear = 0u;

    if (VehFrontSourceSel != 0) /* ABS Veh_Speed. */
    {
        tmpVehSpeedFront = BufVehSpeedFrontCAN[0];
        VehSpeedFrontNc = VehSpeedFrontNcCAN;
    }
    else /* Internal Veh_Speed. */
    {
        /* CALCOLO RatioVSpeedFront */
        LookUp_U8_U16( &(tmpRatioVSpeed), VTRATIOVSPEED, VehSpeedFrontNc, BKVEHSPEED, (BKVEHSPEEDDimension-1));
        RatioVSpeedFrontNc = tmpRatioVSpeed >> 3;
        
        LookUp_U8_U16( &(tmpRatioVSpeed), VTRATIOVSPEED, tmpVehSpeedFront, BKVEHSPEED, (BKVEHSPEEDDimension-1));
        RatioVSpeedFront = tmpRatioVSpeed >> 3;

        /* LETTURA FRONT WHEEL */
        WS_GetLastDeltaTime(CH_FRONT_WHEEL, &deltatime, &lastwtimefront, &FlgStallFront, VEHSTALLPERIOD);
        WS_GetLastDeltaEdge(CH_FRONT_WHEEL, &deltaedge);

        if (deltatime > 0)
        {
            if ((FlgStallFront) || (deltatime > VEHSTALLPERIOD))
            {
                tmpVehSpeedFront = 0;
                VehSpeedFrontNc = 0;
                FirstSpeedCalcFront = 1;
                /* SATURAZIONE DTimeRear */
                if(deltatime > MAX_uint32_T)
                {
                    DTimeFront = MAX_uint32_T;
                }
                else
                {
                    DTimeFront = (uint32_T)deltatime;
                }
            }
            else if (lastwtimefront != lastwtimefrontold)
            {
                lastwtimefrontold = lastwtimefront;
                /* SATURAZIONE DTimefront */
                if (deltatime > MAX_uint32_T)
                {
                    DTimeFront = MAX_uint32_T;
                }
                else
                {
                    DTimeFront = (uint32_T)deltatime;
                }

                if (deltaedge > MAX_uint16_T)
                {
                    DEdgeFront = MAX_uint16_T;
                }
                else
                {
                    DEdgeFront = (uint16_T) deltaedge;
                }

                /* CALCOLO VELOCITA' */
                Look2D_U16_U16_U16(&tmpCentVSpeed, &(TBFWHEELCENTCORR[0][0]), VehSpeedFrontNc, &(BKVCENTCORR[0]), (BKVCENTCORRDimension-1), TyreTypeCAN, &(BKTYRECORR[0]), (BKTYRECORRDimension-1));
                tmpCalcWheelRadius = (uint16_T)(((uint32_T)FRONTWHEELRADIUS[TyreTypeCAN] * tmpCentVSpeed) >> 15);
                if (FlgYawRec == 1)
                {
                    tmpDVSpeed = (ROLL_REC_15DEG * 100);
                }
                else
                {
                    tmpDVSpeed = abs(RollCAN);
                }
                Look2D_U16_S16_U16(&tmpRatioVSpeed, &(TBFWHEELRADIUSCORR[0][0]), tmpDVSpeed, &(BKROLLANGLE[0]), (BKROLLANGLEDimension-1), TyreTypeCAN, &(BKTYRECORR[0]), (BKTYRECORRDimension-1));
                FrontWheelRadius = (uint16_T)(((uint32_T)tmpCalcWheelRadius * tmpRatioVSpeed) >> 15);

                tmpVehSpeed = (uint32_T)DEdgeFront * FRONTWHEELRADIUS[TyreTypeCAN];
                tmpVehSpeed *= KVSPEEDCALC;
                tmpVehSpeed /= DTimeFront;
                VehSpeedFrontNc = (uint16_T)(tmpVehSpeed);

                tmpVehSpeed = (uint32_T)DEdgeFront * FrontWheelRadius;
                tmpVehSpeed *= KVSPEEDCALC;
                tmpVehSpeed /= DTimeFront;
                tmpVehSpeedFront = (uint16_T)(tmpVehSpeed);

                if (FirstSpeedCalcFront == 1)
                {
                    /* Primo calcolo di velocit� dopo lo stallo */
                    FirstSpeedCalcFront = 0;
                }
                else
                {
                    if (DEdgeFront >= 2)
                    {
                        tmpVehSpeed = DEdgeFront * VehSpeedFrontNc;
                        tmpVehSpeed /= (DEdgeFront-1);
                        tmpVehSpeed = (tmpVehSpeed * RatioVSpeedFrontNc) >> 10;

                        if(VehSpeedFrontNc > ((uint16_T)tmpVehSpeed))
                        {
                            tmpVehSpeed = VehSpeedFrontNc * (DEdgeFront-1);
                            VehSpeedFrontNc = tmpVehSpeed / DEdgeFront;
                        }
                        else
                        {
                            /* Non fare niente. */
                        }
                    
                        tmpVehSpeed = DEdgeFront * tmpVehSpeedFront;
                        tmpVehSpeed /= (DEdgeFront-1);
                        tmpVehSpeed = (tmpVehSpeed * RatioVSpeedFront) >> 10;

                        if(tmpVehSpeedFront > ((uint16_T)tmpVehSpeed))
                        {
                            tmpVehSpeed = tmpVehSpeedFront * (DEdgeFront-1);
                            tmpVehSpeedFront = tmpVehSpeed / DEdgeFront;
                        }
                        else
                        {
                            /* Non fare niente. */
                        }
                    }
                    else
                    {
                        /* Non fare niente. */
                    }
                }
            }
            else
            {
                /* Fase di conteggio fronti; non necessario calcolare la velocit� */
            }
        }
        else
        {
            /* Errore, Velocit� non calcolabile. */
        }

        /* AGGIORNAMENTO NUMERO DI IMPULSI DA CONTARE */
        LookUp_U8_U16( &(tmpVEdgeThr), VTPULSETHR, tmpVehSpeedFront, BKVEHSPEED, (BKVEHSPEEDDimension-1));
        VEdgeThrFront = tmpVEdgeThr >> 8;
        
        if(VEdgeThrFront != VEdgeThrOldFront)
        {
            WS_SetThreshold(CH_FRONT_WHEEL, VEdgeThrFront);
            VEdgeThrOldFront = VEdgeThrFront;
        }
        else
        {
            /* MISRA. */
        }
    }

    if (VehRearSourceSel != 0) /* ABS Veh_Speed. */
    {
        tmpVehSpeedRear = BufVehSpeedRearCAN[0];
        VehSpeedRearNc = VehSpeedRearNcCAN;

        /* CALCOLO ACCELERAZIONI */
        calc_RearAccCAN(&BufVehSpeedRearCAN[0], sizeof(BufVehSpeedRearCAN));
    }
    else /* Internal Veh_Speed. */
    {
        /* CALCOLO RatioVSpeedRear */
        LookUp_U8_U16( &(tmpRatioVSpeed), VTRATIOVSPEED, VehSpeedRearNc, BKVEHSPEED, (BKVEHSPEEDDimension-1));
        RatioVSpeedRearNc = tmpRatioVSpeed >> 3;
        
        LookUp_U8_U16( &(tmpRatioVSpeed), VTRATIOVSPEED, tmpVehSpeedRear, BKVEHSPEED, (BKVEHSPEEDDimension-1));
        RatioVSpeedRear = tmpRatioVSpeed >> 3;

        /* LETTURA REAR WHEEL */
        WS_GetLastDeltaTime(CH_REAR_WHEEL, &deltatime, &lastwtimerear, &FlgStallRear, VEHSTALLPERIOD);
        WS_GetLastDeltaEdge(CH_REAR_WHEEL, &deltaedge);

        if (deltatime > 0)
        {
            if ((FlgStallRear) || (deltatime > VEHSTALLPERIOD))
            {
                tmpVehSpeedRear = 0;
                VehSpeedRearNc = 0;
                BufVehSpeedRearOld[0] = 0;
                BufVehSpeedRearOld[1] = 0;
                BufVehSpeedRearOld[2] = 0;
                FirstSpeedCalcRear = 1;
                /* SATURAZIONE DTimeRear */
                if(deltatime > MAX_uint32_T)
                {
                    DTimeRear = MAX_uint32_T;
                }
                else
                {
                    DTimeRear = (uint32_T)deltatime;
                }
                DTimeRearOld = 0;
                DTimeRearOld2 = 0;
                AccRear = 0;
                AccRear2 = 0;
                AccRear3 = 0;
            }
            else if (lastwtimerear != lastwtimerearold)
            {
                lastwtimerearold = lastwtimerear;
                BufVehSpeedRearOld[2] = BufVehSpeedRearOld[1];
                BufVehSpeedRearOld[1] = BufVehSpeedRearOld[0];
                BufVehSpeedRearOld[0] = tmpVehSpeedRear;

                /* SATURAZIONE DTimeRear */
                if (deltatime > MAX_uint32_T)
                {
                    DTimeRear = MAX_uint32_T;
                }
                else
                {
                    DTimeRear = (uint32_T)deltatime;
                }

                /* SATURAZIONE DEdgeRear */
                if (deltaedge > MAX_uint16_T)
                {
                    DEdgeRear = MAX_uint16_T;
                }
                else
                {
                    DEdgeRear = (uint16_T) deltaedge;
                }

                /* CALCOLO VELOCITA' */
                Look2D_U16_U16_U16(&tmpCentVSpeed, &(TBRWHEELCENTCORR[0][0]), VehSpeedRearNc, &(BKVCENTCORR[0]), (BKVCENTCORRDimension-1), TyreTypeCAN, &(BKTYRECORR[0]), (BKTYRECORRDimension-1));
                tmpCalcWheelRadius = (uint16_T)(((uint32_T)REARWHEELRADIUS[TyreTypeCAN] * tmpCentVSpeed) >> 15);
                if (FlgYawRec == 1)
                {
                    tmpDVSpeed = (ROLL_REC_15DEG * 100);
                }
                else
                {
                    tmpDVSpeed = abs(RollCAN);
                }
                Look2D_U16_S16_U16(&tmpRatioVSpeed, &(TBRWHEELRADIUSCORR[0][0]), tmpDVSpeed, &(BKROLLANGLE[0]), (BKROLLANGLEDimension-1), TyreTypeCAN, &(BKTYRECORR[0]), (BKTYRECORRDimension-1));
                RearWheelRadius = (uint16_T)(((uint32_T)tmpCalcWheelRadius * tmpRatioVSpeed) >> 15);

                tmpVehSpeed = (uint32_T)DEdgeRear * REARWHEELRADIUS[TyreTypeCAN];
                tmpVehSpeed *= KVSPEEDCALC;
                tmpVehSpeed /= DTimeRear;
                VehSpeedRearNc = (uint16_T)(tmpVehSpeed);
                
                tmpVehSpeed = (uint32_T)DEdgeRear * RearWheelRadius;
                tmpVehSpeed *= KVSPEEDCALC;
                tmpVehSpeed /= DTimeRear;
                tmpVehSpeedRear = (uint16_T)(tmpVehSpeed);
                
                if (FirstSpeedCalcRear == 1)
                {
                    /* Primo calcolo di velocit� dopo lo stallo */
                    FirstSpeedCalcRear = 0;
                    AccRear = 0;
                    AccRear2 = 0;
                    AccRear3 = 0;
                }
                else
                {
                    if (DEdgeRear >= 2)
                    {
                        tmpVehSpeed = DEdgeRear * VehSpeedRearNc;
                        tmpVehSpeed /= (DEdgeRear-1);
                        tmpVehSpeed = (tmpVehSpeed * RatioVSpeedRearNc) >> 10;

                        if (VehSpeedRearNc > ((uint16_T)tmpVehSpeed))
                        {
                            tmpVehSpeed = VehSpeedRearNc * (DEdgeRear-1);
                            VehSpeedRearNc = tmpVehSpeed / DEdgeRear;
                        }
                        else
                        {
                            /* Non fare niente. */
                        }
                    
                        tmpVehSpeed = DEdgeRear * tmpVehSpeedRear;
                        tmpVehSpeed /= (DEdgeRear-1);
                        tmpVehSpeed = (tmpVehSpeed * RatioVSpeedRear) >> 10;

                        if (tmpVehSpeedRear > ((uint16_T)tmpVehSpeed))
                        {
                            tmpVehSpeed = tmpVehSpeedRear * (DEdgeRear-1);
                            tmpVehSpeedRear = tmpVehSpeed / DEdgeRear;
                        }
                        else
                        {
                            /* Non fare niente. */
                        }
                    }
                    else
                    {
                        /* Non fare niente. */
                    }

                    /* CALCOLO ACCELERAZIONI */
                    calc_RearAcc(&BufVehSpeedRearOld[0], sizeof(BufVehSpeedRearOld), tmpVehSpeedRear);
                }
            }
            else
            {
                /* Fase di conteggio fronti; non necessario calcolare la velocit� */
            }
        }
        else
        {
            /* Errore, Velocit� non calcolabile. */
        }
    
        /* AGGIORNAMENTO NUMERO DI IMPULSI DA CONTARE */
        LookUp_U8_U16( &(tmpVEdgeThr), VTPULSETHR, tmpVehSpeedRear, BKVEHSPEED, (BKVEHSPEEDDimension-1));
        VEdgeThrRear = tmpVEdgeThr >> 8;
        
        if (VEdgeThrRear != VEdgeThrOldRear)
        {
            WS_SetThreshold(CH_REAR_WHEEL, VEdgeThrRear);
            VEdgeThrOldRear = VEdgeThrRear;
        }
        else
        {
            /* MISRA. */
        }
    } 

    if (ENVSKALM != 0u)
    {
        /* Applicare Kalman alla VehSpeedFront */
        idxKmax = min(NKALDEEP, VS_BUFF_KAL_DEEP);
        for (i = 1u; i < idxKmax; i++)
        {
            VehSpeedFrontKal[(i-1)] = VehSpeedFrontKal[i];
            VtXApoEstFr[(i-1)] = VtXApoEstFr[i];
            VtPApoEstFr[(i-1)] = VtPApoEstFr[i];
        }
        VehSpeedFrontKal[(idxKmax-1)] = (int16_T)tmpVehSpeedFront;
        QKalmanFr = QKALMANFR;
        RKalmanFr = RKALMANFR;
        for (i = 1u; i < idxKmax; i++)
        {
            /* Do a prediction */
            VtXApriEstFr[i] = VtXApoEstFr[(i-1)];
            VtPApriEstFr[i] = VtPApoEstFr[(i-1)] + QKalmanFr;
            /* Calculate Kalman Gain */
            KgKalmanFr = (int16_T)((int32_T)(VtPApriEstFr[i] * ((int16_T)(1.0f * 4096.0f))) / (VtPApriEstFr[i] + RKalmanFr));
            /* Perform a correction and update aposteriori estimation of x and P */
            VtXApoEstFr[i] = (int16_T)((((int32_T)(VtXApriEstFr[i] * ((int16_T)(1.0f * 4096.0f)))) + ((int32_T)(KgKalmanFr * ((int16_T)VehSpeedFrontKal[i] - VtXApriEstFr[i])))) >> 12);
            VtPApoEstFr[i] = (int16_T)(((int32_T)((((int16_T)(1.0f * 4096.0f)) - KgKalmanFr) * VtPApriEstFr[i])) >> 12);
        }
        if (VtXApoEstFr[(idxKmax-1)] >= 0)
        {
            VehSpeedFrontKalF = (uint16_T)VtXApoEstFr[(idxKmax-1)];
        }
        else
        {
            VehSpeedFrontKalF = 0u;
        }
    }
    else
    {
        VehSpeedFrontKalF = tmpVehSpeedFront;
    }

    /* Buffering VehSpeedFront */
    flgMaxVSFPre = 1;
    flgMaxVSFPost = 1;
    if (VSNAACCF < VS_BUFF_DEEP)
    {
        tmpAccRearRbVf = (int16_T)VehSpeedFrontKalF - OldVehSpeedFront[VSNAACCF];
        if (tmpAccRearRbVf > (64 * 16))
        {
            tmpAccRearRbVf = (64 * 16);
        }
        else
        {
            if (tmpAccRearRbVf < (-64 * 16))
            {
                tmpAccRearRbVf = (-64 * 16);
            }
            else
            {
                /* NONE */
            }
        }
        i = VSNAACCF;
        j = (int8_T)((int16_T)VSNAACCF - (int16_T)VSNAACCFP);
        k = j;
        if (k > 0)
        {
            /* */
        }
        else
        {
            k = 0;
        }
        while (i > 0)
        {
            if (j > 0)
            {
                if (OldVehSpeedFront[k] >= OldVehSpeedFront[j])
                {
                    /* */
                }
                else
                {
                    flgMaxVSFPost = 0;
                }
            }
            else
            {
                /* */
            }
            if (VehSpeedFrontKalF > OldVehSpeedFront[i])
            {
                /* */
            }
            else
            {
                flgMaxVSFPre = 0;
            }
            OldVehSpeedFront[i] = OldVehSpeedFront[(i - 1)];
            i--;
            j--;
        }
    }
    else
    {
        tmpAccRearRbVf = 0;
    }
    if (OldVehSpeedFront[k] >= VehSpeedFrontKalF)
    {
        /* */
    }
    else
    {
        flgMaxVSFPost = 0;
    }
    if (VehSpeedFrontKalF > OldVehSpeedFront[0])
    {
        /* */
    }
    else
    {
        flgMaxVSFPre = 0;
    }
    FlgMaxVSFPre = (flgMaxVSFPre != 0);
    FlgMaxVSFPost = (flgMaxVSFPost != 0);
    OldVehSpeedFront[0] = VehSpeedFrontKalF;

    /* Calcolo AccFrontRbVf */
    AccFrontRbVf = (tmpAccRearRbVf * 100) / (VSNAACCF + 1); // Km/h*s

    if (ENVSKALM != 0u)
    {
        /* Applicare Kalman alla VehSpeedRear */
        idxKmax = min(NKALDEEP, VS_BUFF_KAL_DEEP);
        for (i = 1u; i < idxKmax; i++)
        {
            VehSpeedRearKal[(i-1)] = VehSpeedRearKal[i];
            VtXApoEst[(i-1)] = VtXApoEst[i];
            VtPApoEst[(i-1)] = VtPApoEst[i];
        }
        VehSpeedRearKal[(idxKmax-1)] = (int16_T)tmpVehSpeedRear;
        QKalman = QKALMAN;
        RKalman = RKALMAN;
        for (i = 1u; i < idxKmax; i++)
        {
            /* Do a prediction */
            VtXApriEst[i] = VtXApoEst[(i-1)];
            VtPApriEst[i] = VtPApoEst[(i-1)] + QKalman;
            /* Calculate Kalman Gain */
            KgKalman = (int16_T)((int32_T)(VtPApriEst[i] * ((int16_T)(1.0f * 4096.0f))) / (VtPApriEst[i] + RKalman));
            /* Perform a correction and update aposteriori estimation of x and P */
            VtXApoEst[i] = (int16_T)((((int32_T)(VtXApriEst[i] * ((int16_T)(1.0f * 4096.0f)))) + ((int32_T)(KgKalman * ((int16_T)VehSpeedRearKal[i] - VtXApriEst[i])))) >> 12);
            VtPApoEst[i] = (int16_T)(((int32_T)((((int16_T)(1.0f * 4096.0f)) - KgKalman) * VtPApriEst[i])) >> 12);
        }
        if (VtXApoEst[(idxKmax-1)] >= 0)
        {
            VehSpeedRearKalF = (uint16_T)VtXApoEst[(idxKmax-1)];
        }
        else
        {
            VehSpeedRearKalF = 0u;
        }
    }
    else
    {
        VehSpeedRearKalF = tmpVehSpeedRear;
    }
    
    VehSpeedRear = VehSpeedRearKalF;

    /* Buffering VehSpeedRear */
    if (VSNAACCR < VS_BUFF_DEEP)
    {
        tmpAccRearRbVf = (int16_T)VehSpeedRear - OldVehSpeedRear[VSNAACCR];
        if (tmpAccRearRbVf > (64 * 16))
        {
            tmpAccRearRbVf = (64 * 16);
        }
        else
        {
            if (tmpAccRearRbVf < (-64 * 16))
            {
                tmpAccRearRbVf = (-64 * 16);
            }
            else
            {
                /* NONE */
            }
        }
        i = VSNAACCR;
        while (i > 0)
        {
            OldVehSpeedRear[i] = OldVehSpeedRear[(i - 1)];
            i--;
        }
    }
    else
    {
        tmpAccRearRbVf = 0;
    }
    OldVehSpeedRear[0] = VehSpeedRear;

    /* Calcolo AccRearRbVf e VehRbVfInt ricostruita */
    AccRearRbVf = (tmpAccRearRbVf * 100) / (VSNAACCR + 1); // Km/h*s

    /* Calcolo AccRearRbVfAvg */
    if ((0 < ACCREARRBVFDIM) && (ACCREARRBVFDIM < VS_BUFF_ACC_DEEP))
    {
        tmpAccRearRbVf = VtAccRearRbVf[0];
        for (i = 1u; i <= ACCREARRBVFDIM; i++)
        {
            tmpAccRearRbVf = tmpAccRearRbVf + VtAccRearRbVf[i];
            VtAccRearRbVf[(i - 1)] = VtAccRearRbVf[i];
        }
        VtAccRearRbVf[ACCREARRBVFDIM] = AccRearRbVf;
        tmpAccRearRbVf = tmpAccRearRbVf + AccRearRbVf;
        AccRearRbVfAvg = (tmpAccRearRbVf / (ACCREARRBVFDIM + 1));
    }
    else
    {
        AccRearRbVfAvg = AccRearRbVf;
    }
    
    /* Calcolo DeltaAccRbVf */
    DeltaAccRbVf = (AccRearRbVf - AccFrontRbVf);
    if ((AccRearRbVf < 0) && (AccFrontRbVf < 0))
    {
        accPlaus = 0;
    }
    else
    {
        accPlaus = 1;
    }

    /* Calcolo DeltaVehSpeedKal */
    DeltaVehSpeedKal = (int16_T)VehSpeedRear - (int16_T)VehSpeedFrontKalF;

    if ((AxCAN < 0) || (AxCAN >= DBMINAXCAN))
    {
        tmpAxCAN = AxCAN;
    }
    else
    {
        tmpAxCAN = 0;
    }

    /* Stato AntiWheeling */
    switch (StRbVf)
    {
        case INIT_RB_VF:
        {
            if ((flgMaxVSFPre != 0) || (VehSpeedFrontKalF > VehSpeedFrontAW) || (EndStartFlg == 0) || (IdleFlg == 1))
            {
                VehRbVfInt = VehSpeedFrontKalF;
                TmpVehRbVfAxInt = (int32_T)(VehSpeedFrontKalF * 100);
                VehRbVfAxInt = VehSpeedFrontKalF;
                VehSpeedFrontEndAW = 0;
            }
            else
            {
                if ((DeltaVehSpeedKal > THRRBVFDVS) && ((AxCAN > THRAXCANACCDEV) || ((DeltaAccRbVf > THRRBVFACCDEV) && (accPlaus != 0))))
                {
                    StRbVf = CALC_RB_VF_INT;
                    VehSpeedFrontEndAW = VehSpeedFrontKalF + HYSVSFENDAW;
                }
                else
                {
                    /* */
                }
                tmpVehRbVfInt = ((VehRbVfInt * 100) + AccRearRbVf) / 100;
                VehRbVfInt = max(tmpVehRbVfInt, VehSpeedFrontKalF);
                tmpVehRbVfInt = (((TmpVehRbVfAxInt * 100) + (((tmpAxCAN * 3600) / 1000) * 16)) / 100);
                TmpVehRbVfAxInt = max(tmpVehRbVfInt, (VehSpeedFrontKalF * 100));
                VehRbVfAxInt = TmpVehRbVfAxInt / 100;
            }
        }
        break;

        case CALC_RB_VF_INT:
        {
            if ((flgMaxVSFPre != 0) || (VehSpeedFrontKalF >= VehSpeedFrontAW) || (EndStartFlg == 0) || (IdleFlg == 1))
            {
                StRbVf = INIT_RB_VF;
                VehRbVfInt = VehSpeedFrontKalF;
                TmpVehRbVfAxInt = (int32_T)(VehSpeedFrontKalF * 100);
                VehRbVfAxInt = VehSpeedFrontKalF;
                VehSpeedFrontEndAW = 0;
            }
            else
            {
                if ((flgMaxVSFPost != 0) && (DeltaVehSpeedKal > THRRBVFDVS))
                {
                    StRbVf = WHEELING_EXEC;
                }
                else
                {
                    /* */
                }
                tmpVehRbVfInt = ((VehRbVfInt * 100) + AccRearRbVf) / 100;
                VehRbVfInt = max(tmpVehRbVfInt, VehSpeedFrontKalF);
                tmpVehRbVfInt = (((TmpVehRbVfAxInt * 100) + (((tmpAxCAN * 3600) / 1000) * 16)) / 100);
                TmpVehRbVfAxInt = max(tmpVehRbVfInt, (VehSpeedFrontKalF * 100));
                VehRbVfAxInt = TmpVehRbVfAxInt / 100;
            }
        }
        break;

        case WHEELING_EXEC:
        {
            if ((VehSpeedFrontKalF + HYSWHLEXVSFKALF) >= VehSpeedFrontAW)
            {
                StRbVf = INIT_RB_VF;
                VehRbVfInt = VehSpeedFrontKalF;
                TmpVehRbVfAxInt = (int32_T)(VehSpeedFrontKalF * 100);
                VehRbVfAxInt = VehSpeedFrontKalF;
                VehSpeedFrontEndAW = 0;
            }
            else if (DeltaVehSpeedKal < THRRBVFDVS)
            {
                StRbVf = CALC_RB_VF_INT;
                tmpVehRbVfInt = ((VehRbVfInt * 100) + AccRearRbVf) / 100;
                VehRbVfInt = max(tmpVehRbVfInt, VehSpeedFrontKalF);
                tmpVehRbVfInt = (((TmpVehRbVfAxInt * 100) + (((tmpAxCAN * 3600) / 1000) * 16)) / 100);
                TmpVehRbVfAxInt = max(tmpVehRbVfInt, (VehSpeedFrontKalF * 100));
                VehRbVfAxInt = TmpVehRbVfAxInt / 100;
            }
            else
            {
                tmpVehRbVfInt = ((VehRbVfInt * 100) + AccRearRbVf) / 100;
                VehRbVfInt = max(tmpVehRbVfInt, VehSpeedFrontKalF);
                tmpVehRbVfInt = (((TmpVehRbVfAxInt * 100) + (((tmpAxCAN * 3600) / 1000) * 16)) / 100);
                TmpVehRbVfAxInt = max(tmpVehRbVfInt, (VehSpeedFrontKalF * 100));
                VehRbVfAxInt = TmpVehRbVfAxInt / 100;
            }
        }
        break;

        default:
        {
            VehRbVfInt = VehSpeedFrontKalF;
            TmpVehRbVfAxInt = (int32_T)(VehSpeedFrontKalF * 100);
            VehRbVfAxInt = VehSpeedFrontKalF;
            VehSpeedFrontEndAW = 0;
        }
        break;    
    }

    if (VehRbVfInt > MAX_int16_T)
    {
        VehRbVfInt = MAX_int16_T;
    }
    else
    {
        if (VehRbVfInt < MIN_int16_T)
        {
            VehRbVfInt = MIN_int16_T;
        }
        else
        {
            /* */
        }
    }
    if (VehRbVfAxInt > MAX_int16_T)
    {
        VehRbVfAxInt = MAX_int16_T;
        TmpVehRbVfAxInt = (MAX_int16_T * 100);
    }
    else
    {
        if (VehRbVfAxInt < MIN_int16_T)
        {
            VehRbVfAxInt = MIN_int16_T;
            TmpVehRbVfAxInt = (MIN_int16_T * 100);
        }
        else
        {
            /* */
        }
    }
    MinVehRbVfInt = min(VehRbVfAxInt, VehRbVfInt);
    DeltaVehRbVfInt = MinVehRbVfInt - VehSpeedFrontKalF;
    if ((VehSpeedFrontKalF > VehSpeedFrontEndAW) && (DeltaVehRbVfInt <= HYSVSFDRBVFAW))
    {
        if (filtOn == 0u)
        {
            tmpVehSpeedFrontAW = VehSpeedFrontKalF;
        }
        else
        {
            LookUp_S16_S16(&MinRtAWKalF, &VTRTDISTAWKALF[0], DistAWKalF, &BKRTDISTAWKALF[0], (BKRTDISTAWKALF_dim - 1));
            RateLimiter_S16(&tmpVehSpeedFrontAW, VehSpeedFrontKalF, oldVehSpeedFrontAW, MinRtAWKalF, 0);
            oldVehSpeedFrontAW = tmpVehSpeedFrontAW;
            if (StRbVf == INIT_RB_VF)
            {
                filtOn = 0u;
            }
            else { /* MISRA */ }
        }
    }
    else
    {
        tmpVehSpeedFrontAW = (((VehRbVfInt * WHTVSFINT) + (VehRbVfAxInt * ((1 * 128) - WHTVSFINT))) >> 7);
        if (tmpVehSpeedFrontAW > VehSpeedRear)
        {
            tmpVehSpeedFrontAW = VehSpeedRear;
        }
        else if (tmpVehSpeedFrontAW < 0)
        {
            tmpVehSpeedFrontAW = 0;
        }
        else
        {
            /* */
        }
        filtOn = 1u;
        oldVehSpeedFrontAW = tmpVehSpeedFrontAW;
    }
    VehSpeedFrontAW = tmpVehSpeedFrontAW;

    DistAWKalF = (int16_T)(VehSpeedFrontAW - VehSpeedFrontKalF);

    /* Routing VehSpeedFront */
    if (ENRBVSFAW != 0u)
    {
        if (ENRBVSFWE != 0u)
        {
            VehSpeedFrontNz = VehSpeedFrontAW;
        }
        else if ((StRbVf == CALC_RB_VF_INT) || (StRbVf == WHEELING_EXEC))
        {
            VehSpeedFrontNz = VehSpeedFrontAW;
        }
        else
        {
            VehSpeedFrontNz = VehSpeedFrontKalF;
        }
    }
    else
    {
        VehSpeedFrontNz = VehSpeedFrontKalF;
    }
    VehSpeedFront = (uint16_T)(((uint32_T)(VehSpeedFrontNz * EEGnVehSpeedFront)) >> 10);
    
    /* Calcolo DeltaVehSpeed e AccDeltaVehSpeed */
    DeltaVehSpeed = (int16_T)VehSpeedRear - (int16_T)VehSpeedFront;
    if (VSNADVS < VS_BUFF_DVS_DEEP)
    {
        tmpAccDeltaSpd = (((DeltaVehSpeed - OldDeltaVehSpeed[VSNADVS]) * 100) / (VSNADVS + 1));
        tmpAccDeltaSpd  = min (tmpAccDeltaSpd, MAX_int16_T);
        AccDeltaVehSpeed = max (tmpAccDeltaSpd, MIN_int16_T);
        i = VSNADVS;
        while (i > 0)
        {
            OldDeltaVehSpeed[i] = OldDeltaVehSpeed[(i - 1)];
            i--;
        }
    }
    else
    {
        tmpAccDeltaSpd = ((DeltaVehSpeed - OldDeltaVehSpeed[0]) * 100);
        tmpAccDeltaSpd  = min (tmpAccDeltaSpd, MAX_int16_T);
        AccDeltaVehSpeed = max (tmpAccDeltaSpd, MIN_int16_T);
    }
    OldDeltaVehSpeed[0] = DeltaVehSpeed;
    
    if ((DeltaVehSpeed > THRDVSSUP) && (VehSpeedFrontKalF > THRVSFRTZERO))
    {
        FlgDeltaVehSpeedSup = 1;
    }
    else
    {
        FlgDeltaVehSpeedSup = 0;
    }
    if ((DeltaVehSpeed < THRDVSINF)  && (VehSpeedFrontKalF > THRVSFRTZERO))
    {
        FlgDeltaVehSpeedInf = 1;
    }
    else
    {
        FlgDeltaVehSpeedInf = 0;
    }
}

static void calc_RearAcc (uint16_T *buffVehSpeed, uint8_T size, uint16_T vehSpeed)
{
    int16_T tmpDVSpeed;
    int32_T tmpAcc;

    if (size == 8) /* 4 Byte. */
    {
        /* CALCOLO ACCELERAZIONE - BASE TEMPI 3 CAMPIONI */
        if (DTimeRearOld2 != 0)
        {
            int32_T dtime3;
            
            tmpDVSpeed = vehSpeed - buffVehSpeed[2];
            if (tmpDVSpeed > MAX_D_VSPEED)
            {
                tmpDVSpeed = MAX_D_VSPEED;
            }
            else if(tmpDVSpeed < MIN_D_VSPEED)
            {
                tmpDVSpeed = MIN_D_VSPEED;
            }
            else
            {
                /* VALORE CALCOLATO OK */
            }

            dtime3 = (int32_T)DTimeRear + (int32_T)DTimeRearOld;
            dtime3 = dtime3 + (int32_T)DTimeRearOld2;

            /* Accelerazione espressa in kmh/s */
            tmpAcc = (((int32_T)tmpDVSpeed)<<6);
            tmpAcc = tmpAcc * 15625;
            tmpAcc = tmpAcc/dtime3;
            if (tmpAcc > MAX_int16_T)
            {
                AccRear3 = MAX_int16_T;
            }
            else if (tmpAcc < MIN_int16_T)
            {
                AccRear3 = MIN_int16_T;
            }
            else
            {
                AccRear3 = (int16_T)tmpAcc;
            }
        }
        DTimeRearOld2 = DTimeRearOld;

        /* CALCOLO ACCELERAZIONE - BASE TEMPI 2 CAMPIONI */
        if (DTimeRearOld != 0)
        {
            int32_T dtime2;
            
            tmpDVSpeed = vehSpeed - buffVehSpeed[1];
            if (tmpDVSpeed > MAX_D_VSPEED)
            {
                tmpDVSpeed = MAX_D_VSPEED;
            }
            else if(tmpDVSpeed < MIN_D_VSPEED)
            {
                tmpDVSpeed = MIN_D_VSPEED;
            }
            else
            {
                /* VALORE CALCOLATO OK */
            }

            dtime2 = (int32_T)DTimeRear + (int32_T)DTimeRearOld;

            /* Accelerazione espressa in kmh/s */
            tmpAcc = (((int32_T)tmpDVSpeed)<<6);
            tmpAcc = tmpAcc * 15625;
            tmpAcc = tmpAcc/dtime2;
            if (tmpAcc > MAX_int16_T)
            {
                AccRear2 = MAX_int16_T;
            }
            else if (tmpAcc < MIN_int16_T)
            {
                AccRear2 = MIN_int16_T;
            }
            else
            {
                AccRear2 = (int16_T)tmpAcc;
            }
        }
        DTimeRearOld = DTimeRear;

        /* CALCOLO ACCELERAZIONE */
        tmpDVSpeed = vehSpeed - buffVehSpeed[0];
        if (tmpDVSpeed > MAX_D_VSPEED)
        {
            tmpDVSpeed = MAX_D_VSPEED;
        }
        else if(tmpDVSpeed < MIN_D_VSPEED)
        {
            tmpDVSpeed = MIN_D_VSPEED;
        }
        else
        {
            /* VALORE CALCOLATO OK */
        }

        /* Accelerazione espressa in kmh/s */
        tmpAcc = (((int32_T)tmpDVSpeed)<<6);
        tmpAcc = tmpAcc * 15625;
        tmpAcc = tmpAcc/((int32_T)DTimeRear);
        if (tmpAcc > MAX_int16_T)
        {
            AccRear = MAX_int16_T;
        }
        else if (tmpAcc < MIN_int16_T)
        {
            AccRear = MIN_int16_T;
        }
        else
        {
            AccRear = (int16_T)tmpAcc;
        }
    }
    else
    {
        /* Skip */
    }
}

static int16_T calc_RearAccCAN (uint16_T *bufVehSpeedIn, uint8_T size)
{
    int16_T  ret = NO_ERROR;
    int16_T  tmpDVSpeed;
    int32_T  tmpAcc;
    static uint16_T updateAcc = 0;

    if (size == (2 * SIZE_VS_CAN)) /* SIZE_VS_CAN Byte. */
    {
        LookUp_U16_U16(&IdxVsCAN, &VTRATIOVSCANDTIME[0], bufVehSpeedIn[0], &BKVEHSPEED[0], (BKVEHSPEEDCANDimension-1));

        if (updateAcc >= IdxVsCAN)
        {
            updateAcc = 1;
            if ((IdxVsCAN == 0) || (IdxVsCAN > 6))
            {
                IdxVsCAN = 3; /* Errore di Idx. */
                ret = (-1);
            }
            else
            {
                /* Non fare niente. */
            }
            /* CALCOLO ACCELERAZIONE */
            DTimeRear = ((uint32_T)DELTA_T_VEH_SPEED * (1 * IdxVsCAN));
            tmpDVSpeed = bufVehSpeedIn[0] - bufVehSpeedIn[(1 * IdxVsCAN)];
            if (tmpDVSpeed > MAX_D_VSPEED)
            {
                tmpDVSpeed = MAX_D_VSPEED;
            }
            else if(tmpDVSpeed < MIN_D_VSPEED)
            {
                tmpDVSpeed = MIN_D_VSPEED;
            }
            else
            {
                /* VALORE CALCOLATO OK */
            }

            /* Accelerazione espressa in kmh/s */
            tmpAcc = (((int32_T)tmpDVSpeed) << 6);
            tmpAcc = tmpAcc * 15625;
            tmpAcc = tmpAcc / ((int32_T)DTimeRear);
            if (tmpAcc > MAX_int16_T)
            {
                AccRear = MAX_int16_T;
            }
            else if (tmpAcc < MIN_int16_T)
            {
                AccRear = MIN_int16_T;
            }
            else
            {
                AccRear = (int16_T)tmpAcc;
            }

            /* CALCOLO ACCELERAZIONE - BASE TEMPI 2 CAMPIONI */
            DTimeRearOld = ((uint32_T)DELTA_T_VEH_SPEED * (2 * IdxVsCAN));
            tmpDVSpeed = bufVehSpeedIn[0] - bufVehSpeedIn[(2 * IdxVsCAN)];
            if (tmpDVSpeed > MAX_D_VSPEED)
            {
                tmpDVSpeed = MAX_D_VSPEED;
            }
            else if(tmpDVSpeed < MIN_D_VSPEED)
            {
                tmpDVSpeed = MIN_D_VSPEED;
            }
            else
            {
                /* VALORE CALCOLATO OK */
            }

            /* Accelerazione espressa in kmh/s */
            tmpAcc = (((int32_T)tmpDVSpeed) << 6);
            tmpAcc = tmpAcc * 15625;
            tmpAcc = tmpAcc / ((int32_T)DTimeRearOld);
            if (tmpAcc > MAX_int16_T)
            {
                AccRear2 = MAX_int16_T;
            }
            else if (tmpAcc < MIN_int16_T)
            {
                AccRear2 = MIN_int16_T;
            }
            else
            {
                AccRear2 = (int16_T)tmpAcc;
            }

            /* CALCOLO ACCELERAZIONE - BASE TEMPI 3 CAMPIONI */
            DTimeRearOld2 = ((uint32_T)DELTA_T_VEH_SPEED * (3 * IdxVsCAN));
            tmpDVSpeed = bufVehSpeedIn[0] - bufVehSpeedIn[(3 * IdxVsCAN)];
            if (tmpDVSpeed > MAX_D_VSPEED)
            {
                tmpDVSpeed = MAX_D_VSPEED;
            }
            else if(tmpDVSpeed < MIN_D_VSPEED)
            {
                tmpDVSpeed = MIN_D_VSPEED;
            }
            else
            {
                /* VALORE CALCOLATO OK */
            }

            /* Accelerazione espressa in kmh/s */
            tmpAcc = (((int32_T)tmpDVSpeed) << 6);
            tmpAcc = tmpAcc * 15625;
            tmpAcc = tmpAcc / ((int32_T)DTimeRearOld2);
            if (tmpAcc > MAX_int16_T)
            {
                AccRear3 = MAX_int16_T;
            }
            else if (tmpAcc < MIN_int16_T)
            {
                AccRear3 = MIN_int16_T;
            }
            else
            {
                AccRear3 = (int16_T)tmpAcc;
            }
        }
        else
        {
            updateAcc++;
        }
    }
    else
    {
        ret = (-1); /* Skip */
    }
    return ret;
}

void Update_RearRollSpeed (uint16_T *tmpVSpeed, uint16_T *tmpWheelRadius, uint8_T EnComp)
{
    uint16_T  tmpCalcVSpeed;
    int16_T   tmpDVSpeed;
    uint16_T  tmpCentVSpeed;
    uint16_T  tmpRatioVSpeed;
    uint16_T  tmpCalcWheelRadius;

    tmpCalcVSpeed = (uint16_T)((uint32_T)VehSpeedRearCAN * KM_CAN_VEHSPEED_REAR) / (KD_CAN_VEHSPEED_REAR);
    if (EnComp != 0)
    {
        Look2D_U16_U16_U16(&tmpCentVSpeed, &(TBRWHEELCENTCORR[0][0]), tmpCalcVSpeed, &(BKVCENTCORR[0]), (BKVCENTCORRDimension-1), TyreTypeCAN, &(BKTYRECORR[0]), (BKTYRECORRDimension-1));
        tmpCalcVSpeed = (uint16_T)(((uint32_T)tmpCalcVSpeed * tmpCentVSpeed) >> 15);
        tmpCalcWheelRadius = (uint16_T)(((uint32_T)REARWHEELRADIUS[TyreTypeCAN] * tmpCentVSpeed) >> 15);
    }
    else
    {
        tmpCentVSpeed = (1 << 15);
        tmpCalcWheelRadius = REARWHEELRADIUS[TyreTypeCAN];
    }

    /* CALCOLO VELOCITA' */
    if (EnComp == 0)
    {
        tmpRatioVSpeed = (uint16_T)(((uint32_T)TBRWHEELRADIUSCORR[0][0] * tmpCentVSpeed) >> 15);
        *tmpWheelRadius = (uint16_T)(((uint32_T)tmpCalcWheelRadius * tmpCentVSpeed) >> 15);
    }
    else
    {
        if (FlgYawRec == 1)
        {
            tmpDVSpeed = (ROLL_REC_15DEG * 100);
        }
        else
        {
            tmpDVSpeed = abs(RollCAN);
        }
        Look2D_U16_S16_U16(&tmpRatioVSpeed, &(TBRWHEELRADIUSCORR[0][0]), tmpDVSpeed, &(BKROLLANGLE[0]), (BKROLLANGLEDimension-1), TyreTypeCAN, &(BKTYRECORR[0]), (BKTYRECORRDimension-1));
        *tmpWheelRadius = (uint16_T)(((uint32_T)tmpCalcWheelRadius * tmpRatioVSpeed) >> 15);
    }
    tmpCalcVSpeed = (uint16_T)(((uint32_T)tmpCalcVSpeed * tmpRatioVSpeed) >> 15);
    *tmpVSpeed = tmpCalcVSpeed;
}

void Update_FrontRollSpeed (uint16_T *tmpVSpeed, uint16_T *tmpWheelRadius, uint8_T EnComp)
{
    uint16_T  tmpCalcVSpeed;
    int16_T   tmpDVSpeed;
    uint16_T  tmpCentVSpeed;
    uint16_T  tmpRatioVSpeed;
    uint16_T  tmpCalcWheelRadius;

    tmpCalcVSpeed = (uint16_T)((uint32_T)VehSpeedFrontCAN * KM_CAN_VEHSPEED_FRONT) / (KD_CAN_VEHSPEED_FRONT);
    if (EnComp != 0)
    {
        Look2D_U16_U16_U16(&tmpCentVSpeed, &(TBFWHEELCENTCORR[0][0]), tmpCalcVSpeed, &(BKVCENTCORR[0]), (BKVCENTCORRDimension-1), TyreTypeCAN, &(BKTYRECORR[0]), (BKTYRECORRDimension-1));
        tmpCalcVSpeed = (uint16_T)(((uint32_T)tmpCalcVSpeed * tmpCentVSpeed) >> 15);
        tmpCalcWheelRadius = (uint16_T)(((uint32_T)FRONTWHEELRADIUS[TyreTypeCAN] * tmpCentVSpeed) >> 15);
    }
    else
    {
        tmpCentVSpeed = (1 << 15);
        tmpCalcWheelRadius = FRONTWHEELRADIUS[TyreTypeCAN];
    }

    /* CALCOLO VELOCITA' */
    if (EnComp == 0)
    {
        tmpRatioVSpeed = (uint16_T)(((uint32_T)TBFWHEELRADIUSCORR[0][0] * tmpCentVSpeed) >> 15);
        *tmpWheelRadius = (uint16_T)(((uint32_T)tmpCalcWheelRadius * tmpCentVSpeed) >> 15);
    }
    else
    {
        if (FlgYawRec == 1)
        {
            tmpDVSpeed = (ROLL_REC_15DEG * 100);
        }
        else
        {
            tmpDVSpeed = abs(RollCAN);
        }
        Look2D_U16_S16_U16(&tmpRatioVSpeed, &(TBFWHEELRADIUSCORR[0][0]), tmpDVSpeed, &(BKROLLANGLE[0]), (BKROLLANGLEDimension-1), TyreTypeCAN, &(BKTYRECORR[0]), (BKTYRECORRDimension-1));
        *tmpWheelRadius = (uint16_T)(((uint32_T)tmpCalcWheelRadius * tmpRatioVSpeed) >> 15);
    }
    tmpCalcVSpeed = (uint16_T)(((uint32_T)tmpCalcVSpeed * tmpRatioVSpeed) >> 15);
    *tmpVSpeed = tmpCalcVSpeed;
}

#endif
