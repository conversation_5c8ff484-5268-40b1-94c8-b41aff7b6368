/*
 * File: LaunchCtrl_private.h
 *
 * Code generated for Simulink model 'LaunchCtrl'.
 *
 * Model version                  : 1.304
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu Jun 12 15:18:56 2025
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Freescale->32-bit PowerPC
 * Code generation objectives:
 *    1. MISRA C:2012 guidelines
 *    2. Execution efficiency
 *    3. Safety precaution
 *    4. RAM efficiency
 *    5. Traceability
 *    6. Debugging
 *    7. ROM efficiency
 * Validation result: Not run
 */

#ifndef RTW_HEADER_LaunchCtrl_private_h_
#define RTW_HEADER_LaunchCtrl_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "LaunchCtrl.h"

/* Includes for objects with custom storage classes. */
#include "diagmgm_out.h"
#include "cmisatmgm_out.h"
#include "canmgm.h"
#include "digitalin.h"
#include "Cmefilter_mgm.h"
#include "cmidriver_mgm.h"
#include "rpm_limiter.h"
#include "engflag.h"
#include "ptrain_diag.h"
#include "trq_driver.h"
#include "gear_mgm.h"
#include "Timing.h"
#include "syncmgm.h"
#include "temp_mgm.h"
#include "trac_ctrl.h"
#include "vspeed_mgm.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

/* Imported (extern) block parameters */
extern int16_T BKLCIDXCTF[4];          /* Variable: BKLCIDXCTF
                                        * Referenced by: '<S12>/BKLCIDXCTF'
                                        * Bk cutoff
                                        */
extern int16_T BKLCIDXCTF2[4];         /* Variable: BKLCIDXCTF2
                                        * Referenced by: '<S12>/BKLCIDXCTF2'
                                        * Bk cutoff
                                        */
extern int16_T DTRPMLCTRGWOT;          /* Variable: DTRPMLCTRGWOT
                                        * Referenced by: '<S14>/Relay'
                                        * delata
                                        */
extern int16_T HYRPMLCTRGWOT;          /* Variable: HYRPMLCTRGWOT
                                        * Referenced by: '<S14>/Relay'
                                        * delata
                                        */
extern int16_T LCRPMTRGRED;            /* Variable: LCRPMTRGRED
                                        * Referenced by: '<S13>/LCRPMTRGRED'
                                        * rpm reduct
                                        */
extern int16_T RTLCRPMTRGRED;          /* Variable: RTLCRPMTRGRED
                                        * Referenced by: '<S13>/RTLCRPMTRGRED'
                                        * min Lc rate
                                        */
extern int16_T VTRPMLCTRGWOT[5];       /* Variable: VTRPMLCTRGWOT
                                        * Referenced by: '<S13>/VTRPMLCTRGWOT'
                                        * Rpm cutoff
                                        */
extern int16_T THRTEMPLC;              /* Variable: THRTEMPLC
                                        * Referenced by: '<S45>/Enable_Lc_Km'
                                        * Temp Lc
                                        */
extern int16_T LCCMETRGIDLE;           /* Variable: LCCMETRGIDLE
                                        * Referenced by:
                                        *   '<S32>/LCCMETRGIDLE'
                                        *   '<S36>/LCCMETRGIDLE'
                                        * Cme idle
                                        */
extern int16_T LCCMETRGRED;            /* Variable: LCCMETRGRED
                                        * Referenced by:
                                        *   '<S44>/LCCMETRGRED'
                                        *   '<S68>/LCCMETRGRED'
                                        * Cme reduct
                                        */
extern int16_T LCHYSCMIRET;            /* Variable: LCHYSCMIRET
                                        * Referenced by: '<S44>/LCHYSCMIRET'
                                        * Cmi Hyst
                                        */
extern int16_T LCRTCMEREADY;           /* Variable: LCRTCMEREADY
                                        * Referenced by: '<S55>/LCRTCMEREADY'
                                        * Cme maxrate
                                        */
extern int16_T LCRTCMERED;             /* Variable: LCRTCMERED
                                        * Referenced by: '<S68>/LCRTCMERED'
                                        * Cme minrate
                                        */
extern int16_T LCRTCMERET;             /* Variable: LCRTCMERET
                                        * Referenced by: '<S74>/LCRTCMERET'
                                        * Cme maxrate
                                        */
extern int16_T LCRTCMERETLOW;          /* Variable: LCRTCMERETLOW
                                        * Referenced by: '<S74>/LCRTCMERETLOW'
                                        * Cme maxrate low
                                        */
extern int16_T VTLCCMEOFFI[7];         /* Variable: VTLCCMEOFFI
                                        * Referenced by:
                                        *   '<S55>/VTLCCMEOFFI'
                                        *   '<S80>/VTLCCMEOFFI'
                                        * Cme sat
                                        */
extern int16_T VTLCCMETRG[7];          /* Variable: VTLCCMETRG
                                        * Referenced by:
                                        *   '<S55>/VTLCCMETRG'
                                        *   '<S80>/VTLCCMETRG'
                                        * Cme sat
                                        */
extern int16_T THLCAXCTF;              /* Variable: THLCAXCTF
                                        * Referenced by: '<S14>/THLCAXCTF'
                                        * AxCAN threshold
                                        */
extern uint16_T BKLCCMETRG[7];         /* Variable: BKLCCMETRG
                                        * Referenced by:
                                        *   '<S56>/BKLCCMETRG'
                                        *   '<S81>/BKLCCMETRG'
                                        * Bk speed
                                        */
extern uint16_T BKRPMLCTRGWOT[5];      /* Variable: BKRPMLCTRGWOT
                                        * Referenced by: '<S17>/BKRPMLCTRGWOT'
                                        * Bk speed
                                        */
extern uint16_T LCGASIDLE;             /* Variable: LCGASIDLE
                                        * Referenced by:
                                        *   '<S40>/LCGASIDLE'
                                        *   '<S44>/LCGASIDLE'
                                        *   '<S50>/LCGASIDLE'
                                        * Lc WOT
                                        */
extern uint16_T LCGASWOT;              /* Variable: LCGASWOT
                                        * Referenced by: '<S51>/LCGASWOT'
                                        * Lc WOT
                                        */
extern uint16_T LCVEHAXINTSAT;         /* Variable: LCVEHAXINTSAT
                                        * Referenced by:
                                        *   '<S65>/LCVEHAXINTSAT'
                                        *   '<S88>/LCVEHAXINTSAT'
                                        *   '<S22>/LCVEHAXINTSAT'
                                        * Threshold to swith to VehLcAxIntSat to VehSpeedFront
                                        */
extern uint16_T LCVEHSPDEND;           /* Variable: LCVEHSPDEND
                                        * Referenced by: '<S40>/LCVEHSPDEND'
                                        * Lc End
                                        */
extern uint16_T VTLCRPMTRG[7];         /* Variable: VTLCRPMTRG
                                        * Referenced by:
                                        *   '<S55>/VTLCRPMTRG'
                                        *   '<S80>/VTLCRPMTRG'
                                        * Rpm trg
                                        */
extern uint8_T NLCKM;                  /* Variable: NLCKM
                                        * Referenced by: '<S45>/Enable_Lc_Km'
                                        * Km Lc
                                        */
extern uint8_T CNTLCWAMSG;             /* Variable: CNTLCWAMSG
                                        * Referenced by: '<S4>/Chart_LaunchCtrl'
                                        * counter
                                        */
extern uint8_T ENAWMAXLEVEL;           /* Variable: ENAWMAXLEVEL
                                        * Referenced by:
                                        *   '<S8>/ENAWMAXLEVEL'
                                        *   '<S26>/ENAWMAXLEVEL'
                                        * selector
                                        */
extern uint8_T ENLCCTRL;               /* Variable: ENLCCTRL
                                        * Referenced by: '<S39>/ENLCCTRL'
                                        * selector
                                        */
extern uint8_T ENLCMAXLEVEL;           /* Variable: ENLCMAXLEVEL
                                        * Referenced by:
                                        *   '<S8>/ENLCMAXLEVEL'
                                        *   '<S42>/ENLCMAXLEVEL'
                                        * selector
                                        */
extern uint8_T ENLCTOEXITCAN;          /* Variable: ENLCTOEXITCAN
                                        * Referenced by: '<S28>/ENLCTOEXITCAN'
                                        * selector
                                        */
extern uint8_T LCGEAREND;              /* Variable: LCGEAREND
                                        * Referenced by:
                                        *   '<S40>/LCGEAREND'
                                        *   '<S50>/LCGEAREND'
                                        * selector
                                        */
extern uint8_T NUMLCTRIP;              /* Variable: NUMLCTRIP
                                        * Referenced by:
                                        *   '<S8>/NUMLCTRIP'
                                        *   '<S45>/Enable_Lc_Km'
                                        * selector
                                        */
extern uint8_T VTAWOVRLEV[4];          /* Variable: VTAWOVRLEV
                                        * Referenced by: '<S4>/Chart_LaunchCtrl'
                                        * select ctrl
                                        */
extern uint8_T VTENTCMAXLEVEL[5];      /* Variable: VTENTCMAXLEVEL
                                        * Referenced by:
                                        *   '<S8>/VTENTCMAXLEVEL'
                                        *   '<S26>/VTENTCMAXLEVEL'
                                        * Selector
                                        */
extern uint8_T VTLCIDXCTF[4];          /* Variable: VTLCIDXCTF
                                        * Referenced by: '<S12>/VTLCIDXCTF'
                                        * idx cutoff
                                        */
extern uint8_T VTTCOVRLEV[4];          /* Variable: VTTCOVRLEV
                                        * Referenced by: '<S4>/Chart_LaunchCtrl'
                                        * select ctrl
                                        */
extern void LaunchCtrl_Lc_Dis(void);
extern void LaunchCtrl_Lc_Idle(void);
extern void LaunchCtrl_Lc_CtfLim(uint16_T rtu_vehspeed_index, int32_T
  rtu_vehspeed_ratio);
extern void LaunchCtrl_Lc_SatLim(uint16_T rtu_vehspeed_index, int32_T
  rtu_vehspeed_ratio);
extern void LaunchCtrl_Lc_Ret(uint8_T rtu_flgRetTO, int16_T rtu_CmeDriverP);
extern void LaunchCtrl_Lc_Reduct(void);
extern void LaunchCtrl_Lc_To_Idle(void);
extern void LaunchCtrl_Init(void);
extern void LaunchCtrl_T10ms_Init(void);
extern void LaunchCtrl_T10ms(void);
extern void LaunchCtrl_PreTDC(void);

/* Exported data declaration */

/* Declaration for custom storage class: EE_CSC */
extern uint32_T EELcKm;                /* '<Root>/Data Store Memory28' */

/* Km Lc POR */
extern uint16_T EELcTrip;              /* '<Root>/Data Store Memory20' */

/* counter Lc Trip */
#endif                                 /* RTW_HEADER_LaunchCtrl_private_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
