/*****************************************************************************************************************/
/* $HeadURL:: http://***********:8080/svn/Rep_Bo/EM/appl_calib/branches/LL_MV6_32_DIAG/tree/DD/COMMO#$   */
/* $ Description:                                                                                                */
/* $Revision:: 13831  $                                                                                          */
/* $Date:: 2022-02-17 16:01:18 +0100 (gio, 17 feb 2022)   $                                                      */
/* $Author:: LanaL                   $                                                                       */
/*****************************************************************************************************************/
#ifndef _CANMGM_950_H_
#define _CANMGM_950_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "can.h"
#include "canmgm.h"
#include "syncmgm.h"
#include "digitalin.h"
#include "analogin.h"
#include "mathlib.h"
#include "utils.h"
#include "stub.h"
#include "gaspos_mgm.h"
#include "thrposmgm.h"
#include "engflag.h"
#include "antitampering_out.h"
#include "diagcanmgm.h"
#include "trac_ctrl.h"
#include "rpm_limiter.h"
#include "creep_limiter_mgm.h"
#include "cmisatmgm_out.h"
#include "trq_driver.h"
#include "launchctrl_out.h"
#include "temp_mgm.h"
#include "trc2wzero_out.h"
#include "gearshift_mgm.h"
#include "tairdash_out.h"
#include "immo_app.h"
#include "trq_est.h"
#include "air_mgm.h"
#include "vspeed_mgm.h"
#include "diagmgm_out.h"
#include "ptrain_diag.h"
#include "TransportLock_out.h"
#include "gasposfilt_mgm.h"
#include "HeatGripDriveMgm.h"

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* ENG1 */
#define ENGINESPEED_CAN_GAIN (0) // 2^-0 -> 2^-0
#define ENGINESPEED_CAN_OFFSET (int32_T)(0.0f * 1.0f) // 0 rpm

#define VEHSPEED_CAN_GAIN (1) // 2^-3 -> 2^-4
#define VEHSPEED_CAN_OFFSET (int32_T)(0.0f * 16.0f) // 0 Km/h
/********/

/* ENG2 */
#define WATERTEMP_CAN_GAIN (4) // 2^-4 -> 2^-0
#define WATERTEMP_CAN_OFFSET (int32_T)(-40.0f * 16.0f) // 0 �C

#define QFUELLIT_CAN_GAIN (4) // 2^-8 -> 2^-4
#define QFUELLIT_CAN_OFFSET (int32_T)(0.0f * 256.0f) // 0 l/h
/********/

/* ENG4 */
#define CMEEST_CAN_GAIN (0) // 2^-0 -> 2^-0
#define CMEEST_CAN_OFFSET (int32_T)(0.0f * 32.0f) // 0 Nm
#define CMEEST_CAN_MIN (int32_T)(-1024.0f * 32.0f) // -1024 Nm

#define GASPOSCC_CAN_GAIN (4) // 2^-4 -> 2^-0
#define GASPOSCC_CAN_OFFSET (int32_T)(0.0f * 1.0f) // 0 bar
/********/

/* ENG5 */
#define VSPEEDCCSP_CAN_GAIN (0) // 2^-0 -> 2^-0
#define VSPEEDCCSP_CAN_OFFSET (int32_T)(0.0f * 1.0f) // 0 Km/h

#define TAIRDASH_CAN_GAIN (4) // 2^-0 -> 2^-4
#define TAIRDASH_CAN_OFFSET (int32_T)(-40.0f * 16.0f) // -40 �C
/********/

/* ENG7 */
#define AIRTEMP_CAN_GAIN (4) // 2^-4 -> 2^-0
#define AIRTEMP_CAN_OFFSET (int32_T)(-40.0f * 16.0f) // 0 �C

#define PRESINTK_CAN_GAIN (3) // 2^-0 -> 2^8
#define PRESINTK_CAN_OFFSET (int32_T)(0.0f * 8.0f) // 0 mBar

#define ANGTHR_CAN_GAIN (4) // 2^-4 -> 2^-0
#define ANGTHR_CAN_OFFSET (int32_T)(0.0f * 1.0f) // 0 bar

#define GASPOS_CAN_GAIN (5) // 2^-5 -> 2^-0
#define GASPOS_CAN_OFFSET (int32_T)(0.0f * 1.0f) // 0 bar
/********/

/* ABS */
#define VEHSPEEDFRONT_ABS_CAN_GAIN (0) // 2^-0 -> 2^-0
#define VEHSPEEDFRONT_ABS_CAN_OFFSET (int32_T)(0.0f * 1.0f) // 0 Km/h

#define VEHSPEEDREAR_ABS_CAN_GAIN (0) // 2^-0 -> 2^-0
#define VEHSPEEDREAR_ABS_CAN_OFFSET (int32_T)(0.0f * 1.0f) // 0 Km/h

#define FRONTWHLPRESSURE_CAN_GAIN (4) // 2^-0 -> 2^-4
#define FRONTWHLPRESSURE_CAN_OFFSET (int32_T)(0.0f * 16.0f) // 0 bar
/********/

/* DASH */
#define VSCCSETUP_CAN_GAIN (3) // 2^-1 -> 2^-4
#define VSCCSETUP_CAN_OFFSET (int32_T)(0.0f * 16.0f) // 0 Km/h
/********/

/* MCU1 */
#define TAIRENV_CAN_GAIN (4) // 2^-0 -> 2^-4
#define TAIRENV_CAN_OFFSET (int32_T)(-40.0f * 16.0f) // -40 �C
/********/

/* E-LEAN */
#define AX_CAN_GAIN (2) // 1/100
#define AX_CAN_OFFSET (int32_T)(-163.8f * 100.0f) // -163.8 deg
#define AX_CAN_MIN (int32_T)(-163.8f * 100.0f) // -163.8 deg
#define AX_CAN_MAX (int32_T)(163.8f * 100.0f) // 163.8 deg

#define AY_CAN_GAIN (2) // 1/100
#define AY_CAN_OFFSET (int32_T)(-163.8f * 100.0f) // -163.8 deg
#define AY_CAN_MIN (int32_T)(-163.8f * 100.0f) // -163.8 deg
#define AY_CAN_MAX (int32_T)(163.8f * 100.0f) // 163.8 deg

#define AZ_CAN_GAIN (2) // 1/100
#define AZ_CAN_OFFSET (int32_T)(-163.8f * 100.0f) // -163.8 deg
#define AZ_CAN_MIN (int32_T)(-163.8f * 100.0f) // -163.8 deg
#define AZ_CAN_MAX (int32_T)(163.8f * 100.0f) // 163.8 deg

#define WX_CAN_GAIN (2) // 1/100
#define WX_CAN_OFFSET (int32_T)(-163.8f * 100.0f) // -163.8 deg/s
#define WX_CAN_MIN (int32_T)(-163.8f * 100.0f) // -163.8 deg/s
#define WX_CAN_MAX (int32_T)(163.8f * 100.0f) // 163.8 deg/s

#define WY_CAN_GAIN (2) // 1/100
#define WY_CAN_OFFSET (int32_T)(-163.8f * 100.0f) // -163.8 deg/s
#define WY_CAN_MIN (int32_T)(-163.8f * 100.0f) // -163.8 deg/s
#define WY_CAN_MAX (int32_T)(163.8f * 100.0f) // 163.8 deg/s

#define WZ_CAN_GAIN (2) // 1/100
#define WZ_CAN_OFFSET (int32_T)(-163.8f * 100.0f) // -163.8 deg/s
#define WZ_CAN_MIN (int32_T)(-163.8f * 100.0f) // -163.8 deg/s
#define WZ_CAN_MAX (int32_T)(163.8f * 100.0f) // 163.8 deg/s

#define PITCH_CAN_GAIN (2) // 1/100
#define PITCH_CAN_OFFSET (int32_T)(-163.8f * 100.0f) // -163.8 deg
#define PITCH_CAN_MIN (int32_T)(-163.8f * 100.0f) // -163.8 deg
#define PITCH_CAN_MAX (int32_T)(163.8f * 100.0f) // 163.8 deg

#define ROLL_CAN_GAIN (2) // 1/100
#define ROLL_CAN_OFFSET (int32_T)(-163.8f * 100.0f) // -163.8 deg
#define ROLL_CAN_MIN (int32_T)(-163.8f * 100.0f) // -163.8 deg
#define ROLL_CAN_MAX (int32_T)(163.8f * 100.0f) // 163.8 deg

#define GX_CAN_GAIN (2) // 1/100
#define GX_CAN_OFFSET (int32_T)(-163.8f * 100.0f) // -163.8 deg
#define GX_CAN_MIN (int32_T)(-163.8f * 100.0f) // -163.8 deg
#define GX_CAN_MAX (int32_T)(163.8f * 100.0f) // 163.8 deg

#define GY_CAN_GAIN (2) // 1/100
#define GY_CAN_OFFSET (int32_T)(-163.8f * 100.0f) // -163.8 deg
#define GY_CAN_MIN (int32_T)(-163.8f * 100.0f) // -163.8 deg
#define GY_CAN_MAX (int32_T)(163.8f * 100.0f) // 163.8 deg

#define GZ_CAN_GAIN (2) // 1/100
#define GZ_CAN_OFFSET (int32_T)(-163.8f * 100.0f) // -163.8 deg
#define GZ_CAN_MIN (int32_T)(-163.8f * 100.0f) // -163.8 deg
#define GZ_CAN_MAX (int32_T)(163.8f * 100.0f) // 163.8 deg
/********/

/* GPS */
#define ALT_GPS_GAIN (1) // 1/10
#define ALT_GPS_OFFSET (int32_T)(0.0f * 10.0f) // 0 m
#define ALT_GPS_MIN (int32_T)(0.0f * 10.0f) // 0 m
#define ALT_GPS_MAX (int32_T)(6553.5f * 10.0f) // 6553.5 m

#define SPEED_GPS_GAIN (4) // 2^-0 -> 2^-4
#define SPEED_GPS_OFFSET (int32_T)(0.0f * 16.0f) // 0 Km/h
#define SPEED_GPS_MIN (int32_T)(0.0f * 16.0f) // 0 Km/h
#define SPEED_GPS_MAX (int32_T)(511.0f * 16.0f) // 511 Km/h
/********/

/* ERRS */
#define LSQ 11
#define RRS_N_RETRY 2u
#define RRS_INIT 0u
#define RRS_RND  1u
#define RRS_REQ  2u
#define RRS_SIGN 3u
#define RRS_ACK  4u
#define RRS_NACK 5u
#define RRS_STOP 6u
/********/

/************************/
/********* ENG1 *********/
/************************/
typedef struct ENG1_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  GearEngaged0   :4;
            vuint8_T  VehicleSpeed0  :4;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  VehicleSpeed1   :8;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  E1Integrity2   :8;
        } B;
    } Byte2;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  EngineSpeed3   :8;
        } B;
    } Byte3;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  EngineSpeed4   :8;
        } B;
    } Byte4;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  E1AliveCounter5   :3;
            vuint8_T  MILLampSts5   :1;
            vuint8_T  FuelPumpCmd5   :1;
            vuint8_T  KitMap5   :1;
            vuint8_T  SideStandSts5   :1;
            vuint8_T  StarterMotor5   :1;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  ATroutineEnable6   :1;
            vuint8_T  EngineOverspeedLimit6   :2;
            vuint8_T  WarningLampSts6   :1;
            vuint8_T  VehicleSpeedValidData6   :1;
            vuint8_T  FLCCtrlAct6   :1;
            vuint8_T  EngineSpeedValidData6   :1;
            vuint8_T  GearEngagedValidData6   :1;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  RidingMode7   :3;
            vuint8_T  HeatedGripPresence   :1; //niso added.
            vuint8_T  RidingModeTimeOut7   :1;
            vuint8_T  FlgTracCtrl7   :1;
            vuint8_T  ENRIDINGMODE_3_7   :1;
            vuint8_T  TracCtrlAct7   :1;
        } B;
    } Byte7;
} ENG1_T;

/************************/
/********* ENG2 *********/
/************************/
typedef struct ENG2_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  WaterTemperature0   :8;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  E2Integrity1   :8;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  E2AliveCounter2   :2;
            vuint8_T  ParkingBrakeFlag   :1; //niso added.
            vuint8_T  BrakeSignal   :1; //niso added.
            vuint8_T  OilPressureStatus2   :1;
            vuint8_T  AMTPresence2   :1;
            vuint8_T  FuelConsumption2   :2;
        } B;
    } Byte2;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  FuelConsumption3   :8;
        } B;
    } Byte3;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  WaterTemperatureValidData4   :1;
            vuint8_T  LcStatus4   :3;
            vuint8_T  FuelConsumptionValidData4   :1;
            vuint8_T  VehicleSpeedControllerStatus4   :3;
        } B;
    } Byte4;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  LcKm5   :3;
            vuint8_T  SL_TC2W_Timeout5   :3;
            vuint8_T  SL_TC2W5   :2;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  SL_TC2W6   :1;
            vuint8_T  LcTrip6   :2;
            vuint8_T  QuickShiftPresence6   :1;
            vuint8_T  IDModelYear6   :4;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  IDModelV7   :8;
        } B;
    } Byte7;
} ENG2_T;

/************************/
/********* ENG3 *********/
/************************/
typedef struct ENG3_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  airerror0   :1;
            vuint8_T  phaseerror0   :1;
            vuint8_T  airtemperror0   :1;
            vuint8_T  wattemperror0   :1;
            vuint8_T  throttleerror0   :1;
            vuint8_T  lambdasenserror0   :1;
            vuint8_T  exhvalveerror0   :1;
            vuint8_T  egearerror0   :1;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  injlowerror1   :1;
            vuint8_T  Dummy1_0   :3;
            vuint8_T  injuperror1   :1;
            vuint8_T  Dummy1_1   :3;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  gaserror2   :1;
            vuint8_T  Dummy2_1   :1;
            vuint8_T  vrserror2   :1;
            vuint8_T  igncoilerror2   :1;
            vuint8_T  Dummy2_0   :1;
            vuint8_T  lambdasens2error2   :1;
            vuint8_T  caterror2   :1;
            vuint8_T  sparkpeakerror2   :1;
        } B;
    } Byte2;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  vehspeederror3   :1;
            vuint8_T  vregerror3    :1;
            vuint8_T  eleannodeerror3   :1;
            vuint8_T  ecuerror3   :1;
            vuint8_T  knockcoherror3   :1;
            vuint8_T  dbwerror3   :1;
            vuint8_T  senssupplyerror3   :1;
            vuint8_T  clutcherror3   :1;
        } B;
    } Byte3;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  safetyerror4   :1;
            vuint8_T  ioncherror4   :1;
            vuint8_T  tipovererror4   :1;
            vuint8_T  gearsenserror4   :1;
            vuint8_T  absnodeerror4   :1;
            vuint8_T  vehcanerror4   :1;
            vuint8_T  licplateerror4   :1;
            vuint8_T  dashnodeerror4   :1;
        } B;
    } Byte4;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  VINerror5   :1;
            vuint8_T  QSerror5   :1;
            vuint8_T  misfireerror5   :1;
            vuint8_T  egearnoderror5   :1;
            vuint8_T  mcunodeerror5   :1;
            vuint8_T  tpmsnoderror5   :1;
            vuint8_T  lhbnodeerror5   :1;
            vuint8_T  rhbnodeerror5   :1;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  ptraincanerror6   :1;
            vuint8_T  gpsnodeerror6   :1;
            vuint8_T  Dummy6_0   :1;
            vuint8_T  Download6   :1;
            vuint8_T  Immoflg6   :1;
            vuint8_T  E3AliveCounter6   :3;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  E3Integrity7   :8;
        } B;
    } Byte7;
} ENG3_T;

/************************/
/********* ENG4 *********/
/************************/
typedef struct ENG4_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Effective_Torque0   :8;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Effective_Torque1   :8;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy2_0   :1;
            vuint8_T  GasPos2   :7;
        } B;
    } Byte2;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  GearDWNEnable3   :1;
            vuint8_T  GearUPEnable3    :1;
            vuint8_T  GearNEnable3   :1;
            vuint8_T  StartSignal3   :1;
            vuint8_T  Cutoff3   :1;
            vuint8_T  E4IDMux3   :3;
        } B;
    } Byte3;

    union
    {
        vuint8_T R;
        union
        {
            struct
            {
                vuint8_T  Reserved_4_0_4   :8;
            } M0;
            struct
            {
                vuint8_T  Reserved_4_1_4   :8;
            } M1;
            struct
            {
                vuint8_T  Reserved_4_2_4   :8;
            } M2;
            struct
            {
                vuint8_T  Reserved_4_3_4   :8;
            } M3;
            struct
            {
                vuint8_T  Reserved_4_4_4   :8;
            } M4;
            struct
            {
                vuint8_T  Reserved_4_5_4   :8;
            } M5;
        } B;
    } Byte4;

    union
    {
        vuint8_T R; 
        union
        {
            struct
            {
                vuint8_T  Reserved_4_0_5   :8;
            } M0;
            struct
            {
                vuint8_T  Reserved_4_1_5   :8;
            } M1;
            struct
            {
                vuint8_T  Reserved_4_2_5   :8;
            } M2;
            struct
            {
                vuint8_T  Reserved_4_3_5   :8;
            } M3;
            struct
            {
                vuint8_T  Reserved_4_4_5   :8;
            } M4;
            struct
            {
                vuint8_T  Reserved_4_5_5   :8;
            } M5;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        union
        {
            struct
            {
                vuint8_T  Reserved_4_0_6   :8;
            } M0;
            struct
            {
                vuint8_T  Reserved_4_1_6   :8;
            } M1;
            struct
            {
                vuint8_T  Reserved_4_2_6   :8;
            } M2;
            struct
            {
                vuint8_T  Reserved_4_3_6   :8;
            } M3;
            struct
            {
                vuint8_T  Reserved_4_4_6   :8;
            } M4;
            struct
            {
                vuint8_T  Reserved_4_5_6   :8;
            } M5;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  E4Integrity7   :8;
        } B;
    } Byte7;
} ENG4_T;

/************************/
/********* ENG5 *********/
/************************/
typedef struct ENG5_tag
{
    union
    {
        vuint8_T R; 
        union
        {
            struct
            {
                vuint8_T  MaxVSPCC0   :8;
            } M0;
            struct
            {
                vuint8_T  Reserved_5_1_0   :6;
                vuint8_T  DisEBK0   :1;
                vuint8_T  AuxCfg0   :1;
            } M1;
            struct
            {
                vuint8_T  AirTempDash0   :8;
            } M2;
            struct
            {
                vuint8_T  Reserved_5_3_0   :8;
            } M3;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        union
        {
            struct
            {
                vuint8_T  MinVSPCC1   :8;
            } M0;
            struct
            {
                vuint8_T  EnTCMax01   :4;
                vuint8_T  EnTCMax11   :4;
            } M1;
            struct
            {
                vuint8_T  VDAirTempDash1   :1;
                vuint8_T  TransportLock1   :2;
                vuint8_T  UnlockTime1   :5;
            } M2;
            struct
            {
                vuint8_T  Reserved_5_3_1   :8;
            } M3;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        union
        {
            struct
            {
                vuint8_T  MaxVSPLim2   :8;
            } M0;
            struct
            {
                vuint8_T  EnTCMax22   :4;
                vuint8_T  EnTCMax32   :4;
            } M1;
            struct
            {
                vuint8_T  UnlockTime2   :3;
                vuint8_T  Reserved_5_2_2   :5;
            } M2;
            struct
            {
                vuint8_T  Reserved_5_3_2   :8;
            } M3;
        } B;
    } Byte2;

    union
    {
        vuint8_T R;
        union
        {
            struct
            {
                vuint8_T  MinVSPLim3   :8;
            } M0;
            struct
            {
                vuint8_T  Reserved_5_1_3   :8;
            } M1;
            struct
            {
                vuint8_T  Reserved_5_2_3   :8;
            } M2;
            struct
            {
                vuint8_T  Reserved_5_3_3   :8;
            } M3;
        } B;
    } Byte3;

    union
    {
        vuint8_T R;
        union
        {
            struct
            {
                vuint8_T  Reserved_5_0_4   :8;
            } M0;
            struct
            {
                vuint8_T  Reserved_5_1_4   :8;
            } M1;
            struct
            {
                vuint8_T  Reserved_5_2_4   :8;
            } M2;
            struct
            {
                vuint8_T  Reserved_5_3_4   :8;
            } M3;
        } B;
    } Byte4;

    union
    {
        vuint8_T R;
        union
        {
            struct
            {
                vuint8_T  Dummy5_0   :8;
            } M0;
            struct
            {
                vuint8_T  EnTCMax45   :4;
                vuint8_T  RiderPresence5   :1; //niso added.
                vuint8_T  Dummy5_1   :3;
            } M1;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  EnHeatGripMaxLevel   :2; //niso added.
            vuint8_T  Dummy6_0   :3;
            vuint8_T  NcmKL15_6   :1;
            vuint8_T  E5IDMux6   :2;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  E5Integrity7   :8;
        } B;
    } Byte7;
} ENG5_T;

/************************/
/********* ENG7 *********/
/************************/
typedef struct ENG7_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  E7Integrity0   :8;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  BatteryVoltage   :8; //niso added.
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  AirTemperature2   :8;
        } B;
    } Byte2;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  PresIntake3   :8;
        } B;
    } Byte3;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  ClutchSignal4   :1;
            vuint8_T  AirTemperatureValidData4   :1;
            vuint8_T  AngThrottle4   :6;
        } B;
    } Byte4;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  AngThrottle5   :1;
            vuint8_T  GasPos5   :7;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  E7AliveCounter6   :3;
            vuint8_T  VDBatteryVoltage   :1; //niso added.
            vuint8_T  EnAwMaxLevel6   :3;
            vuint8_T  EnLcMaxLevel6   :1;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  EnLcMaxLevel7   :1;
            vuint8_T  ClutchSignal27   :1;
            vuint8_T  FanCmd7   :1;
            vuint8_T  EngineStarted7   :1;
            vuint8_T  IDVersion7   :4;
        } B;
    } Byte7;
} ENG7_T;

/************************/
/********* ELR1 *********/
/************************/
typedef struct ELR1_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  C_OB_Start0   :8;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  DebugModeCAN1   :8;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  ELeanReset2   :8;
        } B;
    } Byte2;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  EOL_req3   :8;
        } B;
    } Byte3;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  EOL_result4   :8;
        } B;
    } Byte4;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy5_0   :8;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy6_0   :8;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy7_0   :8;
        } B;
    } Byte7;
} ELR1_T;

/************************/
/********* EVIN *********/
/************************/
typedef struct EVIN_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  data0   :8;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  data1   :8;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  data2   :8;
        } B;
    } Byte2;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  data3   :8;
        } B;
    } Byte3;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  data4   :8;
        } B;
    } Byte4;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  data5   :8;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  data6   :8;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  data7   :8;
        } B;
    } Byte7;
} EVIN_T;

/************************/
/********* ERRS *********/
/************************/
typedef struct ERRS_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  RRSRequest0   :8;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  KLSRnd01   :8;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  KLSRnd02   :8;
        } B;
    } Byte2;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  KLSRnd03   :8;
        } B;
    } Byte3;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  KLSRnd04   :8;
        } B;
    } Byte4;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy5_0   :8;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy6_0   :2;
            vuint8_T  ERRSIdx6   :2;
            vuint8_T  ERRSAliveCounter6   :4;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  ERRSCheckSum7   :8;
        } B;
    } Byte7;
} ERRS_T;

/************************/
/********* KLS1 *********/
/************************/
typedef struct KLS1_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  KEY_BATTERY0   :1;
            vuint8_T  Dummy0_1   :1;
            vuint8_T  KEY_ERROR0   :2;
            vuint8_T  BOLT_IN0   :1;
            vuint8_T  BOLT_OUT0   :1;
            vuint8_T  Dummy0_0   :1;
            vuint8_T  LOCK_UNLOCK0   :1;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  MSG_ID_CNT1   :3;
            vuint8_T  Dummy1_1   :1;
            vuint8_T  KEY_IDENTIFICATION1   :2;
            vuint8_T  LF_FAULT1   :1;
            vuint8_T  Dummy1_0   :1;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  LOCKING_STATUS2   :1;
            vuint8_T  KL152   :1;
            vuint8_T  KEY_STORAGE2   :1;
            vuint8_T  KEY_STORAGE_STATE2   :3;
            vuint8_T  FREQUENCY_SETTING2   :2;
        } B;
    } Byte2;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy3_0   :6;
            vuint8_T  KEY_INTERNAL_IDENTIFICATION3   :2;
        } B;
    } Byte3;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  Dummy4_0   :8;
        } B;
    } Byte4;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy5_0   :8;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy6_0   :8;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  CRC_CONSISTENCY7   :8;
        } B;
    } Byte7;
} KLS1_T;

/************************/
/******* KLS_RRS ********/
/************************/
typedef struct KLS_RRS_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  KLSSign0   :8;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  KLSSign01   :8;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  KLSSign02   :8;
        } B;
    } Byte2;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  KLSSign03   :8;
        } B;
    } Byte3;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  KLSSign04   :8;
        } B;
    } Byte4;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy5_0   :8;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy6_0   :4;
            vuint8_T  KRAliveCounter6   :4;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  KRCheckSum7   :8;
        } B;
    } Byte7;
} KLS_RRS_T;

/************************/
/********* MCU1 *********/
/************************/
typedef struct MCU1_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  AirTempEnv0   :8;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  VFuelTankLevel1   :8;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  VFuelTankLevel2   :2;
            vuint8_T  RiderPresence2   :1;
            vuint8_T  NCMImmo2   :1;
            vuint8_T  VDBrakeSignal2   :1;
            vuint8_T  MainRelayCtrl2   :1;
            vuint8_T  EnPassengerHSMaxLev2   :2;
        } B;
    } Byte2;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  EnRiderHSMaxLev3   :2;
            vuint8_T  PassengerHSeatPres3   :1;
            vuint8_T  RiderHSeatPres3   :1;
            vuint8_T  EnHeatGripMaxLevel3   :2;
            vuint8_T  HeatedGripPresence3   :1;
            vuint8_T  VDVBatteryLevel3   :1;
            
        } B;
    } Byte3;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  BlinkerSx4   :1;
            vuint8_T  BlinkerDx4   :1;
            vuint8_T  VDAirTempEnv4   :1;
            vuint8_T  OilPressureSignal4   :1;
            vuint8_T  BrakeSignalLamp4   :1;
            vuint8_T  ParkingBrake4   :1;
            vuint8_T  TransportLock4   :1;
            vuint8_T  StarterMotorCtrl4   :1;
        } B;
    } Byte4;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  VBatteryLevel5   :8;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  FuelTankLevel6   :4;
            vuint8_T  Mcu1AliveCounter6   :4;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Mcu1CheckSum7   :8;
        } B;
    } Byte7;
} MCU1_T;

/************************/
/********* MCU3 *********/
/************************/
typedef struct MCU3_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  mcugenericerror0   :1;
            vuint8_T  Dummy0_0   :5;
            vuint8_T  vsenserror0   :1;
            vuint8_T  keylesserror0   :1;
            
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  ignerror1   :1;
            vuint8_T  imuerror1   :1;
            vuint8_T  mcurhbnodeerror1   :1;
            vuint8_T  mculhbnodeerror1   :1;
            vuint8_T  parkingbrakeerror1   :1;
            vuint8_T  fanerror1   :1;
            vuint8_T  hazardswerror1   :1;
            vuint8_T  turnswerror1   :1;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  airtemperror2   :1;
            vuint8_T  fuelpumperror2   :1;
            vuint8_T  loadpowererror2   :1;
            vuint8_T  rearbrakeswitcherror2   :1;
            vuint8_T  frontbrakeswitcherror2   :1;
            vuint8_T  mcuncmcanerror2   :1;
            vuint8_T  mcudashcanerror2   :1;
            vuint8_T  mcuabscanerror2   :1;
        } B;
    } Byte2;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  busofferror3   :1;
            vuint8_T  swbatteryerror3   :1;
            vuint8_T  shpillionerror3   :1;
            vuint8_T  dxrearblinkererror3   :1;
            vuint8_T  sxrearblinkererror3   :1;
            vuint8_T  dxfrontblinkererror3   :1;
            vuint8_T  sxfrontblinkererror3   :1;
            vuint8_T  startermotorerror3   :1;
        } B;
    } Byte3;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  rearbrakelamperror4   :1;
            vuint8_T  heatedgriperror4   :1;
            vuint8_T  hornerror4   :1;
            vuint8_T  shridererror4   :1;
            vuint8_T  ecmerror4   :1;
            vuint8_T  igperror4   :1;
            vuint8_T  vbatteryerror4   :1;
            vuint8_T  fuellevelerror4   :1;
        } B;
    } Byte4;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy5_0   :8;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy6_0   :4;
            vuint8_T  Mcu3AliveCounter6   :4;

        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Mcu3CheckSum7   :8;
        } B;
    } Byte7;
} MCU3_T;

/************************/
/********* DASH *********/
/************************/
typedef struct DASH_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  LcTimeout0   :1;
            vuint8_T  LcLevel0   :2;
            vuint8_T  RiderHSLevel0   :2;
            vuint8_T  VehicleSpeedControlStatus0   :2;
            vuint8_T  VehicleSpeedSetUp0   :1;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  VehicleSpeedSetUp1   :8;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  LowFuelFlag2   :1;
            vuint8_T  Dummy2_0   :1;
            vuint8_T  HeatedGripStatus2   :2;
            vuint8_T  DsAliveCounter2   :4;
        } B;
    } Byte2;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  TractionControlStatus3   :4;
            vuint8_T  ATroutineFlag3   :1;
            vuint8_T  SuspensionModeRequest3   :3;
        } B;
    } Byte3;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  GasSensitivity4   :2;
            vuint8_T  MaxEngineTorque4   :2;
            vuint8_T  EngineBrake4   :1;
            vuint8_T  EngineResponse4   :1;
            vuint8_T  RpmLimiter4   :1;
            vuint8_T  QuickShiftEN4   :1;
        } B;
    } Byte4;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy5_0   :8;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy6_0   :1;
            vuint8_T  PassengerHSLevel6   :2;
            vuint8_T  BKLStatus   :1;
            vuint8_T  BikeLoadStatus6   :2;
            vuint8_T  FLCLevel6   :2;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  DsCheckSum7   :8;
        } B;
    } Byte7;
} DASH_T;

/************************/
/******* DASH INFO ******/
/************************/
typedef struct DINFO_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  TC_Setup_request0   :3;
            vuint8_T  DashboardMode0   :1;
            vuint8_T  Data_LoggerSts0   :1;
            vuint8_T  QSCyborgMode0   :1;
            vuint8_T  ABSMode0   :2;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  AvailRange1   :8;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  AvailRange2   :2;
            vuint8_T  Dummy2_0   :1;
            vuint8_T  OTAUpdate2   :1;
            vuint8_T  TyreType2   :2;
            vuint8_T  InitialCheckFlag2   :1;
            vuint8_T  ABSDisable2   :1;
        } B;
    } Byte2;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  FuelConsAvgA3   :8;
        } B;
    } Byte3;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  FuelConsAvgB4   :8;
        } B;
    } Byte4;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy5_0   :4;
            vuint8_T  DsiAliveCounter5   :4;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Variant6   :8;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  DsiCheckSum7   :8;
        } B;
    } Byte7;
} DINFO_T;

/************************/
/********* ABS **********/
/************************/
typedef struct ABS_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  VehSpeedFrontCAN0   :8;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  VehSpeedFrontCAN1   :8;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  VehSpeedRearCAN2   :8;
        } B;
    } Byte2;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  VehSpeedRearCAN3   :8;
        } B;
    } Byte3;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  ABSWarningLamp4   :2;
            vuint8_T  ABSPresent4   :1;
            vuint8_T  Dummy4_1   :2;
            vuint8_T  VDVehSpeedRearCAN4   :1;
            vuint8_T  Dummy4_0   :1;
            vuint8_T  VDVehSpeedFrontCAN4   :1;
        } B;
    } Byte4;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Front_Wheel_Pressure5   :8;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy6_0   :6;
            vuint8_T  OCBDisabled6   :1;
            vuint8_T  VDFrontWheelPressure6   :1;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy7_0   :8;
        } B;
    } Byte7;
} ABS_T;

/************************/
/********* LHB **********/
/************************/
typedef struct LHB_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  PassingButton0   :1;
            vuint8_T  CyborgModulePresence0   :1;
            vuint8_T  CCSpeedDown0   :1;
            vuint8_T  CyborgAutoManualButton0   :1;
            vuint8_T  CCSpeedUp0   :1;
            vuint8_T  TurnLeftButton0   :1;
            vuint8_T  TurnResetButton0   :1;
            vuint8_T  TurnRightButton0   :1;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  JUpButton1   :1;
            vuint8_T  JDownButton1   :1;
            vuint8_T  JRightButton1   :1;
            vuint8_T  JLeftButton1   :1;
            vuint8_T  HazardButton1   :1;
            vuint8_T  HornButton1   :1;
            vuint8_T  HighBeamButton1   :1;
            vuint8_T  LowBeamButton1   :1;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  JOk2   :1;
            vuint8_T  ClutchSwitch2_2   :1;
            vuint8_T  ClutchSwitch1_2   :1;
            vuint8_T  GearDownButton2   :1;
            vuint8_T  GearUpButton1   :1;
            vuint8_T  Dummy3_0   :3;
        } B;
    } Byte2;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy3_0   :8;
        } B;
    } Byte3;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy4_0   :2;
            vuint8_T  LHsDashNodeError4   :1;
            vuint8_T  LHsEGearNodeError4   :1;
            vuint8_T  LHsStError4   :4;
        } B;
    } Byte4;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  LHsFirmVer5   :8;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  LHsCountryVer6   :4;
            vuint8_T  LHs_Counter6   :4;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  LHs_CheckSum7   :8;
        } B;
    } Byte7;
} LHB_T;

/************************/
/********* RHB **********/
/************************/
typedef struct RHB_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  FogLightButton0   :1;
            vuint8_T  DRLAutoManualButton0   :1;
            vuint8_T  RunStopButton0   :1;
            vuint8_T  ABSButton0   :1;
            vuint8_T  StartMap0   :1;
            vuint8_T  SLLCButton0   :1;
            vuint8_T  BrakeFrontSwitch0   :1;
            vuint8_T  Dummy0_0   :1;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy1_0   :8;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy2_0   :8;
        } B;
    } Byte2;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy3_0   :8;
        } B;
    } Byte3;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy4_0   :3;
            vuint8_T  RHsDashNodeError4   :1;
            vuint8_T  RHsStError4   :4;
        } B;
    } Byte4;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  RHsFirmVer5   :8;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  RHsCountryVer6   :4;
            vuint8_T  RHs_Counter6   :4;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  RHs_CheckSum7   :8;
        } B;
    } Byte7;
} RHB_T;

/************************/
/********* GPS_02 *******/
/************************/
typedef struct GPS02_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Altitude0   :8;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Altitude1   :8;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Satellites2   :6;
            vuint8_T  Dummy2_0   :2;
        } B;
    } Byte2;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  SW_revision3   :8;
        } B;
    } Byte3;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy4_0   :8;
        } B;
    } Byte4;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  Dummy5_0   :8;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy6_0   :5;
            vuint8_T  Speed6   :3;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Speed7   :6;
            vuint8_T  Dummy7_0   :2;
        } B;
    } Byte7;
} GPS02_T;

/************************/
/********* GPS_04 *******/
/************************/
typedef struct GPS04_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  NvmModuleError0   :1;
            vuint8_T  FLashModuleError0   :1;
            vuint8_T  GpsModuleError0   :1;
            vuint8_T  Dummy1_1   :2;
            vuint8_T  GPS_Fix0   :1;
            vuint8_T  Dummy1_0   :1;
            vuint8_T  GPS_Error0   :1;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  GPS_satellite_number   :8;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy2_0   :8;
        } B;
    } Byte2;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy3_0   :8;
        } B;
    } Byte3;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy4_0   :8;
        } B;
    } Byte4;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  Dummy5_0   :8;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy6_0   :8;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy7_0   :8;
        } B;
    } Byte7;
} GPS04_T;

/************************/
/********* AXP1 *********/
/************************/
typedef struct AXP1_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  AzCAN0   :8;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  AzCAN1   :8;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  AyCAN2   :8;
        } B;
    } Byte2;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  AyCAN3   :8;
        } B;
    } Byte3;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  AxCAN4   :8;
        } B;
    } Byte4;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  AxCAN5   :8;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Tacc6   :8;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Tacc7   :8;
        } B;
    } Byte7;
} AXP1_T;

/************************/
/********* AXP2 *********/
/************************/
typedef struct AXP2_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  GzCAN0   :8;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  GzCAN1   :8;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  GyCAN2   :8;
        } B;
    } Byte2;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  GyCAN3   :8;
        } B;
    } Byte3;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  GxCAN4   :8;
        } B;
    } Byte4;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  GxCAN5   :8;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy6_0   :2;
            vuint8_T  OB_OFF_Gz_Validity6   :1;
            vuint8_T  OB_OFF_Gy_Validity6   :1;
            vuint8_T  OB_OFF_Gx_Validity6   :1;
            vuint8_T  OB_OFF_Pitch_Validity6   :1;
            vuint8_T  OB_OFF_Roll_Validity6   :1;
            vuint8_T  OB_Status6   :1;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  SpeedCounter7   :8;
        } B;
    } Byte7;
} AXP2_T;

/************************/
/********* AXP3 *********/
/************************/
typedef struct AXP3_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  PitchCAN0   :8;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  PitchCAN1   :8;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  RollCAN2   :8;
        } B;
    } Byte2;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  RollCAN3   :8;
        } B;
    } Byte3;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  EL3AliveCounter4   :2;
            vuint8_T  VDRollCAN4   :1;
            vuint8_T  VDPitchCAN4   :1;
            vuint8_T  VehSpeedAbsetCAN4   :4;
        } B;
    } Byte4;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  VehSpeedAbsetCAN5   :8;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Temp_26   :8;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  EL3Integrity7   :8;
        } B;
    } Byte7;
} AXP3_T;

/************************/
/********* AXP4 *********/
/************************/
typedef struct AXP4_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Roll_Valid_Data0   :8;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Temp_Valid_data1   :8;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Ax_Valid_Data2   :8;
        } B;
    } Byte2;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  Ay_Valid_Data3   :8;
        } B;
    } Byte3;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Az_Valid_Data4   :8;
        } B;
    } Byte4;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Gx_Valid_Data5   :8;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Gy_Valid_Data6   :8;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Gz_Valid_Data7   :8;
        } B;
    } Byte7;
} AXP4_T;

/************************/
/******** E-GEAR ********/
/************************/
typedef struct EGEAR_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  GearShiftWait0   :3;
            vuint8_T  DOWN_Request_flag0   :1;
            vuint8_T  NEUTRAL_Request_flag0   :1;
            vuint8_T  UP_Request_flag0   :1;
            vuint8_T  VD_request_flag0   :1;
            vuint8_T  VD_VGearPos_CAN0   :1;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy1_0   :8;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy2_0   :8;
        } B;
    } Byte2;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy3_0   :8;
        } B;
    } Byte3;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  Dummy4_0   :8;
        } B;
    } Byte4;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy5_0   :2;
            vuint8_T  VGearPos_CAN5   :6;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  VGearPos_CAN6   :4;
            vuint8_T  EgAliveCounter6   :4;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  EgCheckSum7   :8;
        } B;
    } Byte7;
} EGEAR_T;

/************************/
/********* TPMS *********/
/************************/
typedef struct TPMS_tag
{
    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  LOC0  :8;
        } B;
    } Byte0;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  PRE1  :8;
        } B;
    } Byte1;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  TEM2  :8;
        } B;
    } Byte2;

    union
    {
        vuint8_T R;
        struct
        {
            vuint8_T  WFC3  :4;
            vuint8_T  BAL3  :1;
            vuint8_T  Dummy3_0  :3;
        } B;
    } Byte3;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy4_0  :4;
            vuint8_T  RST4  :4;
        } B;
    } Byte4;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  TIM5  :8;
        } B;
    } Byte5;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  TIM6  :8;
        } B;
    } Byte6;

    union
    {
        vuint8_T R; 
        struct
        {
            vuint8_T  Dummy7_0  :1;
            vuint8_T  COM7  :1;
            vuint8_T  SDA7  :1;
            vuint8_T  TTD7  :2;
            vuint8_T  PDT7  :3;
        } B;
    } Byte7;
} TPMS_T;

/*-----------------------------------*
 * IMPORTED CALIBRATION
 *-----------------------------------*/
// INTERNAL
extern int8_T   FOTYRETYPE;
extern uint8_T  IDXRMDISESS;
extern uint8_T  ENCNTWRONG;
extern uint8_T  ENCRCWRONG;
extern uint8_T  DISCANCRC;
extern uint8_T  DISCANCNT;
extern int8_T   FOIDMUXE4;
extern int8_T   FOIDMUXE5;
extern uint8_T  MAXVSPLIM;
extern uint8_T  MINVSPLIM;
extern uint16_T CANSENDWAITSTOP;
extern uint16_T CANSENDWAITSTART;
extern uint8_T  QUICKBUSOFFRECMAXREP;
extern uint8_T  SLOWBUSOFFRECMAXREP;
extern uint8_T  TIMWAITIMMO;
extern uint8_T  DISABLEIMMO;
extern uint8_T  KITMAP;
extern uint16_T TNOCANDIAGAFTKEYON;
extern uint8_T  SELRPMCAN;
extern uint8_T  TIMCRPFREQ;
extern uint8_T  IDMODELYEAR;
extern uint8_T  IDMODELV;
extern uint16_T FCELEANTO;
extern uint8_T  ENFRZVEHCANENGSTR;
extern uint16_T NMSGCANNODE2EN;
extern uint16_T NMSGCANNODE3EN;
extern uint16_T NMSGCANNODE4EN;
extern uint16_T NMSGCANNODE5EN;
extern uint16_T CANVBATTHRMIN;
extern int8_T   FOVDPITCHCAN;
extern int8_T   FOVDROLLCAN;
extern uint16_T LAMCANGAIN[N_MSG_LAM];
extern uint16_T LAMCANOFS[N_MSG_LAM];
extern uint8_T  TIMGPDEBOUNCE;
extern uint8_T  TIMGPCOUNTER;
extern uint8_T  MINFWPRESBRAKE;

// EXTERNAL
extern uint8_T ENTRACCTRL;
extern uint8_T LOGLAMPOUT[NUMLOGLMP];
extern uint8_T ENRIDINGMODE[RM_SIZE];
//niso test
extern uint8_T  FORCEHORNSIGNAL;

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CANMGM_Init(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CANMGM_CanSend5ms(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CANMGM_CanSend10ms(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CANMGM_CanSend100ms(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CANMGM_CanSendTDC(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CANMGM_CanSendDwlReq(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CANMGM_CanRecv5ms(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CANMGM_CanRecv10ms(void);

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void CANMGM_CanRecv100ms(void);

#endif

