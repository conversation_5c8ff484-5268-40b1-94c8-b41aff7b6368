/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#include "digio.h"
#include "task.h"
#include "Timing.h"
#include "OS_api.h"
#include "tasksdefs.h"
#include "analogin.h"
#include "diagmgm_out.h"
#include "Diagcanmgm.h"
#include "canmgm.h"
#include "PTrain_Diag.h"
#include "digitalin.h"


/* calibrazioni */

/* global vars */
uint16_T VCrashSignal;
uint8_T DiagBrakeLamp;
uint8_T BrakeSignal;
uint8_T ParkBrakeSignal;
uint8_T RiderPresence;
uint8_T OilPresSignal;
uint8_T OilPresSwitch;
uint8_T CrashSignal;
uint8_T KeySignal1;
uint8_T KeySignal;
uint8_T KeyEnDiagSignal;
uint8_T TrestleSignal;
uint16_T TimRollEngOff;
uint8_T KeySig4Coil;
uint8_T FlgEnQs;
//niso
uint8_T HornSignal;
uint8_T HornSwitch;


#ifdef _BUILD_ANALOGQS_
#else
uint8_T GearDownSignal;                /* Down shift flag */
uint8_T GearUpSignal;                  /* Up shift flag */
#endif

static uint8_T cntTrestleSignal;
static uint8_T oldTrestleSignal;
static uint8_T oldGearUpSignal;
static uint8_T cntGearDownSignal;
static uint8_T oldGearDownSignal;
static uint8_T oldKeySignal;
static uint8_T cntKeySignal;
static uint8_T oldKeyEnDiagSignal;
static uint8_T cntKeyEnDiagSignal;
static uint8_T oldDiagBrakeLamp;
static uint8_T cntDiagBrakeLamp;
static uint8_T cntBrakeSignal;
static uint8_T oldBrakeSignal;
static uint8_T cntOilPresSignal;
static uint8_T oldOilPresSignal;
static uint8_T oldParkBrakeSignal;
static uint8_T cntParkBrakeSignal;
static uint8_T oldRiderPresence;
static uint8_T cntRiderPresence;
static uint8_T oldButtonMapSignal;
static uint32_T cntOPAbsTdc;
static uint16_T cntOPOff;
//niso
static uint8_T HornSignalDebounced;    // Debounce edilmis �ikis
static uint8_T HornSignalOld;          // �nceki deger
static uint8_T HornSignalCounter;      // Debounce sayaci

extern uint8_T CNTDEBOUNCE;
extern uint8_T CNTCLUDEBOUNCE;
extern uint8_T CNTKEYDEBOUNCE;
extern uint8_T CNTGEARSHIFTDEBOUNCE;
extern uint32_T CNTOPABSTDC;
extern uint16_T THRQSPRESENCE;
extern uint16_T TIMDEBOPOFF;

extern uint8_T  USEA1RSIGNAL;
extern uint8_T  USEA2RSIGNAL;
extern uint16_T ANGROLLENGOFF;
extern uint16_T TIMROLLENGOFF;

void    Reset_DigInSignals(void)
/* initializes digital signals */
{
    KeySignal = 1;
    KeySig4Coil = 1;
    cntKeySignal = 0;
    KeyEnDiagSignal = 0;
    cntKeyEnDiagSignal = 0;
    TrestleSignal = 0;
    cntTrestleSignal = 0;
    DiagBrakeLamp = 0;
    cntDiagBrakeLamp = 0;
    BrakeSignal = 0;
    OilPresSignal = 0;
    OilPresSwitch = 0;
    cntOPAbsTdc = 0;
    GearUpSignal = 0;
    GearDownSignal = 0;
    TimRollEngOff = 0;
    FlgEnQs = 0;
    //cntBrakeSignal = 0;
    //niso implementation
    HornSignal = 0;
    //HornSwitch = 0;
}
/* HornSignal = HornSignalCAN;
*/

#ifdef _BUILD_DIGITALIN_
/* Functions implementation */
void DigitalIn_Init(void)
{
    uint8_T tmpKeySignal;
    uint32_T flgQSPresence;

#ifdef IDN_FBK_REAR_POS
    DIGIO_InCfgExt(IDN_FBK_REAR_POS, EN_PULL_UP);
#endif

DIGIO_InCfgExt(ITE_Spd_FrontW, DISABLE_PULL);

#ifdef IDN_KEYSENSE
    DIGIO_InCfgExt(IDN_KEYSENSE, KEYSNS_PULL);
    DIGIO_InGet(IDN_KEYSENSE, &tmpKeySignal); 
    // KeySignal1 = !tmpKeySignal;
    KeySignal1 = tmpKeySignal;
    KeySig4Coil = tmpKeySignal;
#endif

#ifdef IDN_CRASHSW  
    DIGIO_InCfgExt(IDN_CRASHSW, DISABLE_PULL);
    DIGIO_InGet(IDN_CRASHSW, &CrashSignal);
#endif
    
#ifdef IDN_TRESTLE  
    DIGIO_InCfgExt(IDN_TRESTLE, DISABLE_PULL);
    DIGIO_InGet(IDN_TRESTLE, &TrestleSignal);
#endif
#ifdef IDN_BRAKE
    DIGIO_InCfgExt(IDN_BRAKE, DISABLE_PULL);
    DIGIO_InGet(IDN_BRAKE, &BrakeSignal);
#endif
    
#ifdef IDN_BUTTONMAP
    DIGIO_InCfgExt(IDN_BUTTONMAP, DISABLE_PULL);
    DIGIO_InGet(IDN_BUTTONMAP, &oldButtonMapSignal);
#endif

    flgQSPresence = (uint32_T)EEFlgsID7;
    flgQSPresence = (flgQSPresence & FLG_ID_7_GET_QS_PRES);
    if (flgQSPresence == 1)
    {
        /* QS Presence */
        FlgEnQs = 1;
    }
    else
    {
        /* QS No Presence */
        FlgEnQs = 0;
    }   
    AnalogQS_Init();
}


void DigitalIn_T5ms (void)
{
    uint16_T acqFdbk;
    uint8_T stDiag;
    uint8_T ptfaultres, diag_state, ptnotval;
    uint8_T tmpSignal;
    int16_T err = NO_ERROR;
    uint16_T tmpDeltaHigh;
    uint16_T tmpDeltaLow;
    uint32_T flgQSPresence;
    static uint8_T cntValidQSPresence = 0;
    static uint8_T cntValidNoQSPresence = 0;
  
#ifdef IDN_GSHIFTUPSW
    DIGIO_InGet(IDN_GSHIFTUPSW, &GearUpSignal); 
#else
 #ifdef IA_SHIFT_UP
    err = ADC_GetSampleRes(IA_SHIFT_UP, &VGearShift, 10);
    if (EECANNode4En != 0)
    {
        /* QS Presence */
        FlgEnQs = 1;
        AnalogQS_T5ms();
    }
    else
    {
        if (err == NO_ERROR)
        {
            flgQSPresence = (uint32_T)EEFlgsID7;
            flgQSPresence = (flgQSPresence & FLG_ID_7_GET_QS_PRES);
            if (flgQSPresence == 0)
            {
                if (VGearShift > THRQSPRESENCE)
                {
                    if (cntValidNoQSPresence >= 100)
                    {
                        /* QS No Presence */
                        flgQSPresence = (uint32_T)EEFlgsID7;
                        flgQSPresence = (flgQSPresence | FLG_ID_7_DIS_QS_PRES);
                        EEFlgsID7 = flgQSPresence;
                        FlgEnQs = 0;
                    }
                    else
                    {
                        /* Wait validation */
                        cntValidNoQSPresence++;
                    }
                    cntValidQSPresence = 0;
                }
                else
                {
                    if (cntValidQSPresence >= 100)
                    {
                        /* QS Presence */
                        flgQSPresence = (uint32_T)EEFlgsID7;
     
                        flgQSPresence = (flgQSPresence | FLG_ID_7_SET_QS_PRES);
                        EEFlgsID7 = flgQSPresence;
                        FlgEnQs = 1;
                    }
                    else
                    {
                        /* Wait validation */
                        cntValidQSPresence++;
                    }
                    cntValidNoQSPresence = 0;
                }
            }
            else if (flgQSPresence == 1)
            {
                /* QS Presence */
                FlgEnQs = 1;
                SETBIT(VehOptConfig, 2);
            }
            else
            {
                /* QS Disable */
                FlgEnQs = 0;
            }
            AnalogQS_T5ms();
        }
        else
        {
            /* Non fare niente. */
        }
    }
 #endif
#endif

#ifdef IDN_GSHIFTDOWNSW   
    DIGIO_InGet(IDN_GSHIFTDOWNSW, &GearDownSignal); 
#else

#ifdef IA_BRAKE_LAMP
    err = AnalogIn_DigIn(IA_BRAKE_LAMP, ANDIGLOTH, ANDIGHITH, ANDIAGLOW_BRAKELAMP, \
                         ANDIAGHIGH_BRAKELAMP, ADC_TRIGGERED, &tmpSignal, &ptfaultres, &ptnotval, &acqFdbk);
    if (err == NO_ERROR)
    {
        VLB3Raw = acqFdbk;
    }
    else
    {
        /* Non fare niente. */
    }
 #endif

#endif
}

void DigitalIn_T10ms(void)
{  
    uint8_T ptfaultres, diag_state, ptnotval;
    uint8_T tmpSignal;
    uint8_T tmpKeySignal;
    uint32_T timer;
    uint16_T acqFdbk;
    uint16_T tmpRoll;
    static uint8_T tmpTrestleSignal = 0u;
    static uint8_T firstTimeOff = 1u;
    static uint8_T stKeySignal = 1u;
    int16_T   err = NO_ERROR;

#ifdef IDN_KEYSENSE
    DIGIO_InGet(IDN_KEYSENSE, &KeySignal1);

#ifdef USE_KEYSIGNAL2
    tmpKeySignal = KeySignal2;
#else
    tmpKeySignal = KeySignal1;
#endif

    tmpKeySignal = (tmpKeySignal != 0);
    DigDebounce(&KeySignal, &oldKeySignal, tmpKeySignal, &cntKeySignal, CNTKEYDEBOUNCE);
    DigDebounceTwoWay(&KeyEnDiagSignal, &oldKeyEnDiagSignal, tmpKeySignal, &cntKeyEnDiagSignal, 1, CNTDEBOUNCE);

    if ((KeySignal == 1) && (stKeySignal == 0))
    {
        ActivateTask(TaskKeyOnID); /* NOTA RetroCompatibilit�: Esegue solo da KeyOff confermato a KeyOn. */
        stKeySignal = 1;
    }
    else
    {
        if ((KeySignal == 0) && (stKeySignal == 1))
        {
            ActivateTask(TaskKeyOffID);
            stKeySignal = 0;
        }
        else
        {
            /* Non fare niente. */
        }
    }
#endif

#ifdef IDN_BUTTONMAP
    DIGIO_InGet(IDN_BUTTONMAP, &tmpSignal);
    tmpSignal = (tmpSignal == 0);
    DigDebounce(&OilPresSwitch, &oldOilPresSignal, tmpSignal, &cntOilPresSignal, CNTDEBOUNCE);
    if (EndStartFlg != 0)
    {
        if (cntOPAbsTdc > CNTOPABSTDC)
        {
            if (OilPresSwitch != 0)
            {
                if (cntOPOff <= TIMDEBOPOFF)
                {
                    cntOPOff++;
                }
                else
                {
                    OilPresSignal = 1;
                }
            }
            else
            {
                OilPresSignal = 0;
                cntOPOff = 0;
            }
        }
        else
        {
            OilPresSignal = 0;
            cntOPAbsTdc = CntAbsTdc;
        }    
    }
    else
    {
        cntOPOff = 0;
        OilPresSignal = 0;
        cntOPAbsTdc = 0;
    }
#endif

#ifdef ITE_Spd_FrontW   // niso canceled this condition: if (USEA1RSIGNAL != 0)
    DIGIO_InGet(ITE_Spd_FrontW, &tmpSignal);
    tmpSignal = (tmpSignal == 0);
    DigDebounce(&RiderPresence, &oldRiderPresence, tmpSignal, &cntRiderPresence, CNTDEBOUNCE);
#endif

#ifdef IA_START_SW   //niso added. //niso canceled this condition: if (USEA2RSIGNAL == 1)
   
    err = AnalogIn_DigIn(IA_START_SW, ANDIGLOTH, ANDIGHITH, ANDIAGLOW_STARTSW, \
                    ANDIAGHIGH_STARTSW, ADC_TRIGGERED, &tmpSignal, &ptfaultres, &ptnotval, &acqFdbk);
    if (err == NO_ERROR)
    {
        if (ptnotval == 1)
        {
            /* Non fare niente. */
        }
        else
        {
            tmpSignal = (tmpSignal == 0);
            DigDebounce(&ParkBrakeSignal, &oldParkBrakeSignal, tmpSignal, &cntParkBrakeSignal, CNTDEBOUNCE);   
        }
    }
    else
    {
        /* Non fare niente. */
    }

 
#endif


#ifdef IDN_CRASHSW  
    DIGIO_InGet(IDN_CRASHSW, &CrashSignal);
#else
 #ifdef IA_TIP_OVER
    if ((FlgYawRec == 1) && (COBStartActive == 0))
    {
        TimRollEngOff = 0;
        CrashSignal = 0;
        DiagMgm_SetDiagState(DIAG_TIP_OVER, SIGNAL_NOT_VALID, &diag_state);
    }
    else
    {
        tmpRoll = abs(RollCAN);
        if (tmpRoll > ANGROLLENGOFF)
        {
            if (TimRollEngOff >= TIMROLLENGOFF)
            {
                CrashSignal = 1;
            }
            else
            {
                TimRollEngOff++;
            }
        }
        else
        {
            TimRollEngOff = 0;
            CrashSignal = 0;
        }
        DiagMgm_SetDiagState(DIAG_TIP_OVER, NO_PT_FAULT, &diag_state);
    }

 #endif
#endif

#ifdef IDN_TRESTLE  
    DIGIO_InGet(IDN_TRESTLE, &TrestleSignal);
#else
 #ifdef IA_SIDE_STAND
    err = AnalogIn_DigIn(IA_SIDE_STAND, ANDIGLOTH, ANDIGHITH, ANDIAGLOW_SIDESTAND, \
                         ANDIAGHIGH_SIDESTAND, ADC_TRIGGERED, &tmpSignal, &ptfaultres, &ptnotval, &acqFdbk) ;
    if (err == NO_ERROR)
    {
        if (ptnotval == 1)
        {
            /* Non fare niente. */
        }
        else
        {
            tmpSignal = (tmpSignal == 0);
            DigDebounce(&tmpTrestleSignal, &oldTrestleSignal, tmpSignal, &cntTrestleSignal, CNTDEBOUNCE);
            if (KeySignal != 0)
            {
                TrestleSignal = tmpTrestleSignal;
            }
            else 
            { 
                /* SKIP */
            }
        }
        // Diagnosi non supportata in questa versione HW, bisogna montare 2 resistenze.
        /* DiagMgm_SetDiagState(DIAG_TRESTLE_SIGNAL, ptfaultres, &diag_state); */
    }
    else
    {
        /* Non fare niente. */
    }
 #endif
#endif
// Niso mv8
#ifdef ODE_HORN
  // HornSignalCAN CAN mesajindan geliyor (CANMGM_MV_950.c'den)
  DigDebounce(&HornSignalDebounced,      // �ikis degeri
    &HornSignalOld,            // �nceki deger
    HornSignalCAN,             // Yeni giris degeri
    &HornSignalCounter,        // Saya�
    CNTDEBOUNCE);              // Debounce s�resi (30 �rnek)

HornSignal = (HornSignalDebounced != 0);
//HornSignal = HornSignalCAN;
#endif

//Niso implementation
#ifdef IDN_BRAKE
DIGIO_InGet(IDN_BRAKE, &BrakeSignal);
#else

#ifdef IA_BRAKE_SWITCH
err = AnalogIn_DigIn(IA_BRAKE_SWITCH, ANDIGLOTHLC4, ANDIGHITHLC4, ANDIAGLOW_BRAKESW, \
                     ANDIAGHIGH_BRAKESW, ADC_TRIGGERED, &tmpSignal, &ptfaultres, &ptnotval, &acqFdbk);
if (err == NO_ERROR)
{
    VLC4Raw = acqFdbk;
    if (ptnotval == 1)
    {
        /* Non fare niente. */
    }
    else
    {
        tmpSignal = (tmpSignal == 1);
        DigDebounce(&BrakeSignal, &oldBrakeSignal, tmpSignal, &cntBrakeSignal, CNTDEBOUNCE);

        
    }
}
else
{
    /* Non fare niente. */
}
#endif
#endif

}

void DigitalIn_CoilDiag (void)
{
    DIGIO_InGet(IDN_KEYSENSE, &KeySig4Coil);
}
#endif // _BUILD_DIGITALIN_

