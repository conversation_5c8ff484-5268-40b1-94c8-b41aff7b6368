/*****************************************************************************************************************/
/* $HeadURL:: https://************/svn/Rep_Bo/EM/NISO/MV8/trunk/tree/APPLICATION/ENGFLAG/engflag.c           $   */
/* $ Description:                                                                                                */
/* $Revision:: 16719  $                                                                                          */
/* $Date:: 2025-06-24 11:07:28 +0300 (<PERSON><PERSON>, 24 Jun 2025)   $                                                      */
/* $Author:: yeldano                 $                                                                           */
/*****************************************************************************************************************/

#include "engflag.h"
#include "mathlib.h"
#include "digitalin.h"
#include "canmgm.h"
#include "syncmgm.h"
#include "thrposmgm.h"
#include "trq_driver.h"
#include "temp_mgm.h"
#include "PTrain_Diag.h"
#include "Vspeed_mgm.h"
#include "diagmgm_out.h"
#include "misfobd2_out.h"
#include "lambda_mgm.h"
#include "lamheater_mgm.h"
#include "gearposclu_mgm.h"
#include "Exhval_mgm.h"
#include "gear_mgm.h"

#define TIM_ENRIDINGSELECT 4

#ifdef  MATLAB_MEX_FILE
#define _BUILD_ENGFLAG_
#define USE_TORQUE_MGM
#endif

extern uint8_T  StEngineStart;
extern uint8_T  SetTracCtrl;
extern uint16_T VehSpeed;
extern uint8_T  StDiag[DIAG_NUMBER];

//TUNING PARAMETERS
extern uint8_T  FODRVC;
extern int8_T   FOENDSTARTFLG;
extern uint8_T  ENECONTNKEMPTY;
extern uint16_T THRFRONTVEHSPEEDECONMODE;
extern uint16_T THRVSPEEDECONMODEON;
extern uint16_T THRVSPEEDECONMODEOFF;
extern uint8_T  THRGEARECONMODE;
extern uint8_T VSPDRIDINGCUSTMIN;
extern uint8_T ENRIDINGMODE[];
extern uint8_T TIMCHRIDINGMODE;
extern uint8_T TIMCHRIDINGMODEFST;
extern uint16_T TIMACKRIDINGMODE;
extern uint16_T RPMENGSTART;
extern uint16_T POSGASOUTIDLE;
extern uint16_T POSGASINIDLE;
extern int16_T  DELTTWATERWUC;
extern int16_T  TWATERWUC;
extern uint8_T  VTACCSENS[];
extern uint8_T  VTMAXTRQ[];
extern uint8_T  VTENGRESP[];
extern uint8_T  VTRPMLIM[];

//OUTPUTS
uint8_T     RidingMode;
uint8_T     EndStartFlg;
uint8_T     FlgEconMode;
uint32_T    EndStartTime;
uint32_T    EngFlagTimer;
uint8_T     RidingModeTOut;
uint8_T     AccSens;
uint8_T     MaxTrq;
uint8_T     EngBrake;
uint8_T     EngResp;
uint8_T     RpmLim;
uint8_T     DrivingCycle;
uint8_T     WarmUpCycle;
uint8_T     CntDrivingCycle;
uint8_T     CntWarmUpCycle;
uint8_T     FlgUDSPwrLSlow;

static uint8_T timEnRidingSelect;
static uint8_T timChangeRidingMode;
static uint16_T timAckRidingMode;
static uint8_T trigRidingModeTOut;
static uint8_T nextRM;
const uint8_T RidingTable[RM_SIZE] = {RM_RAIN, RM_URBAN, RM_SPORT, RM_RACE, RM_CUSTOM};

#ifdef _BUILD_ENGFLAG_

//Private Functions
void EngFlag_EndStartFlg(void);
void EngFlag_DrivingCycleFlg(void);
void EngFlag_WarmUpCycleFlg(void);


void EngFlag_Init(void)
{
    EndStartFlg = 0;
    DrivingCycle = DRVC_OFF;
    WarmUpCycle = WUC_NOT_ENDED;
    CntDrivingCycle = 0;
    CntWarmUpCycle = 0;
  
    RidingModeTOut = 0;
    timEnRidingSelect = 0;
    timChangeRidingMode = 0;
    timAckRidingMode = 0;
    trigRidingModeTOut = 0;
    RidingMode = RidingModeCAN;
    nextRM = RidingMode;

    EngBrake = SetEngBrake;
    if ((RidingMode != RM_CUSTOM) && (RidingMode < (RM_SIZE-1)))
    {
        AccSens = VTACCSENS[RidingMode];
        MaxTrq = VTMAXTRQ[RidingMode];
        EngResp = VTENGRESP[RidingMode];
        RpmLim = VTRPMLIM[RidingMode];
    }
    else
    {
        AccSens = SetAccSens;
        MaxTrq = SetMaxTrq;
        EngResp = SetEngResp;
        RpmLim = SetRpmLim;
    }
}

uint8_T GetNextRidingMode (void)
{
    uint8_T i;
    uint8_T ret;
    i = 0;
    ret = RidingTable[0]; /* Carica il valore di default. */
    /* Cicla fino alla prima mappa disponibile o fino alla fine delle mappe. */
    while ((i < RM_SIZE) && (nextRM < RM_SIZE))
    {
        /* Se la mappa i-esima � settata selazionala. */
        if (ENRIDINGMODE[nextRM] != 0)
        {
            ret = RidingTable[nextRM];
            i = RM_SIZE;
        }
        /* Prossima ricerca mappa. */
        else
        {
            i++;
        }
        /* Avanza alla prossima mappa. */
        if (nextRM < (RM_SIZE - 1))
        {
            nextRM++;
        }
        else
        {
            nextRM = 0;
        }
    } 
    return ret;
}

void EngFlag_SetRidingMode (void)
{
    uint8_T tmpRMSignal;
    uint8_T timThr;

    tmpRMSignal = StartSignal;
    /* Se il motore � avviato abilita la strategia RidingMode. */
    if ((StEngineStart == ENG_STARTED) && (KeySignal != 0))
    {
        /* Se il tasto � rilasciato per TIM_ENRIDINGSELECT comincia la strategia. */
        if (timEnRidingSelect >= TIM_ENRIDINGSELECT)
        {
            /* Se il tasto � premuto avanza nel cambio mappa. */
            if (tmpRMSignal == TRUE)
            {
                /* Se il tasto � premuto per TIMCHRIDINGMODE seleziona la possibile nuova mappa. */
                if (RidingModeTOut != 0)
                {
                    timThr = TIMCHRIDINGMODEFST;
                }
                else
                {
                    timThr = TIMCHRIDINGMODE;
                }
                if (timChangeRidingMode >= timThr)
                {
                    timEnRidingSelect = 0;
                    timChangeRidingMode = 0;
                    RidingModeTOut = 1;
                    RidingModeCAN = GetNextRidingMode();
                }
                /* Filtra il pulsante premuto. */
                else
                {
                    timChangeRidingMode++;
                }
                timAckRidingMode = 0; /* Reset il tempo di conferma della mappa. */
            }
            /* Tasto non premuto. */
            else
            {
                /* Se � scaduto il timeout � c'� una nuova mappa confermala. */
                if ((timAckRidingMode >= TIMACKRIDINGMODE) && (RidingModeTOut == 1))
                {
                    RidingModeTOut = 0; /* Nuova mappa attiva. */
                    /* Ripristina l'indice precedente, alla prossima richiesta di strategia riparte dalla mappa selezionata. */
                    if (nextRM > 0)
                    {
                        nextRM--;
                    }
                    else
                    {
                        nextRM = (RM_SIZE - 1);
                    }
                }
                /* Filtra il timeout. */
                else
                {
                    timAckRidingMode++;
                }
                timChangeRidingMode = 0; /* Reset il tempo di pressione bottone. */
            }
        }
        /* Tempo di abilitazione RidingMode non scaduto. */
        else
        {
            /* Se il tasto � rilasciato incrementa il timer di abilitazione strategia. */
            if (tmpRMSignal == FALSE)
            {
                timEnRidingSelect++;
            }
            /* Tasto premuto, non abilitare la strategia. */
            else
            {
                timEnRidingSelect = 0;
            }
        }
    }
    /* Motore spento, reset variabili e ripristino contesto. */
    else
    {
        RidingMode = RidingModeCAN;
        RidingModeTOut = 0;
        timAckRidingMode = 0;
        timEnRidingSelect = 0;
        timChangeRidingMode = 0;
    }
}

void GetRidingModeMap (void)
{
    /* Setting of Engine brake */
    EngBrake = SetEngBrake;

    /* Cambio valori mappa solo sul trigger della fine di nuova richiesta, per non risovrascriverli sempre. */
    if ((RidingModeTOut == 1) && (trigRidingModeTOut == 0))
    {
        trigRidingModeTOut = 1;
    }
    else { /* MISRA */ }
    /* Cambia i valori della Mappa. */
    if (RidingModeTOut == 0)
    {
        /* Aggiorna alla nuova selezione mappa. */
        if (trigRidingModeTOut == 1)
        {
            switch (RidingModeCAN)
            {
                case RM_RAIN:
                {
                    RidingMode = RidingModeCAN;
                    AccSens = VTACCSENS[RM_RAIN];
                    MaxTrq = VTMAXTRQ[RM_RAIN];
                    EngResp = VTENGRESP[RM_RAIN];
                    RpmLim = VTRPMLIM[RM_RAIN];
                }
                break;

                case RM_URBAN:
                {
                    RidingMode = RidingModeCAN;
                    AccSens = VTACCSENS[RM_URBAN];
                    MaxTrq = VTMAXTRQ[RM_URBAN];
                    EngResp = VTENGRESP[RM_URBAN];
                    RpmLim = VTRPMLIM[RM_URBAN];
                }
                break;

                case RM_SPORT:
                {
                    RidingMode = RidingModeCAN;
                    AccSens = VTACCSENS[RM_SPORT];
                    MaxTrq = VTMAXTRQ[RM_SPORT];
                    EngResp = VTENGRESP[RM_SPORT];
                    RpmLim = VTRPMLIM[RM_SPORT];
                }
                break;

                case RM_RACE:
                {
                    RidingMode = RidingModeCAN;
                    AccSens = VTACCSENS[RM_RACE];
                    MaxTrq = VTMAXTRQ[RM_RACE];
                    EngResp = VTENGRESP[RM_RACE];
                    RpmLim = VTRPMLIM[RM_RACE];
                }
                break;

                case RM_CUSTOM:
                {
                    RidingMode = RidingModeCAN;
                    AccSens = SetAccSens;
                    MaxTrq = SetMaxTrq;
                    EngResp = SetEngResp;
                    RpmLim = SetRpmLim;
                }
                break;
                
                default:
                {
                    /* Non fare niente. */
                }
                break;
            }
            trigRidingModeTOut = 0;
        }
        else { /* MISRA */ }
        /* Mappa custom select in Run. */
        if (RidingModeCAN == RM_CUSTOM)
        {
            if (VehSpeed < VSPDRIDINGCUSTMIN)
            {
                RidingMode = RidingModeCAN;
                AccSens = SetAccSens;
                MaxTrq = SetMaxTrq;
                EngBrake = SetEngBrake;
                EngResp = SetEngResp;
                RpmLim = SetRpmLim;
            }
            else
            {
                /* Non aggiornare in caso di velocit� maggiore di 5 Km/h. */
            }
        }
    }
    else { /* MISRA */ }
}

void EngFlag_T10ms(void)
{
    uint64_T ticksTimer;
    uint64_T msTimer;
    uint8_T tmpFlgEconMode1;
    static uint8_T tmpFlgEconMode2 = 0;
  
    EngFlag_EndStartFlg();
    EngFlag_DrivingCycleFlg();
    EngFlag_WarmUpCycleFlg();

    TIMING_GetAbsTimer(&(ticksTimer));
    TIMING_TicksToMilliSeconds(ticksTimer, &msTimer);
    EngFlagTimer = (uint32_T)(msTimer & 0x00000000FFFFFFFF);

    if (EndStartFlg == 0) /* starting phase not yet finished */
    {
        EndStartTime = EngFlagTimer;
    }
    else { /* MISRA */ }

    /* Calcolo FlgEconMode */
    if ((ENECONTNKEMPTY != 0) && (FlgLowFuel != 0))
    {
        FlgEconMode = 1;
    }
    else
    {
        if ((FlgVehStop == 0) && ((VehSpeedFront < THRFRONTVEHSPEEDECONMODE) || (StDiag[DIAG_VEHSPEED_FRONT] == FAULT)))
        {
            tmpFlgEconMode1 = 1;
        }
        else
        {
            tmpFlgEconMode1 = 0;
        }
        if (GearPos >= THRGEARECONMODE)
        {
            if (VehSpeed > THRVSPEEDECONMODEON)
            {
                tmpFlgEconMode2 = 1;
            }
            else
            {
                if (VehSpeed <= THRVSPEEDECONMODEOFF)
                {
                    tmpFlgEconMode2 = 0;
                }
                else
                {
                    /* Freeze */
                }
            }
        }
        else
        {
            tmpFlgEconMode2 = 0;
        }
        FlgEconMode = ((tmpFlgEconMode1 != 0) || (tmpFlgEconMode2 != 0));
    }
}

void EngFlag_EndStartFlg(void)
{
    //EndStartFlg Calculation
    if (FOENDSTARTFLG >= 0)
    {
        EndStartFlg = FOENDSTARTFLG;
    }
    else if (EndStartFlg != 0)
    {
        EndStartFlg = (Rpm != 0);
    }
    else
    {
        /* Aggiunta condizione su sistema fasato */
        EndStartFlg = ((Rpm > RPMENGSTART) && (FlgSyncPhased != 0));
    }
}

void EngFlag_DrivingCycleFlg(void)
{
    switch (DrivingCycle)
    {
        case DRVC_OFF:
        {
            if ((EndStartFlg != 0) && (KeySignal != 0))
            {
                DrivingCycle = DRVC_START;
            }
            else { /* MISRA */ }
        }
        break;

        case DRVC_START:
        {
            if ((EndStartFlg == 0) && (KeySignal == 0))
            {
                DrivingCycle = DRVC_OFF;
            }
            else if (FlgMasterDiagOn != 0)
            {
                DrivingCycle = DRVC_CONF;
            }
            else if (FODRVC != 0)
            {
                DrivingCycle = DRVC_CONF;
            }
            else { /* MISRA */ }
        }
        break;

        case DRVC_CONF:
        {
            if ((KeySignal == 0) && (Rpm == 0))
            {
                DrivingCycle = DRVC_ENDED;
                CntDrivingCycle++;
            }
            else { /* MISRA */ }
        }
        break;

        case DRVC_ENDED:
        {
            if (KeySignal != 0)
            {
                DrivingCycle = DRVC_OFF;
            }
            else { /* MISRA */ }
        }
        break;

        default:
        {
            DrivingCycle = DRVC_OFF;
        }
        break;
    }
}

void EngFlag_WarmUpCycleFlg(void)
{
    static uint8_T warmupCycleTrig = 0;
    static uint8_T warmupCycleTempOn = 0;
    
    if ((EndStartFlg != 0) && (KeySignal != 0) && (warmupCycleTrig == 0))
    {
        warmupCycleTrig = 1;
    }
    else { /* MISRA */ }
    if (warmupCycleTrig == 1)
    {
        if (((TWater - TWaterCrk) >= DELTTWATERWUC) && (TWaterCrk < TWATERWUC) && (TWater >= TWATERWUC))
        {
            WarmUpCycle = WUC_ENDED;
            CntWarmUpCycle++;
            warmupCycleTrig = 2;
        }
        else if (KeySignal == 0)
        {
            WarmUpCycle = WUC_NOT_ENDED;
            warmupCycleTrig = 0;
        }
        else { /* MISRA */ }
    }
    else if (warmupCycleTrig == 2)
    {
        if (KeySignal == 0)
        {
            WarmUpCycle = WUC_NOT_ENDED;
            warmupCycleTrig = 0;
        }
        else { /* MISRA */ }
    }
    else { /* MISRA */ }
}

#ifndef USE_TORQUE_MGM /* Gestione in assenza del controllo di coppia - sistemi NON Drive By Wire */

void EngFlag_IdleFlg(void);
uint8_t  IdleFlg;
uint16_t RpmIdleObj0;
extern uint16_t RPMIDLETHR;
void EngFlag_IdleFlg(void)
{       
    //IdleFlg Calculation
    if (EndStartFlg)
    {
        if (IdleFlg)
            IdleFlg = (GasPosCC < POSGASOUTIDLE);
        else
            IdleFlg = (GasPosCC < POSGASINIDLE) && (Rpm < (RpmIdleObj0 + RPMIDLETHR));
    }
    else
    {                   
        IdleFlg = 0;
    }
}
#endif

#endif