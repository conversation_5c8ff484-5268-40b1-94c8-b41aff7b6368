/*****************************************************************************************************************/
/* $HeadURL:: http://***********:8080/svn/Rep_Bo/EM/appl_calib/branches/LL_MV6_16_CTF/tree/DD/COMMON#$   */
/* $ Description:                                                                                                */
/* $Revision:: 10994  $                                                                                          */
/* $Date:: 2019-10-29 09:36:17 +0100 (mar, 29 ott 2019)   $                                                      */
/* $Author:: LanaL                   $                                                                       */
/*****************************************************************************************************************/

#ifndef _DIAGMGM_DEF_H_
#define _DIAGMGM_DEF_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "typedefs.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
/* Valori di StDiag */
#define  NO_FAULT             0
#define  FAULT_FILTERING      1
#define  FAULT                2

/* Identificativi diagnosi*/
#define NULL_DTC    0xFFFFu

#define DIAG_T_AIR           0  /* Diagnosi TAir                                */
#define DIAG_T_WATER         1  /* Diagnosi TWater                              */
#define DIAG_T_WATER_2       2  /* Diagnosi TWater 2                            */
#define DIAG_EXHVALVEFDBK    3  /* Diagnosi Elettrica ExhValve                  */
#define DIAG_CAMLEVEL        4  /* Diagnosi CamLevel                            */
#define DIAG_MCU_HORN_CTRL   5  /* Horn Diagnostic                              */
#define DIAG_PRESATM         6  /* Diagnosi PresAtm                             */
#define DIAG_SENS_VBAT       7  /* Diagnosi VBattery                            */
#define DIAG_VEHICLE_CAN     8  /* Diagnosi CAN veicolo                         */
#define DIAG_SAFETY_3        9  /* Diagnosi Safety 3 per memorizzare StopCause  */
#define DIAG_ION_CH_A       10  /* Diagnosi IonBufferV CH A                     */
#define DIAG_GEAR_SENSOR    11  /* Diagnosis Gear sensor                        */
#define DIAG_VIN_CODE       12  /* Diagnosis VIN Code AT procedure              */
#define DIAG_FREE_13        13  /* FREE                                         */
#define DIAG_REAR_POS       14  /* Diagnosis rear position light                */
#define DIAG_HEATEDGRIP     15  /* Diagnosis heated grip                        */ //niso added.
#define DIAG_KNOCK_COH_0    16  /* Diagnosis knocking coherence cyl 0           */
#define DIAG_KNOCK_COH_1    17  /* Diagnosis knocking coherence cyl 1           */
#define DIAG_KNOCK_COH_2    18  /* Diagnosis knocking coherence cyl 2           */
#define DIAG_KNOCK_COH_3    19  /* Diagnosis knocking coherence cyl 3           */
#define DIAG_QSHIFT_ANALOG  20  /* Diagnosi Sensore analogico QS                */
#define DIAG_VSPARKPEAK_0   21  /* Diagnosis VSparkPeak cyl 0                   */
#define DIAG_VSPARKPEAK_1   22  /* Diagnosis VSparkPeak cyl 1                   */
#define DIAG_VSPARKPEAK_2   23  /* Diagnosis VSparkPeak cyl 2                   */
#define DIAG_VSPARKPEAK_3   24  /* Diagnosis VSparkPeak cyl 3                   */
#define DIAG_INJECTOR_4     25  /* Diagnosi Iniettore 4                         */
#define DIAG_INJECTOR_5     26  /* Diagnosi Iniettore 5                         */
#define DIAG_INJECTOR_6     27  /* Diagnosi Iniettore 6                         */
#define DIAG_INJECTOR_7     28  /* Diagnosi Iniettore 7                         */
#define DIAG_HBRIDGE_A      29  /* Diagnosis HBRIDGE_A                          */
#define DIAG_HBRIDGE_B      30  /* Diagnosis HBRIDGE_B                          */
#define DIAG_LSD_FANCOIL    31  /* Diagnosi Rel? Elettroventola   Niso!!!      */
#define DIAG_LSD_MAIN_REL   32  /* FREE                                         */
#define DIAG_LAM2_EL        33  /* Diagnosi elettrica sensore lambda 2          */
#define DIAG_O2H_FUNC_2     34  /* Diag lambda2 heater functional               */
#define DIAG_INJECTOR_0     35  /* Diagnosi Iniettore 0                         */
#define DIAG_INJECTOR_1     36  /* Diagnosi Iniettore 1                         */
#define DIAG_INJECTOR_2     37  /* Diagnosi Iniettore 2                         */
#define DIAG_INJECTOR_3     38  /* Diagnosi Iniettore 3                         */
#define DIAG_VSENS1         39  /* Diagnosi Vsens1                              */
#define DIAG_VSENS2         40  /* Diagnosi Vsens1                              */
#define DIAG_VANGTHR_1      41  /* Diagnosis VAngThrottle1                      */
#define DIAG_VANGTHR_2      42  /* Diagnosis VAngThrottle2                      */
#define DIAG_COH_VANGTHR    43  /* Diagnosi Coerenza VAngThrottle               */
#define DIAG_VGASPOS_1      44  /* Diagnosis VGasPos1                           */
#define DIAG_VGASPOS_2      45  /* Diagnosis VGasPos2                           */
#define DIAG_VGASSWITCH     46  /* Diagnosis Ilde Switch                        */
#define DIAG_COH_VGASPOS    47  /* Diagnosi Coerenza VGasPos                    */
#define DIAG_DBW_CONTROL    48  /* Diagnosi controllo DBW                       */
#define DIAG_VEHSPEED_FRONT 49  /* Diagnosi Velocit� ruota anteriore            */
#define DIAG_COIL_0         50  /* Diagnosi Bobina 0                            */
#define DIAG_COIL_1         51  /* Diagnosi Bobina 1                            */
#define DIAG_COIL_2         52  /* Diagnosi Bobina 2                            */
#define DIAG_COIL_3         53  /* Diagnosi Bobina 3                            */
#define DIAG_V_MAPSIGNAL    54  /* Diagnosi VMapSignal                          */
#define DIAG_ADC            55  /* Diagnosi ADC                                 */
#define DIAG_DIS_LOADS      56  /* Diagnosi Dis Carichi                         */
#define DIAG_SAFETY_2       57  /* Diagnosi SAFETY lvl 2                        */
#define DIAG_TSD_TLE6244    58  /* Diagnosi overtemperature driver LSD TLE6244  */
#define DIAG_CAT            59  /* Diagnosi catalizzatore                       */
#define DIAG_EXHVALVPOS     60  /* Diagnosi posizione ExhValve                  */
#define DIAG_PRES_NOT_COH   61  /* Diagnosi funzionale pressione                */
#define DIAG_HOLE_MANIFOLD  62  /* Diagnosi buco collettore                     */
#define DIAG_RPM_SENS       63  /* Diagnosi sensore giri                        */
#define DIAG_LSD_HLAMBDA    64  /* Diagnosi attuatore riscaldatore sonda lambda */
#define DIAG_LSD_HLAMBDA_2  65  /* Diagnosi attuatore riscaldatore sonda lam 2  */
#define DIAG_VEH_CAN_NODE_1 66  /* Diagnosi CAN veicolo nodo 1 assente          */
#define DIAG_VEH_CAN_NODE_2 67  /* Diagnosi CAN veicolo nodo 2 assente          */
#define DIAG_VEH_CAN_NODE_3 68  /* Diagnosi CAN veicolo nodo 3 assente          */
#define DIAG_FLASH          69  /* Diagnosi memoria flash                       */
#define DIAG_EEPROM         70  /* Diagnosi memoria eeprom                      */
#define DIAG_CPU            71  /* Diagnosi CPU                                 */
#define DIAG_RAM            72  /* Diagnosi RAM                                 */
#define DIAG_TIP_OVER       73  /* Diagnosi Tip Over sensor                     */
#define DIAG_LAM_EL         74  /* Diagnosi elettrica sensore lambda            */
#define DIAG_LAM_FUNC       75  /* Diagnosi funzionale sensore lambda           */
#define DIAG_V_REG          76  /* Diagnosi regolatore di tensione              */
#define DIAG_VGASSWCOH      77  /* Diagnosis Coerenza Idle Switch               */
#define DIAG_CLUTCH         78  /* Diagnosi funzionale Frizione                 */
#define DIAG_VEHSPEED       79  /* Diagnosi funzionale Velocit� veicolo         */
#define DIAG_MISF_RND       80  /* Random/Multiple Cylinder Misfire Detected    */
#define DIAG_O2H_FUNC       81  /* Diagnosi lambda functional                   */
#define DIAG_VEH_CAN_NODE_4 82  /* Diagnosi E-Gear                              */
#define DIAG_VEH_CAN_NODE_5 83  /* Diagnosi TPMS                                */
#define DIAG_LAM_FUNC_2     84  /* Diagnosi funzionale sensore lambda 2         */
#define DIAG_O2_SLOW_R2L    85  /* O2 Sensor Slow Response - Rich to Lean       */
#define DIAG_O2_SLOW_L2R    86  /* O2 Sensor Slow Response - Lean to Rich       */
#define DIAG_VEH_CAN_NODE_6 87  /* Diagnosi CAN LHB                             */
#define DIAG_VEH_CAN_NODE_7 88  /* Diagnosi CAN RHB                             */
#define DIAG_VEH_CAN_NODE_8 89  /* Diagnosi CAN MCU                             */
#define DIAG_PRIVATE_CAN    90  /* Diagnosi CAN privato                         */
#define DIAG_VEH_CAN_NODE_9 91  /* Diagnosi CAN GPS                             */
#define DIAG_NUMBER         92  /*                                              */

/* Alias */
#define DIAG_DUMMY          255 /* Diag dummy - Reserved                        */
#define DIAG_CMEDRIVERCAN   DIAG_DUMMY /* Stub diagnosis, Disable */

/* Valori di PtFault*/
#define  NO_PT_FAULT                0
#define  CC_TO_GND                  1
#define  CC_TO_VCC                  2
#define  SIGNAL_NOT_VALID           3
#define  FAULT_CAN_BUSOFF           4
#define  FAULT_CAN_BUFFER_EMPTY     5
#define  SIG_NOT_PLAUSIBLE          6
#define  SIGNAL_NOT_PRESENT         7
#define  OPEN_CIRCUIT               8
#define  SHORT_CIRCUIT              9
#define  TWO_COHERENT_SENS          10
#define  NO_COHERENT_SENS           11
#define  CC_TO_VBAT                 12
#define  CIRCUIT_MALFUNCTION        13
#define  UNDER_VOLTAGE              14
#define  OVERTEMPERATURE            15
#define  SAFETY_2_FAULT             16
#define  ECU_BLANK                  17
#define  INVALID_KEY                18
#define  CONTINUOUS                 19
#define  SAFETY_3_FAULT             20
#define  XCVR_ERROR                 21
#define  DANGER                     22 /* Special Id for MIL Flashing */
#define  LAMBDA_TOO_POOR            23
#define  LAMBDA_TOO_RICH            24
#define  FREQLAM_TOO_LOW            25
#define  OVER_VOLTAGE               26
#define  OVER_CURRENT               27
#define  SIGNAL_TOO_SLOW            28
#define  SIGNAL_STUCK               29
#define  SIGNAL_OVERRANGE           30
#define  FAULT_CAN_BUFFER_OVERRUN   31
#define  FAULT_CAN_BUFFER_CRC       32
#define  FAULT_CAN_BUFFER_ALIVE_CNT 33

typedef struct {
   uint16_t     value;          // DTC value
   uint8_t      status;         // DTC status
   uint8_t      cntmilactive;   // 1111xx11 -> [(1) Pending Run] [(1) Pending DC Off] [(1) Pending last DC] [(1) Permanent] [(1) DC Set] [(1) Flashing ack] [(3) MIL On]
   uint16_t     rpm;
   uint16_t     mapSignal;
   int16_t      twater;
   uint16_t     angthrottle;
   uint16_t     gaspos;
   int16_t      saout;
   uint16_t     load;
   int8_t       shortttermfueltrim;
   int8_t       longtermfueltrim;
   uint16_t     vehiclespeed;
   uint8_t      stfuelsys;
   int16_t      tair;
   uint32_t     odometer;
} DiagDataFaultStruct;

typedef struct{
    uint8_t pos;
    uint16_t value;
    uint8_t priority;
} StoredDtcPosStruct;

#define DIAG_FAULT_LENGTH  12
#define INIT_DIAGDATAFAULT {NULL_DTC, 0x00u, 0x00u, 0x0000u, 0x0000u, 0x0000, 0x0000u, 0x0000u, 0x0000, 0x0000u, 0x00, 0x00, 0x0000u, 0x00u, 0x0000, 0x00000000u}

#endif  /* _DIAGMGM_DEF_H_*/

