/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_New_Reps/SVN_Shared_Rep/EM/appl_calib/branches/MV1/tree/DD/DIAG#$   */
/* $ Description:                                                                                                */
/* $Revision:: 1564   $                                                                                          */
/* $Date:: 2009-08-05 12:31:16 +0200 (mer, 05 ago 2009)   $                                                      */
/* $Author:: bancomotore             $                                                                       */
/*****************************************************************************************************************/

#ifdef _BUILD_DTC_

#ifndef _BUILD_DIAGCANMGM_
#error DTC module enabled without _BUILD_DIAGCANMGM_ macro enabled
#endif /*  _BUILD_DIAGCANMGM_ */



/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "Dtc.h"
#include "diagcanmgm.h"
#include "KwpObd_EnvVarConversion.h"


/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
 
uint8_t dtc_erased = 0;

const uint16_T DiagToDtcTabCfg[DIAG_NUMBER] =
{
    P0110,      /* DIAG_T_AIR           0    Diagnosi TAir                                 */   
    P0115,      /* DIAG_T_WATER         1    Diagnosi TWater                               */   
    P2182,      /* DIAG_T_WATER_2       2    Diagnosi TWater 2                             */   
    P0027,      /* DIAG_EXHVALVEFDBK    3    Diagnosi Elettrica ExhValve                   */ 
    P0102,      /* DIAG_CAMLEVEL        4    Diagnosi CamLevel                             */   
    NULL_DTC,   /* DIAG_BRAKE_LAMP      5    Diagnosi Brake Lamp                           */   
    NULL_DTC,   /* DIAG_PRESATM         6    Diagnosi PresAtm                              */   
    P0560,      /* DIAG_SENS_VBAT       7    Diagnosi VBattery                             */   
    U1602,      /* DIAG_VEHICLE_CAN     8    Diagnosi CAN veicolo                          */   
    P1610,      /* DIAG_SAFETY_3        9    Diagnosi Safety 3 per memorizzare StopCause   */   
    P1300,      /* DIAG_ION_CH_A       10    Diagnosi IonBufferV CH A                      */   
    P0915,      /* DIAG_GEAR_SENSOR    11    Diagnosis Gear sensor                         */   
    P0916,      /* DIAG_VIN_CODE       12    Diagnosis VIN Code AT procedure               */   
    NULL_DTC,   /* DIAG_LSD_MAIN_REL   13    Diagnosis main relay                          */   
    P1014,      /* DIAG_REAR_POS       14    Diagnosis rear position light                 */   
    P1015,      /* DIAG_HEATEDGRIP     15    Diagnosis heated grip                         */   
    P0325,      /* DIAG_KNOCK_COH_0    16    Diagnosis knocking coherence cyl 0            */   
    P0326,      /* DIAG_KNOCK_COH_1    17    Diagnosis knocking coherence cyl 1            */   
    P0327,      /* DIAG_KNOCK_COH_2    18    Diagnosis knocking coherence cyl 2            */   
    P0328,      /* DIAG_KNOCK_COH_3    19    Diagnosis knocking coherence cyl 3            */   
    P0826,      /* DIAG_QSHIFT_ANALOG  20    Diagnosi  Qshift                              */   
    P1305,      /* DIAG_VSPARKPEAK_0   21    Diagnosis VSparkPeak cyl 0                    */   
    P1306,      /* DIAG_VSPARKPEAK_1   22    Diagnosis VSparkPeak cyl 1                    */   
    P1307,      /* DIAG_VSPARKPEAK_2   23    Diagnosis VSparkPeak cyl 2                    */   
    P1308,      /* DIAG_VSPARKPEAK_3   24    Diagnosis VSparkPeak cyl 3                    */   
#ifdef N_INJECTOR_HB    
    P0205,      /* DIAG_INJECTOR_4     25    Diagnosi Iniettore 4                          */   
    P0206,      /* DIAG_INJECTOR_5     26    Diagnosi Iniettore 5                          */   
    P0207,      /* DIAG_INJECTOR_6     27    Diagnosi Iniettore 6                          */
    P0208,      /* DIAG_INJECTOR_7     28    Diagnosi Iniettore 7                          */   
#else
    NULL_DTC,   /* DIAG_INJECTOR_4     25    Diagnosi Iniettore 4                          */   
    NULL_DTC,   /* DIAG_INJECTOR_5     26    Diagnosi Iniettore 5                          */   
    NULL_DTC,   /* DIAG_INJECTOR_6     27    Diagnosi Iniettore 6                          */
    NULL_DTC,   /* DIAG_INJECTOR_7     28    Diagnosi Iniettore 7                          */   
#endif
    P0638,      /* DIAG_HBRIDGE_A      29    Diagnosis HBRIDGE_A                           */   
    P0639,      /* DIAG_HBRIDGE_B      30    Diagnosis HBRIDGE_B                           */   
    NULL_DTC,   /* DIAG_LOW_BEAM       31    Diagnosi attuatore high-lamp                  */   
    NULL_DTC,   /* DIAG_LSD_FANCOIL    32    Diagnosi Rel� Elettroventola                  */   
    P0138,      /* DIAG_LAM2_EL        33    Diagnosi elettrica sensore lambda 2           */
    P1136,      /* DIAG_O2H_FUNC_2     34    Diag lambda2 heater functional                */
    P0201,      /* DIAG_INJECTOR_0     35    Diagnosi Iniettore 0                          */   
    P0202,      /* DIAG_INJECTOR_1     36    Diagnosi Iniettore 1                          */   
    P0203,      /* DIAG_INJECTOR_2     37    Diagnosi Iniettore 2                          */   
    P0204,      /* DIAG_INJECTOR_3     38    Diagnosi Iniettore 3                          */   
    P0641,      /* DIAG_VSENS1         39    Diagnosi Vsens1                               */   
    P0651,      /* DIAG_VSENS2         40    Diagnosi Vsens1                               */   
    P0120,      /* DIAG_VANGTHR_1      41    Diagnosis VAngThrottle1                       */   
    P0121,      /* DIAG_VANGTHR_2      42    Diagnosis VAngThrottle2                       */   
    P1120,      /* DIAG_COH_VANGTHR    43    Diagnosi Coerenza VAngThrottle                */   
    P0220,      /* DIAG_VGASPOS_1      44    Diagnosis VGasPos1                            */   
    P0221,      /* DIAG_VGASPOS_2      45    Diagnosis VGasPos2                            */   
    P0222,      /* DIAG_VGASSWITCH     46    Diagnosis Gas Idle Switch                     */   
    P1222,      /* DIAG_COH_VGASPOS    47    Diagnosi Coerenza VGasPos                     */   
    P1220,      /* DIAG_DBW_CONTROL    48    Diagnosi controllo DBW                        */   
    P0501,      /* DIAG_VEHSPEED_FRONT 49    Diagnosi Velocit� ruota anteriore             */   
    P0351,      /* DIAG_COIL_0         50    Diagnosi Bobina 0                             */   
    P0352,      /* DIAG_COIL_1         51    Diagnosi Bobina 1                             */   
    P0353,      /* DIAG_COIL_2         52    Diagnosi Bobina 2                             */   
    P0354,      /* DIAG_COIL_3         53    Diagnosi Bobina 3                             */   
    P0105,      /* DIAG_V_MAPSIGNAL    54    Diagnosi VMapSignal                           */   
    P1602,      /* DIAG_ADC            55    Diagnosi ADC                                  */   
    P0612,      /* DIAG_DIS_LOADS      56    Diagnosi Dis Carichi                          */   
    P1612,      /* DIAG_SAFETY_2       57    Diagnosi SAFETY lvl 2                         */   
    P060F,      /* DIAG_TSD_TLE6244    58    Diagnosi overtemp TLE6244                     */ 
    P0420,      /* DIAG_CAT            59    Diagnosi catalizzatore                        */ 
    P0078,      /* DIAG_EXHVALVPOS     60    Diagnosi posizione ExhValve                   */
    P0106,      /* DIAG_PRES_NOT_COH   61    Diagnosi funzionale pressione                 */   
    P0101,      /* DIAG_HOLE_MANIFOLD  62    Diagnosi buco collettore                      */     
    P0335,      /* DIAG_RPM_SENS       63    Diagnosi sensore giri                         */     
    P0135,      /* DIAG_LSD_HLAMBDA    64    Diagnosi attuatore riscaldatore sonda lambda  */     
    P0141,      /* DIAG_LSD_HLAMBDA_2  65    Diagnosi attuatore riscaldatore sonda lambda 2*/
    U1701,      /* DIAG_VEH_CAN_NODE_1 66    Diagnosi CAN veicolo nodo 1 assente           */   
    U1706,      /* DIAG_VEH_CAN_NODE_2 67    Diagnosi CAN veicolo nodo 2 assente           */ 
    U1707,      /* DIAG_VEH_CAN_NODE_3 68    Diagnosi CAN veicolo nodo 3 assente           */ 
    P0605,      /* DIAG_FLASH          69    Diagnosi memoria flash                        */     
    P0601,      /* DIAG_EEPROM         70    Diagnosi memoria eeprom                       */   
    P0606,      /* DIAG_CPU            71    Diagnosi CPU                                  */     
    P0604,      /* DIAG_RAM            72    Diagnosi RAM                                  */     
    P1773,      /* DIAG_TIP_OVER       73    Diagnosi Tip Over sensor                      */   
    P0132,      /* DIAG_LAM_EL         74    Diagnosi elettrica sensore lambda             */   
    P0130,      /* DIAG_LAM_FUNC       75    Diagnosi funzionale sensore lambda            */   
    P0561,      /* DIAG_V_REG          76    Diagnosi regolatore di tensione               */   
    P1223,      /* DIAG_VGASSWCOH      77    Diagnosi coerenza idle switch                 */   
    P0704,      /* DIAG_CLUTCH         78    Diagnosi funzionale Frizione                  */ 
    P0500,      /* DIAG_VEHSPEED       79    Diagnosi funzionale Velocit� veicolo          */
    P0300,      /* DIAG_MISF_RND       80    Random/Multiple Cylinder Misfire Detected     */
    P1135,      /* DIAG_O2H_FUNC       81    Diagnosi O2H                                  */
    U0103,      /* DIAG_VEH_CAN_NODE_4 82    Diagnosi E-Gear                               */
    U0127,      /* DIAG_VEH_CAN_NODE_5 83    Diagnosi TPMS                                 */
    P0136,      /* DIAG_LAM_FUNC_2     84    Diagnosi funzionale sensore lambda 2          */
    P014C,      /* DIAG_O2_SLOW_R2L    85    O2 Sensor Slow Response - Rich to Lean        */
    P014D,      /* DIAG_O2_SLOW_L2R    86    O2 Sensor Slow Response - Rich to Lean        */
    U1001,      /* DIAG_VEH_CAN_NODE_6 87    Diagnosi CAN LHB                              */
    U1002,      /* DIAG_VEH_CAN_NODE_7 88    Diagnosi CAN RHB                              */
    U1003,      /* DIAG_VEH_CAN_NODE_8 89    Diagnosi CAN MCU                              */
    U0294,      /* DIAG_PRIVATE_CAN    90    Diagnosi CAN privato                          */
    U1004,      /* DIAG_VEH_CAN_NODE_9 91    Diagnosi CAN GPS                              */
};

const uint16_T DiagSplitDtcTab[SPLIT_DTC_NUMBER] =
{
    P0300,      /* DIAG_MISF_RND       80    Random/Multiple Cylinder Misfire Detected     */
    P0301,      /* DIAG_MISF_RND       80    Cylinder 1 Misfire Detected                   */
    P0302,      /* DIAG_MISF_RND       80    Cylinder 2 Misfire Detected                   */
    P0303,      /* DIAG_MISF_RND       80    Cylinder 3 Misfire Detected                   */
    P0304,      /* DIAG_MISF_RND       80    Cylinder 4 Misfire Detected                   */
};

uint16_T DiagToDtcTab[DIAG_NUMBER];

/* Externals */
extern DiagDataFaultStruct DiagDataFault[DIAG_FAULT_LENGTH];
extern uint8_T EventCounter[DIAG_FAULT_LENGTH];
extern uint8_T StoredFault[DIAG_FAULT_LENGTH];

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * readDTC_TroubleCode - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
int8_t readDTC_TroubleCode(void)
{
    uint16_t dtc;
    uint8_t i; 
    int8_t ret;


    dtc = (((T_DataIND.Data[1])<<8) + (T_DataIND.Data[2]));

    if (DiagMgm_isValidDiag(dtc) != 0)
    {
        ret = DTC_FOUND;

        T_DataREQ.Data[1] = 0;          
        T_DataREQ.Data[2] = T_DataIND.Data[1];
        T_DataREQ.Data[3] = T_DataIND.Data[2];
        T_DataREQ.DataLength = 2;

        for (i = 0; i < DIAG_FAULT_LENGTH; i++)
        {
            if (dtc == DiagDataFault[i].value)
            {
                T_DataREQ.Data[1] = 1;
                T_DataREQ.Data[4] = DiagDataFault[i].status;
                T_DataREQ.Data[5] = __KwpConv_Rpm(DiagDataFault[i].rpm);
                T_DataREQ.Data[6] = __KwpConv_MapSignal(DiagDataFault[i].mapSignal);
                T_DataREQ.Data[7] = __KwpConv_TWater(DiagDataFault[i].twater);
                T_DataREQ.Data[8] = __KwpConv_AngThrottle(DiagDataFault[i].angthrottle);
                T_DataREQ.Data[9] = __KwpConv_GasPos(DiagDataFault[i].gaspos);
                T_DataREQ.Data[10] = __KwpConv_SAout(DiagDataFault[i].saout);
                T_DataREQ.Data[11] = __KwpConv_Load(DiagDataFault[i].load);
                T_DataREQ.Data[12] = __KwpConv_ShortTermFuelTrim(DiagDataFault[i].shortttermfueltrim);
                T_DataREQ.Data[13] = __KwpConv_LongTermFuelTrim(DiagDataFault[i].longtermfueltrim);
                T_DataREQ.Data[14] = __KwpConv_VehSpeed(DiagDataFault[i].vehiclespeed);
                T_DataREQ.Data[15] = __KwpConv_StFuelSys(DiagDataFault[i].stfuelsys);
                T_DataREQ.Data[16] = __KwpConv_OdometerHi(DiagDataFault[i].odometer);
                T_DataREQ.Data[17] = __KwpConv_OdometerLo(DiagDataFault[i].odometer);
                T_DataREQ.Data[18] = EventCounter[i];
                T_DataREQ.DataLength = 19;
                break;         
            }
        }
    }
    else 
    {
        ret = DTC_NOT_FOUND;
        T_DataREQ.DataLength = T_DataIND.DataLength; 
    }

    return  ret;
}

/*--------------------------------------------------------------------------*
 * readDTC_TroubleCode - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
uint8_t readDTC_CodeStatus(uint8_t * data)
{
    uint8_t i;
    uint8_t nDtc = 0;
    
    for(i=0;i<DIAG_FAULT_LENGTH;i++)
    {
         if(DiagDataFault[i].value != NULL_DTC)
         {
             nDtc++;
             *(data + 2 + (nDtc-1)*3) = (uint8_t) (((DiagDataFault[i].value)>>8)&(0xff));
             *(data + 2 + (nDtc-1)*3 + 1) = (uint8_t) ((DiagDataFault[i].value)&(0xff));
             *(data + 2 + (nDtc-1)*3 + 2) = DiagDataFault[i].status;
         }
    }
    return nDtc;
}

void DTC_Init(void)
{
    uint8_T i;
    
    for (i = 0; i < DIAG_NUMBER; i++)
    {
        DiagToDtcTab[i] = DiagToDtcTabCfg[i];
    }
}

/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/

#else
/*stubs section*/
#include "typedefs.h"
#include "diagmgm_out.h"

const uint16_T DiagToDtcTab[DIAG_NUMBER];
uint8_t dtc_erased = 0;

int8_t readDTC_TroubleCode(void)
{
    
   
  return  0;
   
}

uint8_t readDTC_CodeStatus(uint8_t * data)
{
    
    return 0;

}



#endif/*_BUILD_DTC_*/
